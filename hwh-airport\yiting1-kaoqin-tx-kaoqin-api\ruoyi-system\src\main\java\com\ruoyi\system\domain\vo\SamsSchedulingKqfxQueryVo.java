package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 分析考勤报对象 sams_scheduling_kqfx
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
public class SamsSchedulingKqfxQueryVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场ID
     */
    @Excel(name = "机场ID")
    private Long airportId;

    /**
     * 区域ID
     */
    @Excel(name = "区域ID")
    private Long areaId;

    /**
     * 大队ID
     */
    @Excel(name = "大队ID")
    private Long groupId;

    /**
     * 小组ID
     */
    @Excel(name = "小组ID")
    private Long teamId;

    /**
     * 岗位ID
     */
    @Excel(name = "岗位ID")
    private Long postId;

    /**
     * 员工信息ID
     */
    @Excel(name = "员工信息ID")
    private Long employeeId;
    @Excel(name = "年月")
    private String cxny;
}
