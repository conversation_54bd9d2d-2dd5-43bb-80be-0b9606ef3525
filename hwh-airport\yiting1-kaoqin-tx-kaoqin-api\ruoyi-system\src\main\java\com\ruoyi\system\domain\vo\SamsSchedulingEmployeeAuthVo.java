package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 员工排班信息对象 sams_scheduling_employee
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
public class SamsSchedulingEmployeeAuthVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工排班ID
     */
    private Long schedulingEmployeeId;
    @Excel(name = "审核状态 null待审核，1审核通过，2审核拒绝")
    private Integer authStatus;
    @Excel(name = "审核备注")
    private String authRemark;
    @Excel(name = "考勤调整")
    private String kqtz;

}
