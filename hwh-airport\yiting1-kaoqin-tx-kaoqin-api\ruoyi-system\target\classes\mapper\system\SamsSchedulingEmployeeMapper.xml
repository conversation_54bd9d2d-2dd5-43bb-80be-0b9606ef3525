<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsSchedulingEmployeeMapper">
    <resultMap type="SamsSchedulingEmployee" id="SamsSchedulingEmployeeResult">
        <result property="schedulingEmployeeId" column="scheduling_employee_id"/>
        <result property="schedulingId" column="scheduling_id"/>
        <result property="projectEmployeeId" column="project_employee_id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="dutyDate" column="duty_date"/>
        <result property="postClock" column="post_clock"/>
        <result property="dkStatus" column="dk_status"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="dkAddress" column="dk_address"/>
        <result property="dkDate" column="dk_date"/>
        <result property="dkImage" column="dk_image"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="sfjb" column="sfjb"/>
        <result property="authTime" column="auth_time"/>
        <result property="authStatus" column="auth_status"/>
        <result property="authRemark" column="auth_remark"/>
        <result property="kqtz" column="kqtz"/>
        <result property="dkType" column="dk_type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="dkTag" column="dk_tag"/>

        <result property="nextDay" column="next_day"/>
        <result property="isAnytimeCheckIn" column="is_anytime_check_in"/>

        <result property="startTimeBefore" column="start_time_before"/>
        <result property="startTimeAfter" column="start_time_after"/>
        <result property="endTimeBefore" column="end_time_before"/>
        <result property="endTimeAfter" column="end_time_after"/>
        <result property="scheduleDate" column="schedule_date"/>

        <!--        // 排班信息-->
        <association property="samsScheduling" column="scheduling_id" javaType="SamsScheduling">
            <id property="schedulingId" column="scheduling_id"/>
            <result property="deptId" column="dept_id"/>
            <result property="airportId" column="airport_id"/>
            <result property="areaId" column="area_id"/>
            <result property="groupId" column="group_id"/>
            <result property="teamId" column="team_id"/>
            <result property="projectId" column="project_id"/>
            <result property="postId" column="post_id"/>
        </association>
    </resultMap>

    <sql id="selectSamsSchedulingEmployeeVo">
        select scheduling_employee_id,
               scheduling_id,
               sfjb,
               project_employee_id,
               employee_id,
               duty_date,
               post_clock,
               dk_status,
               latitude,
               longitude,
               dk_address,
               dk_tag,
               dk_date,
               dk_image,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               auth_time,
               auth_status,
               auth_remark,
               kqtz,
               dk_type,
               start_time,
               end_time,
               next_day,
               is_anytime_check_in,
               start_time_before,
               start_time_after,
               end_time_before,
               end_time_after,
               schedule_date
        from sams_scheduling_employee
    </sql>

    <select id="selectSamsSchedulingEmployeeList" parameterType="SamsSchedulingEmployee"
            resultMap="SamsSchedulingEmployeeResult">
        select se.*, s.dept_id, s.airport_id, s.area_id, s.group_id, s.team_id, s.project_id, s.post_id, s.area_id from
        sams_employee as e left join sams_scheduling_employee as se on se.employee_id = e.employee_id
        left join sams_scheduling as s on se.scheduling_id = s.scheduling_id
        <where>
            <if test="schedulingId != null ">
                and se.scheduling_id = #{schedulingId}
            </if>
            <if test="projectEmployeeId != null ">
                and se.project_employee_id = #{projectEmployeeId}
            </if>
            <if test="employeeId != null ">
                and se.employee_id = #{employeeId}
            </if>
            <if test="employeeName != null and employeeName != '' ">
                and e.name like concat('%', #{employeeName}, '%')
            </if>
            <if test="dutyDate != null ">
                and se.duty_date = #{dutyDate}
            </if>
            <choose>
                <when test="queryType == 2">
                    and se.dk_status in (2, 3, 5, 6, 7)
                </when>
                <otherwise>
                    <if test="dkStatus != null ">
                        and se.dk_status = #{dkStatus}
                    </if>
                </otherwise>
            </choose>
            <if test="authStatus != null ">
                and se.auth_status = #{authStatus}
            </if>
            <if test="latitude != null  and latitude != ''">
                and se.latitude = #{latitude}
            </if>
            <if test="longitude != null  and longitude != ''">
                and se.longitude = #{longitude}
            </if>
            <if test="dkAddress != null  and dkAddress != ''">
                and se.dk_address = #{dkAddress}
            </if>
            <if test="dkDate != null ">
                and date(se.dk_date) = date(#{dkDate})
            </if>
            <if test="mobilePhone != null  and mobilePhone != ''">
                and e.mobile_phone = #{mobilePhone}
            </if>

            <if test="deptId != null ">
                and s.dept_id = #{deptId}
            </if>
            <if test="airportId != null ">
                and s.airport_id = #{airportId}
            </if>
            <if test="groupId != null ">
                and s.group_id = #{groupId}
            </if>
            <if test="teamId != null ">
                and s.team_id = #{teamId}
            </if>
            <if test="projectId != null ">
                and s.project_id = #{projectId}
            </if>
            <if test="postId != null ">
                and s.post_id = #{postId}
            </if>
            <if test="areaId != null ">
                and s.area_id = #{areaId}
            </if>

            and se.dk_status is not null
        </where>
        order by se.dk_date desc
    </select>

    <select id="selectByEmployeeId" parameterType="long" resultMap="SamsSchedulingEmployeeResult">
        <include refid="selectSamsSchedulingEmployeeVo"/>
        <where>
            <if test="employeeId != null ">
                and employee_id = #{employeeId}
            </if>
            and DATE(duty_date) = CURDATE() - INTERVAL 1 DAY
        </where>
        order by dk_date asc
    </select>

    <select id="selectByEmployeeId2" parameterType="long" resultMap="SamsSchedulingEmployeeResult">
        SELECT
        sse.scheduling_employee_id,
        sse.scheduling_id,
        sse.sfjb,
        sse.project_employee_id,
        sse.employee_id,
        sse.duty_date,
        sse.post_clock,
        sse.dk_status,
        sse.latitude,
        sse.longitude,
        sse.dk_address,
        sse.dk_date,
        sse.dk_image,
        sse.del_flag,
        sse.create_by,
        sse.create_time,
        sse.update_by,
        sse.update_time,
        sse.remark,
        sse.auth_time,
        sse.auth_status,
        sse.auth_remark,
        sse.kqtz,
        sse.dk_type,
        sse.start_time,
        sse.end_time,
        sse.dk_tag,
        ss.post_id,
        sse.next_day,
        sse.is_anytime_check_in,
        sse.start_time_before,
        sse.start_time_after,
        sse.end_time_before,
        sse.end_time_after,
        sse.schedule_date

        FROM
        sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        <where>
            <if test="employeeId != null ">
                and sse.employee_id = #{employeeId}
            </if>
            <if test="postId != null ">
                and ss.post_id = #{postId}
            </if>
            <if test="schedulingId != null ">
                and sse.scheduling_id= #{schedulingId}
            </if>
            -- 修复跨天问题：考虑跨天班次的情况
            and (
                DATE(sse.duty_date) = CURDATE() - INTERVAL 1 DAY
                OR (sse.next_day = 1 AND DATE(sse.duty_date) = CURDATE() - INTERVAL 2 DAY)
            )
        </where>
        order by dk_date asc
    </select>

    <select id="selectByEmployeeId2Day" parameterType="long" resultMap="SamsSchedulingEmployeeResult">
        SELECT
        sse.scheduling_employee_id,
        sse.scheduling_id,
        sse.sfjb,
        sse.project_employee_id,
        sse.employee_id,
        sse.duty_date,
        sse.post_clock,
        sse.dk_status,
        sse.latitude,
        sse.longitude,
        sse.dk_address,
        sse.dk_date,
        sse.dk_image,
        sse.del_flag,
        sse.create_by,
        sse.create_time,
        sse.update_by,
        sse.update_time,
        sse.remark,
        sse.auth_time,
        sse.auth_status,
        sse.auth_remark,
        sse.kqtz,
        sse.dk_type,
        sse.start_time,
        sse.end_time,
        sse.dk_tag,
        ss.post_id,
        sse.next_day,
        sse.is_anytime_check_in,
        sse.start_time_before,
        sse.start_time_after,
        sse.end_time_before,
        sse.end_time_after,
        sse.schedule_date

        FROM
        sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        <where>
            <if test="employeeId != null ">
                and sse.employee_id = #{employeeId}
            </if>
            <if test="postId != null ">
                and ss.post_id = #{postId}
            </if>
            <if test="schedulingId != null ">
                and sse.scheduling_id= #{schedulingId}
            </if>
            and DATE(sse.duty_date) between (CURDATE() - INTERVAL 2 DAY) and (CURDATE() - INTERVAL 1 DAY)
        </where>
        order by dk_date asc
    </select>

    <select id="selectByEmployeeId4" parameterType="long" resultMap="SamsSchedulingEmployeeResult">
        SELECT
        sse.scheduling_employee_id,
        sse.scheduling_id,
        sse.sfjb,
        sse.project_employee_id,
        sse.employee_id,
        sse.duty_date,
        sse.post_clock,
        sse.dk_status,
        sse.latitude,
        sse.longitude,
        sse.dk_address,
        sse.dk_date,
        sse.dk_image,
        sse.del_flag,
        sse.create_by,
        sse.create_time,
        sse.update_by,
        sse.update_time,
        sse.remark,
        sse.auth_time,
        sse.auth_status,
        sse.auth_remark,
        sse.kqtz,
        sse.dk_type,
        sse.start_time,
        sse.end_time,
        sse.dk_tag,
        ss.post_id,
        sse.next_day,
        sse.is_anytime_check_in,
        sse.start_time_before,
        sse.start_time_after,
        sse.end_time_before,
        sse.end_time_after,
        sse.schedule_date
        FROM
        sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        <where>
            <if test="employeeId != null ">
                and sse.employee_id = #{employeeId}
            </if>
            <if test="postId != null ">
                and ss.post_id = #{postId}
            </if>
            <if test="schedulingId != null ">
                and sse.scheduling_id= #{schedulingId}
            </if>
            and DATE(sse.duty_date) = CURDATE() - INTERVAL 1 DAY
        </where>
    </select>


    <select id="selectYestDayKqsj" resultType="long">
        select distinct(employee_id)
        from sams_scheduling_employee
        where DATE (duty_date) = CURDATE() - INTERVAL 1 DAY
    </select>

    <select id="selectYestDayKqsj2" resultType="com.ruoyi.system.domain.vo.SamsSchedulingPostEmployeeVo">
        SELECT DISTINCT sse.employee_id as employeeId, ss.post_id as postId, sse.scheduling_id as schedulingId
        FROM sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        WHERE
            -- 修复跨天问题：查询前一天和前两天的数据，覆盖跨天班次
            DATE ( sse.duty_date ) BETWEEN CURDATE() - INTERVAL 2 DAY AND CURDATE() - INTERVAL 1 DAY
            -- 或者查询结束时间在前一天的班次
            OR (sse.next_day = 1 AND DATE ( sse.duty_date ) = CURDATE() - INTERVAL 2 DAY)
    </select>

    <select id="selectYestDayKqsj2Day" resultType="com.ruoyi.system.domain.vo.SamsSchedulingPostEmployeeVo">
        SELECT DISTINCT sse.employee_id as employeeId, ss.post_id as postId, sse.scheduling_id as schedulingId
        FROM sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        WHERE DATE (sse.duty_date) between (CURDATE() - INTERVAL 2 DAY) and (CURDATE() - INTERVAL 1 DAY)
    </select>

    <select id="tjbcData" resultType="int">
        select count(distinct scheduling_id)
        from sams_scheduling_employee
        where employee_id = #{employeeId}
          and DATE_FORMAT(duty_date, '%Y-%m') = #{cxny}
    </select>

    <select id="mykqdk" parameterType="com.ruoyi.system.domain.vo.CxkqVo" resultMap="SamsSchedulingEmployeeResult">
        select se.* from sams_scheduling_employee as se join sams_project_employee as pe on se.project_employee_id =
        pe.project_employee_id
        join sams_project p on pe.project_id = p.project_id
        where 1=1
        <if test="projectId != null ">
            and pe.project_id = #{projectId}
        </if>
        and pe.employee_id = #{employeeId} and DATE_FORMAT(se.duty_date,'%Y-%m-%d') = #{cxyf}
        <!-- and date(se.duty_date) <![CDATA[ <= ]]>date(now()) -->
        order by se.duty_date desc
    </select>

    <select id="selectBySchedulingId" parameterType="long" resultMap="SamsSchedulingEmployeeResult">
        <include refid="selectSamsSchedulingEmployeeVo"/>
        <where>
            <if test="schedulingId != null ">
                and scheduling_id = #{schedulingId}
            </if>
            and date(duty_date) <![CDATA[ > ]]> date(now())
        </where>
        order by employee_id asc, duty_date asc
    </select>


    <select id="selectSamsSchedulingEmployeeBySchedulingEmployeeId" parameterType="Long"
            resultMap="SamsSchedulingEmployeeResult">
        <include refid="selectSamsSchedulingEmployeeVo"/>
        where scheduling_employee_id = #{schedulingEmployeeId}
    </select>

    <insert id="insertSamsSchedulingEmployee" parameterType="SamsSchedulingEmployee" useGeneratedKeys="true"
            keyProperty="schedulingEmployeeId">
        insert into sams_scheduling_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="schedulingId != null">
                scheduling_id,
            </if>
            <if test="projectEmployeeId != null">
                project_employee_id,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="dutyDate != null">
                duty_date,
            </if>
            <if test="postClock != null">
                post_clock,
            </if>
            <if test="dkStatus != null">
                dk_status,
            </if>
            <if test="latitude != null">
                latitude,
            </if>
            <if test="longitude != null">
                longitude,
            </if>
            <if test="dkAddress != null">
                dk_address,
            </if>
            <if test="dkDate != null">
                dk_date,
            </if>
            <if test="dkImage != null">
                dk_image,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="sfjb != null">
                sfjb,
            </if>
            <if test="authTime != null">
                auth_time,
            </if>
            <if test="authStatus != null">
                auth_status,
            </if>
            <if test="authRemark != null">
                auth_remark,
            </if>
            <if test="kqtz != null">
                kqtz,
            </if>
            <if test="dkType != null">
                dk_type,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="dkTag != null">
                dk_tag,
            </if>
            <if test="nextDay != null">
                next_day,
            </if>
            <if test="isAnytimeCheckIn != null">
                is_anytime_check_in,
            </if>
            <if test="startTimeBefore != null">
                start_time_before,
            </if>
            <if test="startTimeAfter != null">
                start_time_after,
            </if>
            <if test="endTimeBefore != null">
                end_time_before,
            </if>
            <if test="endTimeAfter != null">
                end_time_after,
            </if>
            <if test="scheduleDate != null">
                schedule_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="schedulingId != null">
                #{schedulingId},
            </if>
            <if test="projectEmployeeId != null">
                #{projectEmployeeId},
            </if>
            <if test="employeeId != null">
                #{employeeId},
            </if>
            <if test="dutyDate != null">
                #{dutyDate},
            </if>
            <if test="postClock != null">
                #{postClock},
            </if>
            <if test="dkStatus != null">
                #{dkStatus},
            </if>
            <if test="latitude != null">
                #{latitude},
            </if>
            <if test="longitude != null">
                #{longitude},
            </if>
            <if test="dkAddress != null">
                #{dkAddress},
            </if>
            <if test="dkDate != null">
                #{dkDate},
            </if>
            <if test="dkImage != null">
                #{dkImage},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="sfjb != null">
                #{sfjb},
            </if>
            <if test="authTime != null">
                #{authTime},
            </if>
            <if test="authStatus != null">
                #{authStatus},
            </if>
            <if test="authRemark != null">
                #{authRemark},
            </if>
            <if test="kqtz != null">
                #{kqtz},
            </if>
            <if test="dkType != null">
                #{dkType},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="dkTag != null">
                #{dkTag},
            </if>
            <if test="nextDay != null">
                #{nextDay},
            </if>
            <if test="isAnytimeCheckIn != null">
                #{isAnytimeCheckIn},
            </if>
            <if test="startTimeBefore != null">
                #{startTimeBefore},
            </if>
            <if test="startTimeAfter != null">
                #{startTimeAfter},
            </if>
            <if test="endTimeBefore != null">
                #{endTimeBefore},
            </if>
            <if test="endTimeAfter != null">
                #{endTimeAfter},
            </if>
            <if test="scheduleDate != null">
                #{scheduleDate},
            </if>
        </trim>
    </insert>

    <update id="updateSamsSchedulingEmployee" parameterType="SamsSchedulingEmployee">
        update sams_scheduling_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="schedulingId != null">
                scheduling_id = #{schedulingId},
            </if>
            <if test="projectEmployeeId != null">
                project_employee_id = #{projectEmployeeId},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId},
            </if>
            <if test="dutyDate != null">
                duty_date = #{dutyDate},
            </if>
            <if test="postClock != null">
                post_clock = #{postClock},
            </if>
            <if test="dkStatus != null">
                dk_status = #{dkStatus},
            </if>
            <if test="latitude != null">
                latitude = #{latitude},
            </if>
            <if test="longitude != null">
                longitude = #{longitude},
            </if>
            <if test="dkAddress != null">
                dk_address = #{dkAddress},
            </if>
            <if test="dkDate != null">
                dk_date = #{dkDate},
            </if>
            <if test="dkImage != null">
                dk_image = #{dkImage},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="sfjb != null">
                sfjb = #{sfjb},
            </if>
            <if test="authTime != null">
                auth_time = #{authTime},
            </if>
            <if test="authStatus != null">
                auth_status = #{authStatus},
            </if>
            <if test="authRemark != null">
                auth_remark = #{authRemark},
            </if>
            <if test="kqtz != null">
                kqtz = #{kqtz},
            </if>
            <if test="dkType != null">
                dk_type = #{dkType},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="dkTag != null">
                dk_tag= #{dkTag},
            </if>
            <if test="nextDay != null">
                next_day= #{nextDay},
            </if>
            <if test="isAnytimeCheckIn != null">
                is_anytime_check_in= #{isAnytimeCheckIn},
            </if>
            <if test="startTimeBefore != null">
                start_time_before= #{startTimeBefore},
            </if>
            <if test="startTimeAfter != null">
                start_time_after= #{startTimeAfter},
            </if>
            <if test="endTimeBefore != null">
                end_time_before= #{endTimeBefore},
            </if>
            <if test="endTimeAfter != null">
                end_time_after= #{endTimeAfter},
            </if>
            <if test="scheduleDate != null">
                schedule_date= #{scheduleDate},
            </if>
        </trim>
        where scheduling_employee_id = #{schedulingEmployeeId}
    </update>

    <delete id="deleteSamsSchedulingEmployeeBySchedulingEmployeeId" parameterType="Long">
        delete
        from sams_scheduling_employee
        where scheduling_employee_id = #{schedulingEmployeeId}
    </delete>

    <delete id="deleteSamsSchedulingEmployeeBySchedulingIdAndAfterDutyDate">
        delete
        from sams_scheduling_employee
        where scheduling_id = #{schedulingId}
          and date (duty_date) <![CDATA[ >= ]]> date (#{afterDutyDate})
    </delete>



    <delete id="deleteSamsSchedulingEmployeeBySchedulingEmployeeIds" parameterType="String">
        delete from sams_scheduling_employee where scheduling_employee_id in
        <foreach item="schedulingEmployeeId" collection="array" open="(" separator="," close=")">
            #{schedulingEmployeeId}
        </foreach>
    </delete>

    <select id="selectSchedulingIdByDutyDate" resultType="java.lang.Long">
        select scheduling_id
        from sams_scheduling_employee
        where duty_date = #{dutyDate}
    </select>
    <select id="selectByDkTag" resultMap="SamsSchedulingEmployeeResult">
        SELECT
        sse.scheduling_employee_id,
        sse.scheduling_id,
        sse.sfjb,
        sse.project_employee_id,
        sse.employee_id,
        sse.duty_date,
        sse.post_clock,
        sse.dk_status,
        sse.latitude,
        sse.longitude,
        sse.dk_address,
        sse.dk_date,
        sse.dk_image,
        sse.del_flag,
        sse.create_by,
        sse.create_time,
        sse.update_by,
        sse.update_time,
        sse.remark,
        sse.auth_time,
        sse.auth_status,
        sse.auth_remark,
        sse.kqtz,
        sse.dk_type,
        sse.start_time,
        sse.end_time,
        sse.dk_tag,
        ss.post_id,
        sse.next_day,
        sse.is_anytime_check_in,
        sse.start_time_before,
        sse.start_time_after,
        sse.end_time_before,
        sse.end_time_after,
        sse.schedule_date
        FROM
        sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        <where>
            <if test="dkTag != null ">
                and sse.dk_tag = #{dkTag}
            </if>
        </where>
        order by dk_date asc
    </select>

    <delete id="deleteSamsSchedulingEmployeeBySchedulingIds" parameterType="long">
        delete from sams_scheduling_employee where scheduling_id in
        <foreach item="schedulingId" collection="schedulingIds" open="(" separator="," close=")">
            #{schedulingId}
        </foreach>
    </delete>

    <delete id="deleteSamsSchedulingEmployeeBySchedulingIdAndDutyDate">
        delete
        from sams_scheduling_employee
        where scheduling_id = #{schedulingId}
          and duty_date = #{dutyDate}
    </delete>

    <delete id="deleteSamsSchedulingEmployeeOffWorkBySchedulingIdAndDutyDate">
        delete
        e from sams_scheduling_employee e
        join (
            select * from sams_scheduling_employee where scheduling_id =
        #{schedulingId}
        and
        duty_date <![CDATA[ > ]]> #{dutyDate}
        group
        by
        dk_tag
        having
        count
        (
        dk_tag
        ) <![CDATA[ > ]]> 1
        )
        dup
        on
        e
        .
        dk_tag
        =
        dup
        .
        dk_tag
    </delete>

    <select id="selectSamsSchedulingEmployeeOffWorkBySchedulingIdAndDutyDate" resultMap="SamsSchedulingEmployeeResult">
        select *
        from sams_scheduling_employee e
                 join (select *
                       from sams_scheduling_employee
                       where scheduling_id = #{schedulingId}
                         and duty_date <![CDATA[ >= ]]> #{dutyDate}
                       group by dk_tag
                       having count(dk_tag) <![CDATA[ > ]]> 1) dup on e.dk_tag = dup.dk_tag
    </select>

    <select id="selectSamsSchedulingEmployeeList2" parameterType="SamsSchedulingEmployee"
            resultMap="SamsSchedulingEmployeeResult">
        select se.* from sams_employee as e left join sams_scheduling_employee as se on se.employee_id = e.employee_id
        <where>
            <if test="schedulingId != null ">
                and se.scheduling_id = #{schedulingId}
            </if>
            <if test="projectEmployeeId != null ">
                and se.project_employee_id = #{projectEmployeeId}
            </if>
            <if test="employeeId != null ">
                and se.employee_id = #{employeeId}
            </if>
            <if test="employeeName != null and employeeName != '' ">
                and e.name like concat('%', #{employeeName}, '%')
            </if>
            <if test="dutyDate != null ">
                and se.duty_date = #{dutyDate}
            </if>
            <choose>
                <when test="queryType == 2">
                    and se.dk_status in (2, 3, 5, 6, 7)
                </when>
                <otherwise>
                    <if test="dkStatus != null ">
                        and se.dk_status = #{dkStatus}
                    </if>
                </otherwise>
            </choose>
            <if test="authStatus != null ">
                and se.auth_status = #{authStatus}
            </if>
            <if test="latitude != null  and latitude != ''">
                and se.latitude = #{latitude}
            </if>
            <if test="longitude != null  and longitude != ''">
                and se.longitude = #{longitude}
            </if>
            <if test="dkAddress != null  and dkAddress != ''">
                and se.dk_address = #{dkAddress}
            </if>
            <if test="dkDate != null ">
                and date(se.dk_date) = date(#{dkDate})
            </if>
            <if test="mobilePhone != null  and mobilePhone != ''">
                and e.mobile_phone = #{mobilePhone}
            </if>
        </where>
        order by se.dk_date desc
    </select>
    <select id="selectBySchedulingIdTask" resultType="com.ruoyi.system.domain.SamsSchedulingEmployee">
        <include refid="selectSamsSchedulingEmployeeVo"/>
        <where>
            <if test="schedulingId != null ">
                and scheduling_id = #{schedulingId}
            </if>
            and date(duty_date) <![CDATA[ <= ]]> date(now())
        </where>
        order by employee_id asc, duty_date asc
    </select>

    <select id="selectByEmployeeId3" parameterType="long" resultMap="SamsSchedulingEmployeeResult">
        SELECT
        sse.scheduling_employee_id,
        sse.scheduling_id,
        sse.sfjb,
        sse.project_employee_id,
        sse.employee_id,
        sse.duty_date,
        sse.post_clock,
        sse.dk_status,
        sse.latitude,
        sse.longitude,
        sse.dk_address,
        sse.dk_date,
        sse.dk_image,
        sse.del_flag,
        sse.create_by,
        sse.create_time,
        sse.update_by,
        sse.update_time,
        sse.remark,
        sse.auth_time,
        sse.auth_status,
        sse.auth_remark,
        sse.kqtz,
        sse.dk_type,
        sse.start_time,
        sse.end_time,
        sse.dk_tag,
        ss.post_id,
        sse.next_day,
        sse.is_anytime_check_in,
        sse.start_time_before,
        sse.start_time_after,
        sse.end_time_before,
        sse.end_time_after,
        sse.schedule_date

        FROM
        sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        <where>
            <if test="employeeId != null ">
                and sse.employee_id = #{employeeId}
            </if>
            <if test="postId != null ">
                and ss.post_id = #{postId}
            </if>
            <if test="dutyDate != null ">
                and sse.duty_date = #{dutyDate}
            </if>
        </where>
    </select>

    <select id="selectBySchedulingIdAndAfterDutyDate" resultType="java.lang.String">
        select
        distinct dk_tag
        from
        sams_scheduling_employee
        where
        scheduling_id = #{schedulingId}
        and
        duty_date <![CDATA[ = ]]> DATE_SUB(DATE (#{afterDutyDate}), INTERVAL 1 DAY);
    </select>

    <delete id="deleteBySchedulingIdAndAfterDutyDate">
        delete
        from
        sams_scheduling_employee
        where
        scheduling_id = #{schedulingId}
        and
        duty_date <![CDATA[ >= ]]> date (#{afterDutyDate})
        <if test="dkTags != null and dkTags.size() > 0">
            and
            dk_tag not in
            <foreach collection="dkTags" item="dkTag" open="(" separator="," close=")">
                #{dkTag}
            </foreach>
        </if>
    </delete>

    <delete id="deleteSamsSchedulingEmployeeBySchedulingIdAndScheduleDate">
        delete
        from sams_scheduling_employee
        where scheduling_id = #{schedulingId}
          and schedule_date = #{dutyDate}
    </delete>

    <select id="selectAttendanceDataByMonth" parameterType="String" resultType="com.ruoyi.system.domain.vo.SamsSchedulingPostEmployeeVo">
        SELECT DISTINCT sse.employee_id as employeeId, ss.post_id as postId, sse.scheduling_id as schedulingId
        FROM sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        WHERE
            DATE_FORMAT(sse.duty_date, '%Y-%m') = #{yearMonth}
            OR (sse.next_day = 1 AND DATE_FORMAT(sse.duty_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{yearMonth}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m'))
    </select>

    <select id="selectEmployeeAttendanceByMonth" resultMap="SamsSchedulingEmployeeResult">
        SELECT
        sse.scheduling_employee_id,
        sse.scheduling_id,
        sse.sfjb,
        sse.project_employee_id,
        sse.employee_id,
        sse.duty_date,
        sse.post_clock,
        sse.dk_status,
        sse.latitude,
        sse.longitude,
        sse.dk_address,
        sse.dk_date,
        sse.dk_image,
        sse.del_flag,
        sse.create_by,
        sse.create_time,
        sse.update_by,
        sse.update_time,
        sse.remark,
        sse.auth_time,
        sse.auth_status,
        sse.auth_remark,
        sse.kqtz,
        sse.dk_type,
        sse.start_time,
        sse.end_time,
        sse.dk_tag,
        ss.post_id,
        sse.next_day,
        sse.is_anytime_check_in,
        sse.start_time_before,
        sse.start_time_after,
        sse.end_time_before,
        sse.end_time_after,
        sse.schedule_date

        FROM
        sams_scheduling_employee sse
        left join sams_scheduling ss on sse.scheduling_id = ss.scheduling_id
        WHERE
            sse.employee_id = #{employeeId}
            AND ss.post_id = #{postId}
            AND (
                DATE_FORMAT(sse.duty_date, '%Y-%m') = #{yearMonth}
                OR (sse.next_day = 1 AND DATE_FORMAT(sse.duty_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(STR_TO_DATE(CONCAT(#{yearMonth}, '-01'), '%Y-%m-%d'), INTERVAL 1 MONTH), '%Y-%m'))
            )
        order by sse.duty_date asc, sse.dk_type asc
    </select>
</mapper>
