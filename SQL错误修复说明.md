# SQL错误修复说明

## 🚨 问题描述

在运行应用时遇到以下SQL错误：

```
### Error querying database. Cause: java.sql.SQLSyntaxErrorException: Unknown column 'spe.project_id' in 'where clause'
### The error may exist in URL [jar:file:/usr/local/services/admin/airport-admin.jar!/BOOT-INF/lib/ruoyi-system-3.8.7.jar!/mapper/system/SamsSchedulingKqfxMapper.xml]
### The error may involve com.ruoyi.system.mapper.SamsSchedulingKqfxMapper.selectSamsSchedulingKqfxList-Inline
### SQL: SELECT count(0) FROM sams_scheduling_kqfx AS sk JOIN sams_employee AS se ON sk.employee_id = se.employee_id LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id LEFT JOIN sams_airport sa ON sk.airport_id = sa.airport_id LEFT JOIN sams_airport_area saa ON sk.area_id = saa.area_id LEFT JOIN sams_airport_team sat ON sk.team_id = sat.team_id LEFT JOIN sams_airport_group sag ON sk.group_id = sag.group_id LEFT JOIN sams_post sp ON sk.post_id = sp.post_id WHERE sk.dept_id = ? AND sk.airport_id = ? AND sk.area_id = ? AND spe.project_id = ?
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'spe.project_id' in 'where clause'
```

## 🔍 问题分析

### 根本原因
在 `SamsSchedulingKqfxMapper.xml` 文件中：

1. **第127行**：`sams_project_employee` 表的JOIN语句被注释掉了
   ```xml
   -- left join sams_project_employee spe on sk.employee_id = spe.employee_id
   ```

2. **第154行**：WHERE条件中仍然引用了 `spe.project_id`
   ```xml
   <if test="projectId != null ">
       and spe.project_id = #{projectId}
   </if>
   ```

3. **结果**：SQL中使用了未定义的表别名 `spe`，导致语法错误

### 影响范围
- `selectSamsSchedulingKqfxList` 方法
- `selectSamsSchedulingKqfxVo` SQL片段（被其他查询引用）

## ✅ 修复方案

### 1. 恢复JOIN语句
将被注释的JOIN语句恢复：

**修复前**：
```xml
join sams_employee as se on sk.employee_id = se.employee_id
-- left join sams_project_employee spe on sk.employee_id = spe.employee_id
LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id
```

**修复后**：
```xml
join sams_employee as se on sk.employee_id = se.employee_id
left join sams_project_employee spe on sk.employee_id = spe.employee_id
LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id
```

### 2. 修复SQL片段
在 `selectSamsSchedulingKqfxVo` 中也添加相同的JOIN：

**修复前**：
```xml
from sams_scheduling_kqfx sk
         join sams_employee as se on sk.employee_id = se.employee_id
         LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id
```

**修复后**：
```xml
from sams_scheduling_kqfx sk
         join sams_employee as se on sk.employee_id = se.employee_id
         left join sams_project_employee spe on sk.employee_id = spe.employee_id
         LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id
```

## 📋 修复详情

### 修复的文件
- `SamsSchedulingKqfxMapper.xml`

### 修复的位置
1. **第127行**：恢复 `sams_project_employee` 表的JOIN
2. **第105行**：在SQL片段中添加 `sams_project_employee` 表的JOIN

### 修复的方法
- `selectSamsSchedulingKqfxList` - 主要的考勤分析列表查询
- `selectSamsSchedulingKqfxVo` - 被其他查询引用的SQL片段

## 🔧 技术细节

### JOIN关系说明
```sql
left join sams_project_employee spe on sk.employee_id = spe.employee_id
```

这个JOIN的作用：
- 连接考勤分析表 (`sams_scheduling_kqfx`) 和项目员工关系表 (`sams_project_employee`)
- 通过员工ID建立关联
- 使用LEFT JOIN确保即使员工没有项目关联也能查询到考勤数据
- 支持按项目ID过滤考勤分析数据

### WHERE条件
```xml
<if test="projectId != null ">
    and spe.project_id = #{projectId}
</if>
```

这个条件的作用：
- 当传入项目ID参数时，过滤出指定项目的考勤分析数据
- 支持按项目维度查看考勤统计

## ✨ 验证结果

修复后的SQL应该能够正常执行：

```sql
SELECT count(0) 
FROM sams_scheduling_kqfx AS sk 
JOIN sams_employee AS se ON sk.employee_id = se.employee_id 
LEFT JOIN sams_project_employee spe ON sk.employee_id = spe.employee_id  -- 修复：添加了这个JOIN
LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id 
LEFT JOIN sams_airport sa ON sk.airport_id = sa.airport_id 
LEFT JOIN sams_airport_area saa ON sk.area_id = saa.area_id 
LEFT JOIN sams_airport_team sat ON sk.team_id = sat.team_id 
LEFT JOIN sams_airport_group sag ON sk.group_id = sag.group_id 
LEFT JOIN sams_post sp ON sk.post_id = sp.post_id 
WHERE sk.dept_id = ? 
AND sk.airport_id = ? 
AND sk.area_id = ? 
AND spe.project_id = ?  -- 现在这个条件可以正常工作了
```

## 🚀 后续建议

1. **测试验证**：在测试环境验证修复效果
2. **代码审查**：检查其他Mapper文件是否有类似问题
3. **文档更新**：更新相关的数据库设计文档
4. **监控告警**：添加SQL错误的监控告警

## 📝 总结

这是一个典型的SQL语法错误，由于JOIN语句被注释但WHERE条件仍然引用表别名导致。修复方法是恢复必要的JOIN语句，确保SQL语法的完整性和正确性。

**关键教训**：
- 修改SQL时要保持JOIN和WHERE条件的一致性
- 注释JOIN语句前要检查是否有WHERE条件依赖
- 使用表别名时要确保对应的表已经在FROM子句中定义
