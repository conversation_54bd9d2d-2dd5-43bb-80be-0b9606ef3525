package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SamsSupplierMapper;
import com.ruoyi.system.domain.SamsSupplier;
import com.ruoyi.system.service.ISamsSupplierService;

/**
 * 供应商信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsSupplierServiceImpl implements ISamsSupplierService 
{
    @Autowired
    private SamsSupplierMapper samsSupplierMapper;

    /**
     * 查询供应商信息
     * 
     * @param supplierId 供应商信息主键
     * @return 供应商信息
     */
    @Override
    public SamsSupplier selectSamsSupplierBySupplierId(Long supplierId)
    {
        return samsSupplierMapper.selectSamsSupplierBySupplierId(supplierId);
    }

    /**
     * 查询供应商信息列表
     * 
     * @param samsSupplier 供应商信息
     * @return 供应商信息
     */
    @Override
    public List<SamsSupplier> selectSamsSupplierList(SamsSupplier samsSupplier)
    {
        return samsSupplierMapper.selectSamsSupplierList(samsSupplier);
    }

    /**
     * 新增供应商信息
     * 
     * @param samsSupplier 供应商信息
     * @return 结果
     */
    @Override
    public int insertSamsSupplier(SamsSupplier samsSupplier)
    {
        samsSupplier.setCreateTime(DateUtils.getNowDate());
        return samsSupplierMapper.insertSamsSupplier(samsSupplier);
    }

    /**
     * 修改供应商信息
     * 
     * @param samsSupplier 供应商信息
     * @return 结果
     */
    @Override
    public int updateSamsSupplier(SamsSupplier samsSupplier)
    {
        samsSupplier.setUpdateTime(DateUtils.getNowDate());
        return samsSupplierMapper.updateSamsSupplier(samsSupplier);
    }

    /**
     * 批量删除供应商信息
     * 
     * @param supplierIds 需要删除的供应商信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSupplierBySupplierIds(Long[] supplierIds)
    {
        return samsSupplierMapper.deleteSamsSupplierBySupplierIds(supplierIds);
    }

    /**
     * 删除供应商信息信息
     * 
     * @param supplierId 供应商信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSupplierBySupplierId(Long supplierId)
    {
        return samsSupplierMapper.deleteSamsSupplierBySupplierId(supplierId);
    }
}
