package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SamsAirportGroup;
import com.ruoyi.system.service.ISamsAirportGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 机场大队信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/airportGroup")
public class SamsAirportGroupController extends BaseController
{
    @Autowired
    private ISamsAirportGroupService samsAirportGroupService;

    /**
     * 查询机场大队信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirportGroup samsAirportGroup)
    {
        startPage();
        List<SamsAirportGroup> list = samsAirportGroupService.selectSamsAirportGroupList(samsAirportGroup);
        return getDataTable(list);
    }

    /**
     * 导出机场大队信息列表
     */
    @Log(title = "机场大队信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsAirportGroup samsAirportGroup)
    {
        List<SamsAirportGroup> list = samsAirportGroupService.selectSamsAirportGroupList(samsAirportGroup);
        ExcelUtil<SamsAirportGroup> util = new ExcelUtil<SamsAirportGroup>(SamsAirportGroup.class);
        util.exportExcel(response, list, "机场大队信息数据");
    }

    /**
     * 获取机场大队信息详细信息
     */
    @GetMapping(value = "/{groupId}")
    public AjaxResult getInfo(@PathVariable("groupId") Long groupId)
    {
        return success(samsAirportGroupService.selectSamsAirportGroupByGroupId(groupId));
    }

    /**
     * 新增机场大队信息
     */
    @Log(title = "机场大队信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsAirportGroup samsAirportGroup)
    {
        return toAjax(samsAirportGroupService.insertSamsAirportGroup(samsAirportGroup));
    }

    /**
     * 修改机场大队信息
     */
    @Log(title = "机场大队信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsAirportGroup samsAirportGroup)
    {
        return toAjax(samsAirportGroupService.updateSamsAirportGroup(samsAirportGroup));
    }

    /**
     * 删除机场大队信息
     */
    @Log(title = "机场大队信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{groupIds}")
    public AjaxResult remove(@PathVariable Long[] groupIds)
    {
        return toAjax(samsAirportGroupService.deleteSamsAirportGroupByGroupIds(groupIds));
    }
}
