package com.ruoyi.web.controller.user;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.service.SysLoginService;
import com.ruoyi.system.mapper.SamsEmployeeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.ruoyi.common.core.domain.AjaxResult.success;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AppLoginController {
    @Autowired
    private SysLoginService loginService;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;
    @Autowired
    private WxMaService wxService;

    /**
     * 小程序手机号登录方法
     *
     * @param code 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestParam String code, @RequestParam String openId) {
        // 生成令牌
        return loginService.login(code, openId);
    }

    @GetMapping("/authToken4Miniapp")
    public AjaxResult authToken4Miniapp(@RequestParam String code) throws Exception {
        log.info("code:{}", code);
        WxMaJscode2SessionResult session = wxService.getUserService().getSessionInfo(code);
        log.info("WxMaJscode2SessionResult:{}", session);
        return success(session);
    }

    /**
     * 获取小程序用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        SamsEmployee user = SecurityUtils.getLoginUser().getEmployee();
        // 角色集合
//        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
//        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = success();
        ajax.put("user", samsEmployeeMapper.selectSamsEmployeeByEmployeeId(user.getEmployeeId()));
//        ajax.put("roles", roles);
//        ajax.put("permissions", permissions);
        return ajax;
    }
}
