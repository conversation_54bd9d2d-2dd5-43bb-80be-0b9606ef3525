<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsAirportGroupMapper">

    <resultMap type="SamsAirportGroup" id="SamsAirportGroupResult">
        <result property="groupId"    column="group_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="airportId"    column="airport_id"    />
        <result property="areaId"    column="area_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="groupCode"    column="group_code"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSamsAirportGroupVo">
        select group_id, dept_id, airport_id, area_id, group_name, group_code, del_flag, create_by, create_time, update_by, update_time, remark from sams_airport_group
    </sql>

    <select id="selectSamsAirportGroupList" parameterType="SamsAirportGroup" resultMap="SamsAirportGroupResult">
        <include refid="selectSamsAirportGroupVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="airportId != null "> and airport_id = #{airportId}</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="groupCode != null  and groupCode != ''"> and group_code = #{groupCode}</if>
        </where>
    </select>

    <select id="selectSamsAirportGroupByGroupId" parameterType="Long" resultMap="SamsAirportGroupResult">
        <include refid="selectSamsAirportGroupVo"/>
        where group_id = #{groupId}
    </select>

    <select id="countAirportGroup" resultType="integer">
        select count(1) from sams_airport_group
    </select>
    <select id="selectSysGroupByDeptIdAndAirportIdAndAreaIdAndGroupName" resultMap="SamsAirportGroupResult">
        <include refid="selectSamsAirportGroupVo"/>
        <where>
            <if test="deptId != null">
                dept_id = #{deptId}
            </if>
            <if test="airportId != null">
                and airport_id = #{airportId}
            </if>
            <if test="areaId != null">
                and area_id = #{areaId}
            </if>
            <if test="group != null">
                and group_name = #{group}
            </if>
        </where>
    </select>


    <insert id="insertSamsAirportGroup" parameterType="SamsAirportGroup" useGeneratedKeys="true" keyProperty="groupId">
        insert into sams_airport_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="airportId != null">airport_id,</if>
            <if test="areaId != null">area_id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="groupCode != null">group_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="airportId != null">#{airportId},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="groupCode != null">#{groupCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsAirportGroup" parameterType="SamsAirportGroup">
        update sams_airport_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="airportId != null">airport_id = #{airportId},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="groupCode != null">group_code = #{groupCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where group_id = #{groupId}
    </update>

    <delete id="deleteSamsAirportGroupByGroupId" parameterType="Long">
        delete from sams_airport_group where group_id = #{groupId}
    </delete>

    <delete id="deleteSamsAirportGroupByGroupIds" parameterType="String">
        delete from sams_airport_group where group_id in
        <foreach item="groupId" collection="array" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </delete>
</mapper>
