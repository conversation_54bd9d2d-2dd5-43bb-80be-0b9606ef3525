package com.ruoyi.web.controller.user;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.entity.SamsAirportTeam;
import com.ruoyi.system.service.ISamsAirportTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机场组别信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/airportTeam")
public class SamsAirportTeamController extends BaseController
{
    @Autowired
    private ISamsAirportTeamService samsAirportTeamService;

    /**
     * 查询机场组别信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirportTeam samsAirportTeam)
    {
        startPage();
        List<SamsAirportTeam> list = samsAirportTeamService.selectSamsAirportTeamList(samsAirportTeam);
        return getDataTable(list);
    }

    /**
     * 获取机场组别信息详细信息
     */
    @GetMapping(value = "/{teamId}")
    public AjaxResult getInfo(@PathVariable("teamId") Long teamId)
    {
        return success(samsAirportTeamService.selectSamsAirportTeamByTeamId(teamId));
    }
}
