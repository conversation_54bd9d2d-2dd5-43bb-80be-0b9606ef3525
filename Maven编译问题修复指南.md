# Maven编译问题修复指南

## 问题描述
```
Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.1:compile (default-compile) on project ruoyi-common: Fatal error compiling: java.lang.NoSuchFieldError: Class com.sun.tools.javac.tree.JCTree$JCImport does not have member field 'com.sun.tools.javac.tree.JCTree qualid'
```

## 问题原因
这个错误是由于Maven编译插件版本过旧（3.1）与当前JDK版本不兼容导致的。

## 已修复内容
已将 `maven-compiler-plugin` 版本从 `3.1` 升级到 `3.8.1`，并添加了必要的编译参数。

## 解决步骤

### 1. 清理Maven缓存
```bash
# 进入项目根目录
cd hwh-airport/yiting1-kaoqin-tx-kaoqin-api

# 清理项目
mvn clean

# 清理本地仓库缓存（可选）
mvn dependency:purge-local-repository
```

### 2. 重新编译
```bash
# 重新编译项目
mvn compile

# 或者完整构建
mvn clean install
```

### 3. 如果仍有问题，检查JDK版本
```bash
# 检查当前JDK版本
java -version
javac -version

# 检查Maven使用的JDK版本
mvn -version
```

### 4. 确保JDK版本兼容性
- 项目配置使用 Java 1.8
- 确保 JAVA_HOME 指向正确的JDK 1.8安装目录
- Maven编译插件 3.8.1 与 JDK 1.8 完全兼容

## 修改详情

### 原配置（有问题）：
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <version>3.1</version>
    <configuration>
        <source>${java.version}</source>
        <target>${java.version}</target>
        <encoding>${project.build.sourceEncoding}</encoding>
    </configuration>
</plugin>
```

### 新配置（已修复）：
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <version>3.8.1</version>
    <configuration>
        <source>${java.version}</source>
        <target>${java.version}</target>
        <encoding>${project.build.sourceEncoding}</encoding>
        <compilerArgs>
            <arg>-parameters</arg>
        </compilerArgs>
    </configuration>
</plugin>
```

## 版本兼容性说明

| Maven Compiler Plugin | 支持的JDK版本 |
|----------------------|--------------|
| 3.1                  | JDK 1.6-1.7  |
| 3.8.1                | JDK 1.8-11   |
| 3.10.1               | JDK 1.8-17   |

## 常见问题排查

### 问题1：IDE中仍显示错误
**解决方案**：
1. 刷新Maven项目：右键项目 → Maven → Reload Projects
2. 重新导入项目
3. 清理IDE缓存

### 问题2：编译时内存不足
**解决方案**：
```bash
# 设置Maven内存参数
export MAVEN_OPTS="-Xmx1024m -XX:MaxPermSize=256m"

# 或在Windows中
set MAVEN_OPTS=-Xmx1024m -XX:MaxPermSize=256m
```

### 问题3：依赖冲突
**解决方案**：
```bash
# 查看依赖树
mvn dependency:tree

# 解决冲突
mvn dependency:resolve
```

## 验证修复
编译成功后，应该看到类似输出：
```
[INFO] --- maven-compiler-plugin:3.8.1:compile (default-compile) @ ruoyi-common ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling XX source files to target/classes
[INFO] BUILD SUCCESS
```

## 注意事项
1. 修改后需要重新加载Maven项目
2. 建议清理缓存后重新编译
3. 确保所有开发人员使用相同的JDK版本
4. 如果使用IDE，建议重启IDE以确保配置生效
