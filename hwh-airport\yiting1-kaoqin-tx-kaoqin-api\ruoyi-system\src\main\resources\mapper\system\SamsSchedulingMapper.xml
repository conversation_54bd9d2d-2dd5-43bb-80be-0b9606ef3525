<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsSchedulingMapper">

    <resultMap type="SamsScheduling" id="SamsSchedulingResult">
        <result property="schedulingId"    column="scheduling_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="airportId"    column="airport_id"    />
        <result property="areaId"    column="area_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="teamId"    column="team_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="postId"    column="post_id"    />
        <result property="dutyDate"    column="duty_date"    />
        <result property="kqEmp"    column="kq_emp"    />
        <result property="postClock"    column="post_clock"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSamsSchedulingVo">
        select scheduling_id, dept_id, airport_id, area_id, group_id, team_id, project_id, post_id, duty_date, kq_emp, post_clock, del_flag, create_by, create_time, update_by, update_time, remark from sams_scheduling
    </sql>

    <select id="selectSamsSchedulingList" parameterType="SamsScheduling" resultMap="SamsSchedulingResult">
        <include refid="selectSamsSchedulingVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="airportId != null "> and airport_id = #{airportId}</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="postId != null "> and post_id = #{postId}</if>
            <if test="dutyDate != null "> and duty_date = #{dutyDate}</if>
            <if test="kqEmp != null  and kqEmp != ''"> and kq_emp = #{kqEmp}</if>
            <if test="cxyf != null  and cxyf != ''"> and duty_date like concat('%',#{cxyf},'%')</if>
        </where>
        order by create_time desc
    </select>


    <select id="list4cxTB" parameterType="SamsScheduling" resultMap="SamsSchedulingResult">
        SELECT
            s.*
        FROM
            sams_scheduling s
        where 1=1
            <if test="deptId != null">
                and s.dept_id=#{deptId}
            </if>
          <if test="airportId != null">
              and s.airport_id=#{airportId}
          </if>
        <if test="areaId != null">
            and s.area_id=#{areaId}
        </if>
        <if test="groupId != null">
            and s.group_id=#{groupId}
        </if>
        <if test="teamId != null">
            and s.team_id=#{teamId}
        </if>
        <if test="projectId != null">
            and s.project_id=#{projectId}
        </if>
        <if test="postId != null">
            and s.post_id = #{postId}
        </if>
          and s.duty_date like concat('%', #{cxStartDate}, '%')
    </select>

    <select id="selectSamsSchedulingList4Cx" parameterType="SamsScheduling" resultType="com.ruoyi.system.domain.vo.PbCxVo">
        select datelist,sum(gwtotal)as gwtotal,sum(rstotal)as rstotal from(
        SELECT
        datelist,0 as gwtotal,0 as rstotal
        FROM
        sys_calendar
        where  date(datelist) BETWEEN #{cxStartDate} AND #{cxEndDate}
        UNION ALL
        select t.datelist,t.gwtotal,t.rstotal from (
        SELECT
        se.duty_date as datelist,count(DISTINCT(s.post_id))as gwtotal,count(DISTINCT(se.employee_id))as rstotal
        FROM
        sams_scheduling s
        JOIN sams_scheduling_employee se ON s.scheduling_id = se.scheduling_id
        where 1=1
        and s.dept_id=#{deptId}
        and s.airport_id=#{airportId}
        and s.area_id=#{areaId}
        and s.group_id=#{groupId}
        and s.team_id=#{teamId}
        and s.project_id=#{projectId}
        <if test="postId != null "> and s.post_id = #{postId} </if>
        <if test="employeeId != null "> and se.employee_id = #{employeeId} </if>
        and se.duty_date BETWEEN #{cxStartDate} AND #{cxEndDate}
        GROUP BY se.duty_date
        order by se.duty_date asc) t)t2
        GROUP BY datelist
    </select>

    <select id="selectSamsSchedulingList4Cx2" parameterType="SamsScheduling" resultType="com.ruoyi.system.domain.vo.SamsSchedulingVo">
        SELECT
        s.scheduling_id as schedulingId,
<!--        se.duty_date as dutyDate,-->
        se.schedule_date dutyDate,
        s.post_id as postId,
        se.employee_id as employeeId,
        s.post_clock as postClock         FROM
        sams_scheduling s
        JOIN sams_post p ON s.post_id = p.post_id
        JOIN sams_scheduling_employee se ON s.scheduling_id = se.scheduling_id
        where 1=1
          <if test="deptId != null">
              and s.dept_id=#{deptId}
          </if>
          <if test="airportId != null">
              and s.airport_id=#{airportId}
          </if>
          <if test="areaId != null">
              and s.area_id=#{areaId}
          </if>
          <if test="groupId != null">
              and s.group_id=#{groupId}
          </if>
          <if test="teamId != null">
              and s.team_id=#{teamId}
          </if>
          <if test="projectId != null">
              and s.project_id=#{projectId}
          </if>
        <if test="postId != null "> and s.post_id = #{postId} </if>
        <if test="employeeId != null "> and se.employee_id = #{employeeId} </if>
<!--        and se.duty_date BETWEEN #{cxStartDate} AND #{cxEndDate}-->
<!--        group by se.duty_date, s.post_id, s.kq_emp-->
        and se.schedule_date BETWEEN #{cxStartDate} AND #{cxEndDate}
        group by se.schedule_date, s.post_id, s.kq_emp
    </select>


    <select id="selectSamsSchedulingBySchedulingId" parameterType="Long" resultMap="SamsSchedulingResult">
        <include refid="selectSamsSchedulingVo"/>
        where scheduling_id = #{schedulingId}
    </select>

    <select id="countByPostIdAndSchedulingId" parameterType="Long" resultMap="SamsSchedulingResult">
        <include refid="selectSamsSchedulingVo"/>
        where scheduling_id != #{schedulingId} and post_id = #{postId}
    </select>

    <select id="selectSamsSchedulingListBySchedulingIds" resultMap="SamsSchedulingResult">
        select * from sams_scheduling where scheduling_id in
        <foreach item="schedulingId" collection="array" open="(" separator="," close=")">
            #{schedulingId}
        </foreach>
    </select>

    <insert id="insertSamsScheduling" parameterType="SamsScheduling" useGeneratedKeys="true" keyProperty="schedulingId">
        insert into sams_scheduling
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="airportId != null">airport_id,</if>
            <if test="areaId != null">area_id,</if>
            <if test="groupId != null">group_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="postId != null">post_id,</if>
            <if test="dutyDate != null">duty_date,</if>
            <if test="kqEmp != null">kq_emp,</if>
            <if test="postClock != null">post_clock,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="airportId != null">#{airportId},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="postId != null">#{postId},</if>
            <if test="dutyDate != null">#{dutyDate},</if>
            <if test="kqEmp != null">#{kqEmp},</if>
            <if test="postClock != null">#{postClock},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsScheduling" parameterType="SamsScheduling">
        update sams_scheduling
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="airportId != null">airport_id = #{airportId},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="postId != null">post_id = #{postId},</if>
            <if test="dutyDate != null">duty_date = #{dutyDate},</if>
            <if test="kqEmp != null">kq_emp = #{kqEmp},</if>
            <if test="postClock != null">post_clock = #{postClock},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where scheduling_id = #{schedulingId}
    </update>

    <delete id="deleteSamsSchedulingBySchedulingId" parameterType="Long">
        delete from sams_scheduling where scheduling_id = #{schedulingId}
    </delete>

    <delete id="deleteSamsSchedulingBySchedulingIds" parameterType="String">
        delete from sams_scheduling where scheduling_id in
        <foreach item="schedulingId" collection="array" open="(" separator="," close=")">
            #{schedulingId}
        </foreach>
    </delete>

    <select id="selectSamsSchedulingByPostIdAndDutyDate" resultMap="SamsSchedulingResult">
        select * from sams_scheduling where post_id = #{postId} and duty_date like concat('%',#{dutyDate},'%')
    </select>

    <select id="selectSamsSchedulingByPostIdAndCreateDate" resultMap="SamsSchedulingResult">
        select * from sams_scheduling where post_id = #{postId} and create_time <![CDATA[ >= ]]> concat(#{createDate},' ' ,'00:00:00')
    </select>

    <select id="selectSamsSchedulingListByPostId" resultMap="SamsSchedulingResult">
        select * from sams_scheduling where post_id = #{postId}
    </select>
</mapper>
