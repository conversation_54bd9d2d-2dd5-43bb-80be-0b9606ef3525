/**
 * 上传相关工具函数
 */

/**
 * 获取上传URL
 * @param {string} endpoint - 上传端点，默认为 '/common/upload'
 * @returns {string} 完整的上传URL
 */
export function getUploadUrl(endpoint = '/common/upload') {
  const baseUrl = process.env.VUE_APP_BASE_API || ''
  return baseUrl + endpoint
}

/**
 * 获取文件访问URL
 * @param {string} filePath - 文件路径
 * @returns {string} 完整的文件访问URL
 */
export function getFileUrl(filePath) {
  if (!filePath) return ''
  
  // 如果已经是完整URL，直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath
  }
  
  const baseUrl = process.env.VUE_APP_BASE_API || ''
  
  // 如果文件路径不以/开头，添加/
  const path = filePath.startsWith('/') ? filePath : '/' + filePath
  
  return baseUrl + path
}

/**
 * 获取当前域名
 * @returns {string} 当前域名
 */
export function getCurrentDomain() {
  if (typeof window !== 'undefined') {
    return window.location.origin
  }
  return ''
}

/**
 * 构建完整的资源URL
 * @param {string} resourcePath - 资源路径
 * @param {boolean} useCurrentDomain - 是否使用当前域名，默认false使用配置的API地址
 * @returns {string} 完整的资源URL
 */
export function buildResourceUrl(resourcePath, useCurrentDomain = false) {
  if (!resourcePath) return ''
  
  // 如果已经是完整URL，直接返回
  if (resourcePath.startsWith('http://') || resourcePath.startsWith('https://')) {
    return resourcePath
  }
  
  const baseUrl = useCurrentDomain ? getCurrentDomain() : (process.env.VUE_APP_BASE_API || '')
  const path = resourcePath.startsWith('/') ? resourcePath : '/' + resourcePath
  
  return baseUrl + path
}
