package com.ruoyi.web.controller.user;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.entity.SamsAirport;
import com.ruoyi.system.service.ISamsAirportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机场信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/airport")
public class SamsAirportController extends BaseController
{
    @Autowired
    private ISamsAirportService samsAirportService;

    /**
     * 查询机场信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirport samsAirport)
    {
        startPage();
        List<SamsAirport> list = samsAirportService.selectSamsAirportList(samsAirport);
        return getDataTable(list);
    }


    /**
     * 获取机场信息详细信息
     */
    @GetMapping(value = "/{airportId}")
    public AjaxResult getInfo(@PathVariable("airportId") Long airportId)
    {
        return success(samsAirportService.selectSamsAirportByAirportId(airportId));
    }

}
