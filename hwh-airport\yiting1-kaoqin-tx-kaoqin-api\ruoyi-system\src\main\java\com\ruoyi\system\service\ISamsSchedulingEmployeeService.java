package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SamsSchedulingEmployee;
import com.ruoyi.system.domain.vo.CxkqVo;

/**
 * 员工排班信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-03
 */
public interface ISamsSchedulingEmployeeService 
{
    /**
     * 查询员工排班信息
     * 
     * @param schedulingEmployeeId 员工排班信息主键
     * @return 员工排班信息
     */
    public SamsSchedulingEmployee selectSamsSchedulingEmployeeBySchedulingEmployeeId(Long schedulingEmployeeId);

    /**
     * 查询员工排班信息列表
     * 
     * @param samsSchedulingEmployee 员工排班信息
     * @return 员工排班信息集合
     */
    public List<SamsSchedulingEmployee> selectSamsSchedulingEmployeeList(SamsSchedulingEmployee samsSchedulingEmployee);

    /**
     *
     * @param cxkqVo
     * @return
     */
    List<SamsSchedulingEmployee> mykqdk(CxkqVo cxkqVo);

    /**
     * 新增员工排班信息
     * 
     * @param samsSchedulingEmployee 员工排班信息
     * @return 结果
     */
    public int insertSamsSchedulingEmployee(SamsSchedulingEmployee samsSchedulingEmployee);

    /**
     * 修改员工排班信息
     * 
     * @param samsSchedulingEmployee 员工排班信息
     * @return 结果
     */
    public int updateSamsSchedulingEmployee(SamsSchedulingEmployee samsSchedulingEmployee);

    /**
     * 批量删除员工排班信息
     * 
     * @param schedulingEmployeeIds 需要删除的员工排班信息主键集合
     * @return 结果
     */
    public int deleteSamsSchedulingEmployeeBySchedulingEmployeeIds(Long[] schedulingEmployeeIds);

    /**
     * 删除员工排班信息信息
     * 
     * @param schedulingEmployeeId 员工排班信息主键
     * @return 结果
     */
    public int deleteSamsSchedulingEmployeeBySchedulingEmployeeId(Long schedulingEmployeeId);
}
