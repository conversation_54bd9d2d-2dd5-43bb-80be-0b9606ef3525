package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.system.domain.SamsPostClock;
import lombok.Data;

import java.util.List;

/**
 * 岗位导出对象
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
public class SamsPostExportVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    private Long postId;
    /**
     * 岗位名称
     */
    @Excel(name = "岗位名称", needMerge = true)
    private String postName;
    /**
     * 部门
     */
    @Excel(name = "部门", needMerge = true)
    private String deptName;

    /**
     * 机场机场
     */
    @Excel(name = "机场", needMerge = true)
    private String airport;

    /**
     * 区域
     */
    @Excel(name = "区域", needMerge = true)
    private String area;

    /**
     * 大队
     */
    @Excel(name = "大队", needMerge = true)
    private String group;

    /**
     * 组别
     */
    @Excel(name = "组别", needMerge = true)
    private String team;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称", needMerge = true)
    private String projectName;

    /**
     * 岗位类别
     */
    @Excel(name = "岗位类别", needMerge = true)
    private String postNature;

    /**
     * 打卡数量
     */
//    @Excel(name = "打卡数量")
    private Integer dksl;

    /**
     * 打卡地点
     */
//    @Excel(name = "打卡地点")
    private String clockAddress;

    /**
     * 打卡范围
     */
//    @Excel(name = "打卡范围")
    private String clockRange;

//    @Excel(name = "岗位人数")
    private Integer postNum;

    @Excel(name = "排班状态", needMerge = true)
    private String pb;

    @Excel(name = "最后排班日期", needMerge = true)
    private String lastPbrq;

    @Excel(name = "打卡信息")
    private List<SamsPostClock> postClockList;
}

