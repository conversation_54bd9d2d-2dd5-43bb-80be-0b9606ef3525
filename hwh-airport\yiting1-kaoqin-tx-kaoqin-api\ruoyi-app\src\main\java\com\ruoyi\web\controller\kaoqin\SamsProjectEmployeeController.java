package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.SamsProjectEmployee;
import com.ruoyi.system.service.ISamsProjectEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目员工Controller
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/projectEmployee")
public class SamsProjectEmployeeController extends BaseController {
    @Autowired
    private ISamsProjectEmployeeService samsProjectEmployeeService;

    /**
     * 根据项目id查询执勤人员信息
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsProjectEmployee samsProjectEmployee) {
        startPage();
        List<SamsProjectEmployee> list = samsProjectEmployeeService.selectSamsProjectEmployeeList(samsProjectEmployee);
        return getDataTable(list);
    }

    /**
     * 获取项目员工详细信息
     */
    @GetMapping(value = "/{projectEmployeeId}")
    public AjaxResult getInfo(@PathVariable("projectEmployeeId") Long projectEmployeeId) {
        return success(samsProjectEmployeeService.selectSamsProjectEmployeeByProjectEmployeeId(projectEmployeeId));
    }

}
