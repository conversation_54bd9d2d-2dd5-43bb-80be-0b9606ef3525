package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 分析考勤报对象 sams_scheduling_kqfx
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
public class SamsSchedulingKqfxExportVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "姓名")
    private String name;

    @Excel(name = "证件类型")
    private String cardTypeName;

    private int cardType;

    @Excel(name = "证件号码")
    private String cardId;

    @Excel(name = "年月")
    private String cxny;

    /**
     * 岗位名称
     */
    @Excel(name = "岗位")
    private String postName;

    @Excel(name = "班次统计")
    private Integer bctj;

    /**
     * 工作时长（分钟）
     */
    @Excel(name = "工作时长")
    private Long gzsc;

    @Excel(name = "加班次数")
    private Integer jbcs;

    /**
     * 加班时长（分钟）
     */
    @Excel(name = "加班时长")
    private Long jbsc;

    @Excel(name = "考勤旷工次数")
    private Integer kgcs;

    /**
     * 迟到次数
     */
    @Excel(name = "考勤迟到次数")
    private Integer cdcs;

    @Excel(name = "考勤早退次数")
    private Integer ztcs;
}
