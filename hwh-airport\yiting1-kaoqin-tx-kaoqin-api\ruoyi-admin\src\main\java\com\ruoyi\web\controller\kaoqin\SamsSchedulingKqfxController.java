package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxExportVo;
import com.ruoyi.system.service.ISamsSchedulingKqfxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 分析考勤报Controller
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@RestController
@RequestMapping("/system/schedulingKqfx")
public class SamsSchedulingKqfxController extends BaseController
{
    @Autowired
    private ISamsSchedulingKqfxService samsSchedulingKqfxService;

    /**
     * 查询分析考勤报列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsSchedulingKqfx samsSchedulingKqfx)
    {
        startPage();
        List<SamsSchedulingKqfx> list = samsSchedulingKqfxService.selectSamsSchedulingKqfxList(samsSchedulingKqfx);
        return getDataTable(list);
    }

    /**
     * 导出分析考勤报列表
     */
    @Log(title = "分析考勤报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsSchedulingKqfx samsSchedulingKqfx)
    {
        List<SamsSchedulingKqfxExportVo> list = samsSchedulingKqfxService.selectSamsSchedulingKqfxExportList(samsSchedulingKqfx);
        ExcelUtil<SamsSchedulingKqfxExportVo> util = new ExcelUtil<>(SamsSchedulingKqfxExportVo.class);
        util.exportExcel(response, list, "分析考勤报数据");
    }

    /**
     * 获取分析考勤报详细信息
     */
    @GetMapping(value = "/{kqfxId}")
    public AjaxResult getInfo(@PathVariable("kqfxId") Long kqfxId)
    {
        return success(samsSchedulingKqfxService.selectSamsSchedulingKqfxByKqfxId(kqfxId));
    }

    /**
     * 新增分析考勤报
     */
    @Log(title = "分析考勤报", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsSchedulingKqfx samsSchedulingKqfx)
    {
        return toAjax(samsSchedulingKqfxService.insertSamsSchedulingKqfx(samsSchedulingKqfx));
    }

    /**
     * 修改分析考勤报
     */
    @Log(title = "分析考勤报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsSchedulingKqfx samsSchedulingKqfx)
    {
        return toAjax(samsSchedulingKqfxService.updateSamsSchedulingKqfx(samsSchedulingKqfx));
    }

    /**
     * 删除分析考勤报
     */
    @Log(title = "分析考勤报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{kqfxIds}")
    public AjaxResult remove(@PathVariable Long[] kqfxIds)
    {
        return toAjax(samsSchedulingKqfxService.deleteSamsSchedulingKqfxByKqfxIds(kqfxIds));
    }
}
