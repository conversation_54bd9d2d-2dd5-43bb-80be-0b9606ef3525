package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SamsPostClock;
import com.ruoyi.system.service.ISamsPostClockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 岗位打卡信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/postClock")
public class SamsPostClockController extends BaseController
{
    @Autowired
    private ISamsPostClockService samsPostClockService;

    /**
     * 查询岗位打卡信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsPostClock samsPostClock)
    {
        startPage();
        List<SamsPostClock> list = samsPostClockService.selectSamsPostClockList(samsPostClock);
        return getDataTable(list);
    }

    /**
     * 获取岗位打卡信息详细信息
     */
    @GetMapping(value = "/{postClockId}")
    public AjaxResult getInfo(@PathVariable("postClockId") Long postClockId)
    {
        return success(samsPostClockService.selectSamsPostClockByPostClockId(postClockId));
    }

    /**
     * 新增岗位打卡信息
     */
    @Log(title = "岗位打卡信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsPostClock samsPostClock)
    {
        return toAjax(samsPostClockService.insertSamsPostClock(samsPostClock));
    }

    /**
     * 修改岗位打卡信息
     */
    @Log(title = "岗位打卡信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsPostClock samsPostClock)
    {
        return toAjax(samsPostClockService.updateSamsPostClock(samsPostClock));
    }

    /**
     * 删除岗位打卡信息
     */
    @Log(title = "岗位打卡信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{postClockIds}")
    public AjaxResult remove(@PathVariable Long[] postClockIds)
    {
        return toAjax(samsPostClockService.deleteSamsPostClockByPostClockIds(postClockIds));
    }
}
