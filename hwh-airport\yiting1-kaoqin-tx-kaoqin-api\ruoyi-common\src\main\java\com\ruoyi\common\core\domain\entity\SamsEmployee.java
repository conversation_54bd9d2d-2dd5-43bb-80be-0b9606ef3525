package com.ruoyi.common.core.domain.entity;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 员工信息对象 sams_employee
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsEmployee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 员工信息ID */
    private Long employeeId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 机场ID */
    @Excel(name = "机场ID")
    private Long airportId;

    /** 区域ID */
    @Excel(name = "区域ID")
    private Long areaId;

    /** 大队ID */
    @Excel(name = "大队ID")
    private Long groupId;

    /** 小组ID */
    @Excel(name = "小组ID")
    private Long teamId;

    /** 员工编号 */
    @Excel(name = "员工编号")
    private String empNo;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobilePhone;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 汉语拼音 */
    @Excel(name = "汉语拼音")
    private String namePinyin;

    /** 姓名简拼 */
    @Excel(name = "姓名简拼")
    private String nameJianpin;

    /** 性别 0男，1女 */
    @Excel(name = "性别 0男，1女")
    private Integer sex;

    /** 民族 */
    @Excel(name = "民族")
    private String nation;

    /** 类别 0内部员工，1劳务派遣 */
    @Excel(name = "类别 0内部员工，1劳务派遣")
    private Integer typeName;

    /** 证件类型 0身份证，1护照，2军官证 */
    @Excel(name = "证件类型 0身份证，1护照，2军官证")
    private Integer cardType;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String cardId;

    /** 人脸识别照片 */
    @Excel(name = "人脸识别照片")
    private String checkImg;

    /** 籍贯 */
    @Excel(name = "籍贯")
    private String jiguan;

    /** 户籍地址 */
    @Excel(name = "户籍地址")
    private String domicileAddr;

    /** 家庭住址 */
    @Excel(name = "家庭住址")
    private String homeAddr;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthDate;

    /** 户口性质 0 城市户口，1农业户口 */
    @Excel(name = "户口性质 0 城市户口，1农业户口")
    private Integer domicileType;

    /** 政治面貌 0群众，1中共党员 */
    @Excel(name = "政治面貌 0群众，1中共党员")
    private Integer polotocalStatus;

    /** 健康状况 */
    @Excel(name = "健康状况")
    private String healthStatus;

    /** 婚姻状况 */
    @Excel(name = "婚姻状况")
    private String maritalStatus;

    /** 学历 0小学，1初中，2高中，3大专，4本科，5硕士，6博士 */
    @Excel(name = "学历 0小学，1初中，2高中，3大专，4本科，5硕士，6博士")
    private Integer education;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 学校 */
    @Excel(name = "学校")
    private String school;

    /** 学历证书照片 */
    @Excel(name = "毕业证书")
    private String educationImg;

    /** 学位 */
    @Excel(name = "学位")
    private String degree;

    /** 特长 */
    @Excel(name = "特长")
    private String talent;

    /** 岗位 */
    @Excel(name = "岗位")
    private String postName;

    /** 参加工作日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "参加工作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cjgzDate;

    /** 紧急联系人 */
    @Excel(name = "紧急联系人")
    private String emContact;

    /** 紧急联系人联系方式 */
    @Excel(name = "紧急联系人联系方式")
    private String emContactPhone;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ruzhiDate;

    /** 离职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lizhiDate;

    /** 司龄 */
    @Excel(name = "司龄")
    private String gsYear;

    /** 合同到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date htComplateDate;

    /** 退休日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退休日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tuixiuDate;

    /** 绑定小程序手机号 */
    @Excel(name = "绑定小程序手机号")
    private String bindPhone;

    /** OpenID */
    @Excel(name = "OpenID")
    private String openId;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nicheng;

    /** 头像 */
    @Excel(name = "头像")
    private String avate;

    /** 状态（0启用 1禁用） */
    @Excel(name = "状态", readConverterExp = "0=启用,1=禁用")
    private String status;

    @Excel(name = "排班权限 0无，1有")
    private Integer pbqx;
    @Excel(name = "出生地")
    private String birthAddress;

    @Excel(name = "户口所在地")
    private String hkszd;

    // 部门名称
//    private String deptName;
//    private String groupName;
//    private String teamName;
//    private String areaName;
//    private String airportName;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    private String accessToken;
    private SysDept sysDept;
    private SamsAirport samsAirport;
    private SamsAirportArea samsAirportArea;
    private SamsAirportGroup samsAirportGroup;
    private SamsAirportTeam samsAirportTeam;
    private Long projectId;
    private List<SamsEmployeeCert> employeeCertList;
}
