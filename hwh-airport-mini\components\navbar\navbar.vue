<template>
	<view class="example-body" style="padding-bottom: 20rpx;">
		<image src="../../static/imgjy/shouye_bg.png" class="bg"></image>
		<uni-nav-bar :fixed="false" color="#333333" background-color="#FFFFFF" right-icon="scan" @clickLeft="showCity" @clickRight="scan">
			<view class="input-view">
				<image src="../../static/imgjy/touxiang_fang.png" style="width: 62rpx;margin-top: -6px; border-radius: 31rpx;" mode="widthFix" @click="gotouserCenter"></image>
				<text style="color: #fff;font-size: 28rpx;margin-left: 16rpx;">武汉<text style="font-size: 24rpx;margin-left: 10rpx;">[ 切换 ]</text></text>
				<!-- <image src="../../static/icon/sousuo_icon.png" style="width: 30rpx;height: 28rpx;position: absolute;left: 252rpx;top: 70rpx;" mode=""></image>
				<text style="position: absolute;left: 300rpx;top: 54rpx;color: #ccc;">|</text>
				<input confirm-type="search" class="nav-bar-input" type="text" placeholder="搜您想搜" @confirm="confirm"> -->
				<span class="scyj">
					<view class="jiage">5.04</view>
					<view class="jiage_title">市场油价</view>
				</span>
				<span class="jryj">
					<view class="jryjjiage">4.82</view>
					<view class="jryjjiage_title">今日油价</view>
				</span>
			</view>
			<view>移动加油</view>
		</uni-nav-bar>
	</view>
</template>

<script>
	export default {
		name: 'navbar',
		data() {
			return {
				city: '北京'
			}
		},
		methods: {
			gotouserCenter(){
				console.log('555')
				uni.navigateTo({
					url:'../../pages/member/index/index'
				})
			},
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			showMenu() {
				uni.showToast({
					title: '菜单'
				})
			},
			clickLeft() {
		
				uni.showToast({
					title: '左侧按钮'
				})
			},
			search() {
				uni.showToast({
					title: '搜索'
				})
			},
			showCity() {
		
				uni.showToast({
					title: '选择城市'
				})
			},
			scan() {
				uni.showToast({
					title: '扫码'
				})
			},
			confirm() {
				uni.showToast({
					title: '搜索'
				})
			}
		},
		onPullDownRefresh() {
			console.log('onPullDownRefresh')
			setTimeout(function() {
				uni.stopPullDownRefresh()
				console.log('stopPullDownRefresh')
			}, 1000)
		}
	}
</script>

<style>
/* 头条小程序组件内不能引入字体 */
	/* #ifdef MP-TOUTIAO */
/* 	@font-face {
		font-family: uniicons;
		font-weight: normal;
		font-style: normal;
		src: url('~@/static/uni.ttf') format('truetype');
	} */

	/* #endif */

	/* #ifndef APP-NVUE */
	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #efeff4;
		min-height: 100%;
		height: auto;
	}

	view {
		font-size: 14px;
		line-height: inherit;
	}

	.example {
		padding: 0 15px 15px;
	}

	.example-info {
		padding: 15px;
		color: #3b4144;
		background: #ffffff;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		/* display: flex; */
		/* #endif */
		padding: 0;
		font-size: 28rpx;
		background-color: #ffffff;
	}
     
	 .bg {
		 width: 100%;
         position: absolute;
		 height: 310rpx;
		 z-index:1;
	 }
	 
	/* #endif */
	.example {
		padding: 0 30rpx;
	}

	.example-info {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 30rpx;
		color: #3b4144;
		background-color: #ffffff;
		font-size: 28rpx;
		line-height: 40rpx;
	}

	.example-info-text {
		font-size: 28rpx;
		line-height: 40rpx;
		color: #3b4144;
	}


	.example-body {
		flex-direction: column;
		padding: 30rpx;
		background-color: #ffffff;
		/* height: 132rpx; */
		height: 310rpx;
		width: 100%;
		overflow: hidden;
		/* position: fixed; */
		position: relative;
		top: 0;
		z-index:9999;
	}

	.word-btn-white {
		font-size: 18px;
		color: #FFFFFF;
	}

	.word-btn {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		border-radius: 6px;
		height: 48px;
		margin: 15px;
		background-color: #007AFF;
	}

	.word-btn--hover {
		background-color: #4ca2ff;
	}

	.uni-nav-bar-text {
		font-size: 14px;
	}

	.city {
		/* #ifndef APP-PLUS-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		/* width: 160rpx;
 */
		margin-left: 4px;
	}

	.input-view {
		/* #ifndef APP-PLUS-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		flex: 1;
		height: 56px;
		border-radius: 15px;
		padding: 28px 10px 0 10px;
		flex-wrap: nowrap;
		line-height: 30px;
		position: relative;
         z-index:999;		
	}
	.scyj {
		display: inline-block;
	    position: absolute;
	    left: 130rpx;
	    top: 165rpx;
	}
	.jryj {
		display: inline-block;
		position: absolute;
		right: 135rpx;
		top: 165rpx;
	}
	.scyj .jiage,
	.scyj .jiage_title{
		text-align: center;
		height: 45rpx;
		line-height: 45rpx;
	}
	.scyj .jiage {
		font-size: 46rpx;
		color: #fff;
	}
	.scyj .jiage_title {
		font-size: 22rpx;
		color: #fff;
	}
	.jryj .jryjjiage,
	.jryj .jryjjiage_title{
		text-align: center;
		height: 45rpx;
		line-height: 45rpx;
	}
	.jryj .jryjjiage {
		font-size: 46rpx;
		color: #fff;
	}
	.jryj .jryjjiage_title {
		font-size: 22rpx;
		color: #fff;
	}
	.input-uni-icon {
		line-height: 60rpx;
	}

	.nav-bar-input {
		height: 56rpx;
		line-height: 60rpx;
		padding: 0 10rpx;
		font-size: 28rpx;
		background-color: #fff;
		border-radius: 40rpx;
		padding-left: 90rpx;
		margin-left: 20rpx;
		width: 388rpx;
		box-sizing: border-box;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 0;
	}
</style>
