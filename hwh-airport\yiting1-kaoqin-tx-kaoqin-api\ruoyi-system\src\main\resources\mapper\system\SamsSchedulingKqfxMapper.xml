<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsSchedulingKqfxMapper">
    <resultMap type="SamsSchedulingKqfx" id="SamsSchedulingKqfxResult">
        <result property="kqfxId" column="kqfx_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="postId" column="post_id"/>
        <result property="airportId" column="airport_id"/>
        <result property="areaId" column="area_id"/>
        <result property="groupId" column="group_id"/>
        <result property="teamId" column="team_id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="cxny" column="cxny"/>
        <result property="gzsc" column="gzsc"/>
        <result property="jbsc" column="jbsc"/>
        <result property="yccs" column="yccs"/>
        <result property="cdcs" column="cdcs"/>
        <result property="kgcs" column="kgcs"/>
        <result property="bctj" column="bctj"/>
        <result property="jbcs" column="jbcs"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="ztcs" column="ztcs"/>
        <result property="postName" column="post_name"/>

        <!--        部门名称-->
        <association property="sysDept" column="dept_id" javaType="com.ruoyi.common.core.domain.entity.SysDept">
            <id property="deptId" column="dept_id"/>
            <result property="deptName" column="dept_name"/>
        </association>
        <association property="samsAirport" column="airport_id"
                     javaType="com.ruoyi.common.core.domain.entity.SamsAirport">
            <id property="airportId" column="airport_id"/>
            <result property="airportName" column="airport_name"/>
        </association>
        <association property="samsAirportArea" column="area_id"
                     javaType="com.ruoyi.common.core.domain.entity.SamsAirportArea">
            <id property="areaId" column="area_id"/>
            <result property="areaName" column="area_name"/>
        </association>
        <association property="samsAirportGroup" column="group_id"
                     javaType="com.ruoyi.common.core.domain.entity.SamsAirportGroup">
            <id property="groupId" column="group_id"/>
            <result property="groupName" column="group_name"/>
        </association>
        <association property="samsAirportTeam" column="team_id"
                     javaType="com.ruoyi.common.core.domain.entity.SamsAirportTeam">
            <id property="teamId" column="team_id"/>
            <result property="teamName" column="team_name"/>
        </association>
<!--        员工信息-->
        <association property="samsEmployee" column="employee_id"
                     javaType="com.ruoyi.common.core.domain.entity.SamsEmployee">
            <id property="employeeId" column="employee_id"/>
            <result property="empNo" column="emp_no"/>
            <result property="name" column="name"/>
            <result property="cardType" column="card_type"/>
            <result property="cardId" column="card_id"/>
            <result property="mobilePhone" column="mobile_phone"/>
        </association>
    </resultMap>

    <sql id="selectSamsSchedulingKqfxVo">
        select sk.kqfx_id,
               sk.dept_id,
               sk.post_id,
               sk.airport_id,
               sk.area_id,
               sk.group_id,
               sk.team_id,
               sk.employee_id,
               sk.cxny,
               sk.gzsc,
               sk.jbsc,
               sk.yccs,
               sk.cdcs,
               sk.kgcs,
               sk.bctj,
               sk.jbcs,
               sk.del_flag,
               sk.create_by,
               sk.create_time,
               sk.update_by,
               sk.update_time,
               sk.remark,
               sk.post_name,
               se.emp_no,
               se.mobile_phone,
               se.NAME,
               se.card_type,
               se.card_id,
               sd.dept_name,
               sa.airport_name,
               saa.area_name,
               sat.team_name,
               sag.group_name
        from sams_scheduling_kqfx sk
                 join sams_employee as se on sk.employee_id = se.employee_id
                 LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id
                 LEFT JOIN sams_airport sa ON sk.airport_id = sa.airport_id
                 LEFT JOIN sams_airport_area saa ON sk.area_id = saa.area_id
                 LEFT JOIN sams_airport_team sat ON sk.team_id = sat.team_id
                 LEFT JOIN sams_airport_group sag ON sk.group_id = sag.group_id
                 join sams_post sp on sk.post_id = sp.post_id
    </sql>

    <select id="selectSamsSchedulingKqfxList" parameterType="SamsSchedulingKqfx" resultMap="SamsSchedulingKqfxResult">
        select sk.*,
        se.emp_no,
        se.mobile_phone,
        se.NAME,
        se.card_type,
        se.card_id,
        sp.post_name,
        sd.dept_name,
        sa.airport_name,
        saa.area_name,
        sat.team_name,
        sag.group_name from sams_scheduling_kqfx as sk
        join sams_employee as se on sk.employee_id = se.employee_id
        left join sams_project_employee spe on sk.employee_id = spe.employee_id
        LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id
        LEFT JOIN sams_airport sa ON sk.airport_id = sa.airport_id
        LEFT JOIN sams_airport_area saa ON sk.area_id = saa.area_id
        LEFT JOIN sams_airport_team sat ON sk.team_id = sat.team_id
        LEFT JOIN sams_airport_group sag ON sk.group_id = sag.group_id
        join sams_post sp on sk.post_id = sp.post_id
        <where>
            <if test="deptId != null ">
                and sk.dept_id = #{deptId}
            </if>
            <if test="postId != null ">
                and sk.post_id= #{postId}
            </if>
            <if test="airportId != null ">
                and sk.airport_id = #{airportId}
            </if>
            <if test="areaId != null ">
                and sk.area_id = #{areaId}
            </if>
            <if test="groupId != null ">
                and sk.group_id = #{groupId}
            </if>
            <if test="teamId != null ">
                and sk.team_id = #{teamId}
            </if>
            <if test="projectId != null ">
                and spe.project_id = #{projectId}
            </if>
            <if test="postId != null ">
                and sk.post_id = #{postId}
            </if>
            <if test="mobilePhone != null  and mobilePhone != ''">
                and se.mobile_phone = #{mobilePhone}
            </if>
            <if test="name != null  and name != ''">
                and se.name like concat('%', #{name}, '%')
            </if>
            <if test="cxny != null">
                and sk.cxny =#{cxny}
            </if>
        </where>
    </select>

    <select id="selectSamsSchedulingKqfxByKqfxId" parameterType="Long" resultMap="SamsSchedulingKqfxResult">
        <include refid="selectSamsSchedulingKqfxVo"/>
        where sk.kqfx_id = #{kqfxId}
    </select>

    <select id="selectByQueryVo" parameterType="com.ruoyi.system.domain.vo.SamsSchedulingKqfxQueryVo"
            resultMap="SamsSchedulingKqfxResult">
        select sk.*,
        se.emp_no,
        se.mobile_phone,
        se.NAME,
        se.card_type,
        se.card_id,
        sp.post_name,
        sd.dept_name,
        sa.airport_name,
        saa.area_name,
        sat.team_name,
        sag.group_name from sams_scheduling_kqfx as sk
        join sams_employee as se on sk.employee_id = se.employee_id
        LEFT JOIN sys_dept sd ON sk.dept_id = sd.dept_id
        LEFT JOIN sams_airport sa ON sk.airport_id = sa.airport_id
        LEFT JOIN sams_airport_area saa ON sk.area_id = saa.area_id
        LEFT JOIN sams_airport_team sat ON sk.team_id = sat.team_id
        LEFT JOIN sams_airport_group sag ON sk.group_id = sag.group_id
        join sams_post sp on sk.post_id = sp.post_id
        <where>
            <if test="deptId != null ">
                and sk.dept_id = #{deptId}
            </if>
            <if test="postId != null ">
                and sk.post_id= #{postId}
            </if>
            <if test="airportId != null ">
                and sk.airport_id = #{airportId}
            </if>
            <if test="areaId != null ">
                and sk.area_id = #{areaId}
            </if>
            <if test="groupId != null ">
                and sk.group_id = #{groupId}
            </if>
            <if test="teamId != null ">
                and sk.team_id = #{teamId}
            </if>
            <if test="employeeId != null">
                and se.employee_id = #{employeeId}
            </if>
            <if test="cxny != null">
                and sk.cxny =#{cxny}
            </if>
        </where>
    </select>


    <insert id="insertSamsSchedulingKqfx" parameterType="SamsSchedulingKqfx" useGeneratedKeys="true"
            keyProperty="kqfxId">
        insert into sams_scheduling_kqfx
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="postId != null">
                post_id,
            </if>
            <if test="airportId != null">
                airport_id,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="cxny != null">
                cxny,
            </if>
            <if test="gzsc != null">
                gzsc,
            </if>
            <if test="jbsc != null">
                jbsc,
            </if>
            <if test="yccs != null">
                yccs,
            </if>
            <if test="cdcs!= null">
                cdcs,
            </if>
            <if test="kgcs != null">
                kgcs,
            </if>
            <if test="bctj != null">
                bctj,
            </if>
            <if test="jbcs != null">
                jbcs,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="ztcs != null">
                ztcs,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">
                #{deptId},
            </if>
            <if test="postId != null">
                #{postId},
            </if>
            <if test="airportId != null">
                #{airportId},
            </if>
            <if test="areaId != null">
                #{areaId},
            </if>
            <if test="groupId != null">
                #{groupId},
            </if>
            <if test="teamId != null">
                #{teamId},
            </if>
            <if test="employeeId != null">
                #{employeeId},
            </if>
            <if test="cxny != null">
                #{cxny},
            </if>
            <if test="gzsc != null">
                #{gzsc},
            </if>
            <if test="jbsc != null">
                #{jbsc},
            </if>
            <if test="yccs != null">
                #{yccs},
            </if>
            <if test="cdcs != null">
                #{cdcs},
            </if>
            <if test="kgcs != null">
                #{kgcs},
            </if>
            <if test="bctj != null">
                #{bctj},
            </if>
            <if test="jbcs != null">
                #{jbcs},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="ztcs != null">
                #{ztcs},
            </if>
        </trim>
    </insert>

    <update id="updateSamsSchedulingKqfx" parameterType="SamsSchedulingKqfx">
        update sams_scheduling_kqfx
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="postId != null">
                post_id = #{postId},
            </if>
            <if test="airportId != null">
                airport_id = #{airportId},
            </if>
            <if test="areaId != null">
                area_id = #{areaId},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
            <if test="teamId != null">
                team_id = #{teamId},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId},
            </if>
            <if test="cxny != null">
                cxny = #{cxny},
            </if>
            <if test="gzsc != null">
                gzsc = #{gzsc},
            </if>
            <if test="jbsc != null">
                jbsc = #{jbsc},
            </if>
            <if test="yccs != null">
                yccs = #{yccs},
            </if>
            <if test="cdcs != null">
                cdcs = #{cdcs},
            </if>
            <if test="kgcs != null">
                kgcs = #{kgcs},
            </if>
            <if test="bctj != null">
                bctj = #{bctj},
            </if>
            <if test="jbcs != null">
                jbcs = #{jbcs},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="ztcs != null">
                ztcs = #{ztcs},
            </if>
        </trim>
        where kqfx_id = #{kqfxId}
    </update>

    <delete id="deleteSamsSchedulingKqfxByKqfxId" parameterType="Long">
        delete
        from sams_scheduling_kqfx
        where kqfx_id = #{kqfxId}
    </delete>

    <delete id="deleteSamsSchedulingKqfxByKqfxIds" parameterType="String">
        delete from sams_scheduling_kqfx where kqfx_id in
        <foreach item="kqfxId" collection="array" open="(" separator="," close=")">
            #{kqfxId}
        </foreach>
    </delete>

    <resultMap type="SamsSchedulingKqfxExportVo" id="SamsSchedulingKqfxExportResult">
        <result property="name" column="name"/>
        <result property="cardType" column="card_type"/>
        <result property="cardId" column="card_id"/>
        <result property="cxny" column="cxny"/>
        <result property="postName" column="post_name"/>
        <result property="bctj" column="bctj"/>
        <result property="gzsc" column="gzsc"/>
        <result property="jbcs" column="jbcs"/>
        <result property="jbsc" column="jbsc"/>
        <result property="kgcs" column="kgcs"/>
        <result property="cdcs" column="cdcs"/>
        <result property="ztcs" column="ztcs"/>
    </resultMap>

    <select id="selectSamsSchedulingKqfxExportList" parameterType="SamsSchedulingKqfx"
            resultMap="SamsSchedulingKqfxExportResult">
        select se.name,
               se.card_type,
               se.card_id,
               sk.cxny,
               sp.post_name,
               sk.bctj,
               sk.gzsc,
               sk.jbcs,
               sk.jbsc,
               sk.kgcs,
               sk.cdcs,
               sk.ztcs
        from sams_scheduling_kqfx as sk
                 join sams_employee as se on sk.employee_id = se.employee_id
                 join sams_post sp on sp.post_id = sk.post_id
    </select>

    <delete id="deleteByYearMonth" parameterType="String">
        delete from sams_scheduling_kqfx where cxny = #{yearMonth}
    </delete>
</mapper>
