package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsEmployeeCert;

/**
 * 员工证书信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-15
 */
public interface SamsEmployeeCertMapper 
{
    /**
     * 查询员工证书信息
     * 
     * @param employeeCertId 员工证书信息主键
     * @return 员工证书信息
     */
    public SamsEmployeeCert selectSamsEmployeeCertByEmployeeCertId(Long employeeCertId);
    int countNum(String certDict);
    /**
     * 查询员工证书信息列表
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 员工证书信息集合
     */
    public List<SamsEmployeeCert> selectSamsEmployeeCertList(SamsEmployeeCert samsEmployeeCert);

    /**
     * 新增员工证书信息
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 结果
     */
    public int insertSamsEmployeeCert(SamsEmployeeCert samsEmployeeCert);

    /**
     * 修改员工证书信息
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 结果
     */
    public int updateSamsEmployeeCert(SamsEmployeeCert samsEmployeeCert);

    /**
     * 删除员工证书信息
     * 
     * @param employeeCertId 员工证书信息主键
     * @return 结果
     */
    public int deleteSamsEmployeeCertByEmployeeCertId(Long employeeCertId);
    int deleteByEmployeeId(Long employeeId);
    /**
     * 批量删除员工证书信息
     * 
     * @param employeeCertIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsEmployeeCertByEmployeeCertIds(Long[] employeeCertIds);
}
