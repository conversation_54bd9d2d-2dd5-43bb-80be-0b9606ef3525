package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SamsEmployeeApply;
import com.ruoyi.system.service.ISamsEmployeeApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工申请信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@RestController
@RequestMapping("/app/employeeApply")
public class SamsEmployeeApplyController extends BaseController {
    @Autowired
    private ISamsEmployeeApplyService samsEmployeeApplyService;

    /**
     * 查询员工申请信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsEmployeeApply samsEmployeeApply) {
        samsEmployeeApply.setEmployeeId(getUserId());
        startPage();
        List<SamsEmployeeApply> list = samsEmployeeApplyService.selectSamsEmployeeApplyList(samsEmployeeApply);
        return getDataTable(list);
    }


    /**
     * 获取员工申请信息详细信息
     */
    @GetMapping(value = "/{employeeApplyId}")
    public AjaxResult getInfo(@PathVariable("employeeApplyId") Long employeeApplyId) {
        return success(samsEmployeeApplyService.selectSamsEmployeeApplyByEmployeeApplyId(employeeApplyId));
    }

    /**
     * 新增员工申请信息
     */
    @Log(title = "员工申请信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsEmployeeApply samsEmployeeApply) {
        samsEmployeeApply.setEmployeeId(getUserId());
        return toAjax(samsEmployeeApplyService.insertSamsEmployeeApply(samsEmployeeApply));
    }

}
