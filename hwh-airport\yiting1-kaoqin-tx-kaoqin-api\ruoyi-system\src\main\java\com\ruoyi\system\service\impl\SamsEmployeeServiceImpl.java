package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.domain.ZzjgVo;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.enmus.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsEmployeeService;
import com.ruoyi.system.utils.GsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 员工信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
@Slf4j
public class SamsEmployeeServiceImpl implements ISamsEmployeeService {
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportGroupMapper samsAirportGroupMapper;
    @Autowired
    private SamsAirportTeamMapper samsAirportTeamMapper;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SamsEmployeeCertMapper samsEmployeeCertMapper;

    /**
     * 查询员工信息
     *
     * @param employeeId 员工信息主键
     * @return 员工信息
     */
    @Override
    public SamsEmployee selectSamsEmployeeByEmployeeId(Long employeeId) {
        SamsEmployee samsEmployee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(employeeId);
        SamsEmployeeCert samsEmployeeCert = new SamsEmployeeCert();
        samsEmployeeCert.setEmployeeId(samsEmployee.getEmployeeId());
        samsEmployee.setEmployeeCertList(samsEmployeeCertMapper.selectSamsEmployeeCertList(samsEmployeeCert));
        return samsEmployee;
    }

    /**
     * 查询员工信息列表
     *
     * @param samsEmployee 员工信息
     * @return 员工信息
     */
    @Override
    public List<SamsEmployee> selectSamsEmployeeList(SamsEmployee samsEmployee) {
        List<SamsEmployee> list = samsEmployeeMapper.selectSamsEmployeeList(samsEmployee);
//        if (list != null && list.size() > 0) {
//            for (SamsEmployee employee : list) {
//                employee.setSysDept(sysDeptMapper.selectDeptById(employee.getDeptId()));
//                employee.setSamsAirport(samsAirportMapper.selectSamsAirportByAirportId(employee.getAirportId()));
//                employee.setSamsAirportArea(samsAirportAreaMapper.selectSamsAirportAreaByAreaId(employee.getAreaId()));
//                employee.setSamsAirportGroup(samsAirportGroupMapper.selectSamsAirportGroupByGroupId(employee.getGroupId()));
//                employee.setSamsAirportTeam(samsAirportTeamMapper.selectSamsAirportTeamByTeamId(employee.getTeamId()));
//            }
//        }
        return list;
    }

    /**
     * 查询员工信息列表
     *
     * @param samsEmployee 员工信息
     * @return 员工信息
     */
    @Override
    public List<SamsEmployeeExportVo> selectSamsEmployeeExportList(SamsEmployee samsEmployee) {
        List<SamsEmployeeExportVo> list = samsEmployeeMapper.selectSamsEmployeeExportVoList(samsEmployee);
        if (list != null && list.size() > 0) {
            for (SamsEmployeeExportVo employee : list) {
                Sex sex = Sex.getSex(employee.getSex());
                if (sex != null) {
                    employee.setSexName(sex.getInfo());
                }
                TypeName typeName = TypeName.getTypeName(employee.getTypeName());
                if (null != typeName) {
                    employee.setType(typeName.getInfo());
                }
                CardType certType = CardType.getCertType(employee.getCardType());
                if (null != certType) {
                    employee.setCard(certType.getInfo());
                }
                // 户口性质
                DomicileType domicileType = DomicileType.getDomicileType(employee.getDomicile());
                employee.setDomicileType(null != domicileType ? domicileType.getInfo() : null);
                // 政治面貌
                PolotocalStatus polotocalStatus = PolotocalStatus.getPolotocalStatus(employee.getPolotocalStatus());
                employee.setPolotocalStatusDesc(null != polotocalStatus ? polotocalStatus.getInfo() : null);
                // 学历
                Education education = Education.getEducation(employee.getEducation());
                employee.setEducationDesc(null != education ? education.getInfo() : null);
                // 排班权限
                SchedulingAuthority schedulingAuthority = SchedulingAuthority.getSchedulingAuthority(employee.getPbqx());
                employee.setPbqxDesc(null != schedulingAuthority ? schedulingAuthority.getInfo() : null);
                // 司龄
                if (null != employee.getRuzhiDate()) {
                    employee.setGsYear(GsUtil.getSn(employee.getRuzhiDate()));
                }
            }
        }
        return list;
    }

    @Override
    public YgrsVo ygrs() {
        YgrsVo ygrsVo = new YgrsVo();
        ygrsVo.setTotalRs(samsEmployeeMapper.totalRs());
        ygrsVo.setNxRs(samsEmployeeMapper.nxRs(0));
        ygrsVo.setNxRs2(samsEmployeeMapper.nxRs(1));
        ygrsVo.setBenjiRs(samsEmployeeMapper.typeRs(1));
        ygrsVo.setLwpqRs(samsEmployeeMapper.typeRs(2));
        ygrsVo.setGysRs(samsEmployeeMapper.typeRs(3));
        return ygrsVo;
    }

    @Override
    public YlzbVo nlzb() {
        YlzbVo ylzbVo = new YlzbVo();
        Integer totalRs = samsEmployeeMapper.totalRs();
        Integer cat1 = samsEmployeeMapper.cxnlrys(20, 29);
        Integer cat2 = samsEmployeeMapper.cxnlrys(30, 39);


        Integer cat3 = samsEmployeeMapper.cxnlrys(40, 49);
        Integer cat4 = samsEmployeeMapper.cxnlrys(50, 59);
        Integer cat5 = samsEmployeeMapper.cxnlrys(60, 69);
        ylzbVo.setCat1(cat1);
        ylzbVo.setCat1St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat1 / totalRs * 100) + "%");
        ylzbVo.setCat2(cat2);
        ylzbVo.setCat2St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat2 / totalRs * 100) + "%");
        ylzbVo.setCat3(cat3);
        ylzbVo.setCat3St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat3 / totalRs * 100) + "%");
        ylzbVo.setCat4(cat4);
        ylzbVo.setCat4St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat4 / totalRs * 100) + "%");
        ylzbVo.setCat5(cat5);
        ylzbVo.setCat5St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat5 / totalRs * 100) + "%");
        return ylzbVo;
    }

    @Override
    public XlzbVo xlzb() {
        XlzbVo xlzbVo = new XlzbVo();
        Integer totalRs = samsEmployeeMapper.totalRs();
        Integer cat1 = samsEmployeeMapper.cxxlrys(0);
        Integer cat2 = samsEmployeeMapper.cxxlrys(1);
        Integer cat3 = samsEmployeeMapper.cxxlrys(2);
        Integer cat4 = samsEmployeeMapper.cxxlrys(3);
        Integer cat5 = samsEmployeeMapper.cxxlrys(4);
        Integer cat6 = samsEmployeeMapper.cxxlrys(6);
        Integer cat7 = samsEmployeeMapper.cxxlrys(7);
        xlzbVo.setCat1(cat1);
        xlzbVo.setCat1St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat1 / totalRs * 100) + "%");
        xlzbVo.setCat2(cat2);
        xlzbVo.setCat2St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat2 / totalRs * 100) + "%");
        xlzbVo.setCat3(cat3);
        xlzbVo.setCat3St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat3 / totalRs * 100) + "%");
        xlzbVo.setCat4(cat4);
        xlzbVo.setCat4St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat4 / totalRs * 100) + "%");
        xlzbVo.setCat5(cat5);
        xlzbVo.setCat5St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat5 / totalRs * 100) + "%");
        xlzbVo.setCat6(cat6);
        xlzbVo.setCat6St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat6 / totalRs * 100) + "%");
        xlzbVo.setCat7(cat7);
        xlzbVo.setCat7St(totalRs.equals(0) ? "0%" : String.format("%.2f", (double) cat7 / totalRs * 100) + "%");
        return xlzbVo;
    }

    @Override
    public List<ZszbVo> zszb() {
        List<SysDictData> dictDataList = DictUtils.getDictCache("sys_cert_type");
        List<ZszbVo> list = new ArrayList<>();
        if (dictDataList != null && dictDataList.size() > 0) {
            int all = samsEmployeeCertMapper.countNum(null);
            for (SysDictData sysDictData : dictDataList) {
                int num = samsEmployeeCertMapper.countNum(sysDictData.getDictValue());
                ZszbVo zszbVo = new ZszbVo();
                zszbVo.setNum(num);
                zszbVo.setCertDict(sysDictData.getDictLabel());
                zszbVo.setCetSt(all==0? "0%" : String.format("%.2f", (double) num / all * 100) + "%");
                list.add(zszbVo);
            }
        }
        return list;
    }

    @Override
    public ZzjgVo zzjg() {
        ZzjgVo zzjgVo = new ZzjgVo();
        zzjgVo.setDept(sysDeptMapper.countDept());
        zzjgVo.setJc(samsAirportMapper.countAirport());
        zzjgVo.setQuyu(samsAirportAreaMapper.countAirportArea());
        zzjgVo.setDadui(samsAirportGroupMapper.countAirportGroup());
        zzjgVo.setZubie(samsAirportTeamMapper.countAirportTeam());
        return zzjgVo;
    }

    /**
     * 新增员工信息
     *
     * @param samsEmployee 员工信息
     * @return 结果
     */
    @Override
    public int insertSamsEmployee(SamsEmployee samsEmployee) {
        samsEmployee.setCreateTime(DateUtils.getNowDate());
//        samsEmployee.setPbqx(0);
        Integer empNo = redisCache.getCacheObject("empNo");
        if (empNo == null) {
            empNo = 1;
        } else {
            empNo++;
        }
        redisCache.setCacheObject("empNo", empNo);
        samsEmployee.setEmpNo("BH" + String.format("%08d", empNo));
        int rows = samsEmployeeMapper.insertSamsEmployee(samsEmployee);
        List<SamsEmployeeCert> employeeCertList = samsEmployee.getEmployeeCertList();
        if (employeeCertList != null && employeeCertList.size() > 0) {
            for (SamsEmployeeCert samsEmployeeCert : employeeCertList) {
                samsEmployeeCert.setEmployeeId(samsEmployee.getEmployeeId());
                samsEmployeeCert.setCreateTime(DateUtils.getNowDate());
                samsEmployeeCertMapper.insertSamsEmployeeCert(samsEmployeeCert);
            }
        }
        //开始添加腾讯人脸信息
        TxRlsbUtils.addPerson(samsEmployee.getName(), samsEmployee.getCheckImg(), samsEmployee.getEmployeeId() + "");
        return rows;
    }

    /**
     * 修改员工信息
     *
     * @param samsEmployee 员工信息
     * @return 结果
     */
    @Override
    public int updateSamsEmployee(SamsEmployee samsEmployee) {
        samsEmployee.setUpdateTime(DateUtils.getNowDate());
        int rows = samsEmployeeMapper.updateSamsEmployee(samsEmployee);
        samsEmployeeCertMapper.deleteByEmployeeId(samsEmployee.getEmployeeId());
        List<SamsEmployeeCert> employeeCertList = samsEmployee.getEmployeeCertList();
        if (employeeCertList != null && employeeCertList.size() > 0) {
            for (SamsEmployeeCert samsEmployeeCert : employeeCertList) {
                samsEmployeeCert.setEmployeeId(samsEmployee.getEmployeeId());
                samsEmployeeCert.setCreateTime(DateUtils.getNowDate());
                samsEmployeeCertMapper.insertSamsEmployeeCert(samsEmployeeCert);
            }
        }
        //先删除再添加腾讯人脸
        TxRlsbUtils.deletePerson(samsEmployee.getEmployeeId() + "");
        //开始添加腾讯人脸信息
        SamsEmployee db = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(samsEmployee.getEmployeeId());
        TxRlsbUtils.addPerson(db.getName(), db.getCheckImg(), samsEmployee.getEmployeeId() + "");
        return rows;
    }

    /**
     * 批量删除员工信息
     *
     * @param employeeIds 需要删除的员工信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsEmployeeByEmployeeIds(Long[] employeeIds) {
        return samsEmployeeMapper.deleteSamsEmployeeByEmployeeIds(employeeIds);
    }

    /**
     * 删除员工信息信息
     *
     * @param employeeId 员工信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsEmployeeByEmployeeId(Long employeeId) {
        return samsEmployeeMapper.deleteSamsEmployeeByEmployeeId(employeeId);
    }

    @Override
    public String importUser(List<SamsEmployeeImportVo> userList, boolean updateSupport, String operName) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new ServiceException("导入员工数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SamsEmployeeImportVo user : userList) {
            try {
                // 1-5: 只要有一个查询为空，则导入失败
                if (StringUtils.isNull(user.getDeptName()) || StringUtils.isNull(user.getAirport())
                        || StringUtils.isNull(user.getArea()) || StringUtils.isNull(user.getGroup())
                        || StringUtils.isNull(user.getTeam()) || StringUtils.isNull(user.getName())
                        || StringUtils.isNull(user.getMobilePhone())) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、员工信息导入失败：部门、机场、区域、组别、大队、项目名称、姓名、手机号不能为空");
                    continue;
                }
                // 1.根据部门名称查询部门信息
                SysDept sysDept = sysDeptMapper.selectDeptByDeptName(user.getDeptName());
                if (null == sysDept) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、员工信息导入失败：部门不存在");
                    continue;
                }
                // 2.根据部门id、机场名查询机场
                SamsAirport airport = samsAirportMapper.selectSamsAirportByDeptIdAndAirportName(sysDept.getDeptId(), user.getAirport());
                if (null == airport) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、员工信息导入失败：机场不存在");
                    continue;
                }
                // 3.根据部门id、机场id、区域名查询区域
                SamsAirportArea area = samsAirportAreaMapper.selectSysAreaByDeptIdAndAirportIdAndAreaName(sysDept.getDeptId(),
                        airport.getAirportId(), user.getArea());
                if (null == area) {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、员工信息导入失败：区域不存在");
                    continue;
                }
                SamsEmployee employee = new SamsEmployee();
                BeanUtils.copyProperties(user, employee);
                employee.setDeptId(sysDept.getDeptId());
                // 4.根据部门id、机场id、区域id、组名查询组
                SamsAirportGroup group = samsAirportGroupMapper.selectSysGroupByDeptIdAndAirportIdAndAreaIdAndGroupName(
                        sysDept.getDeptId(), airport.getAirportId(), area.getAreaId(), user.getGroup());
                if (null != group) {
                    employee.setGroupId(group.getGroupId());
                    // 5.根据部门id、机场id、区域id、组名id、大队名查询大队
                    SamsAirportTeam team = samsAirportTeamMapper.selectSysTeamByDeptIdAndAirportIdAndAreaIdAndGroupIdAndTeamName(
                            sysDept.getDeptId(), airport.getAirportId(), area.getAreaId(), group.getGroupId(), user.getTeam());
                    if (null != team) {
                        employee.setTeamId(team.getTeamId());
                    }
                }

                // 6.根据部门id、机场id、区域id、组别id、大队id、用户名查询用户，判断用户是否重复

                employee.setAirportId(airport.getAirportId());
                employee.setAreaId(area.getAreaId());
                employee.setName(user.getName());
                employee.setMobilePhone(user.getMobilePhone());
                List<SamsEmployee> samsEmployeeList = samsEmployeeMapper.selectSamsEmployeeList(employee);

                // 性别
                if (StringUtils.isNotBlank(user.getSexName())) {
                    employee.setSex(Sex.getSex(user.getSexName()) != null ? Sex.getSex(user.getSexName()).getCode() : null);
                }
                // 类型
                if (StringUtils.isNotBlank(user.getType())) {
                    employee.setTypeName(TypeName.getTypeName(user.getType()) != null ? TypeName.getTypeName(user.getType()).getCode() : null);
                }
                // 证件类型
                if (StringUtils.isNotBlank(user.getCard())) {
                    employee.setCardType(CardType.getCertType(user.getCard()) != null ? CardType.getCertType(user.getCard()).getCode() : null);
                }
                // 户口性质
                if (StringUtils.isNotBlank(user.getDomicileType())) {
                    employee.setDomicileType(DomicileType.getDomicileType(user.getDomicileType()) != null ? DomicileType.getDomicileType(user.getDomicileType()).getCode() : null);
                }
                // 政治面貌
                if (StringUtils.isNotBlank(user.getPolotocalStatusDesc())) {
                    employee.setPolotocalStatus(PolotocalStatus.getPolotocalStatus(user.getPolotocalStatusDesc()) != null ? PolotocalStatus.getPolotocalStatus(user.getPolotocalStatusDesc()).getCode() : null);
                }
                // 学历
                if (StringUtils.isNotBlank(user.getEducationDesc())) {
                    employee.setEducation(Education.getEducation(user.getEducationDesc()) != null ? Education.getEducation(user.getEducationDesc()).getCode() : null);
                }
                // 排班权限
                // 排班权限
                if (StringUtils.isNotBlank(user.getPbqxDesc())) {
                    employee.setPbqx(SchedulingAuthority.getSchedulingAuthority(user.getPbqxDesc()) != null ? SchedulingAuthority.getSchedulingAuthority(user.getPbqxDesc()).getCode() : null);
                }
                if (CollectionUtils.isEmpty(samsEmployeeList)) {
                    // 添加
                    int count = this.insertSamsEmployee(employee);
                    if (count > 0) {
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、账号 " + user.getName() + " 导入成功");
                    } else {
                        successNum++;
                        successMsg.append("<br/>" + successNum + "、账号 " + user.getName() + " 导入失败");
                    }
                } else if (updateSupport) {
                    // 修改
                    employee.setEmployeeId(samsEmployeeList.get(0).getEmployeeId());
                    this.updateSamsEmployee(employee);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + user.getName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + user.getName() + " 已存在");
                }
            }
            catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + user.getName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
