package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxExportVo;

/**
 * 分析考勤报Service接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public interface ISamsSchedulingKqfxService
{
    /**
     * 查询分析考勤报
     *
     * @param kqfxId 分析考勤报主键
     * @return 分析考勤报
     */
    public SamsSchedulingKqfx selectSamsSchedulingKqfxByKqfxId(Long kqfxId);

    /**
     * 查询分析考勤报列表
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 分析考勤报集合
     */
    public List<SamsSchedulingKqfx> selectSamsSchedulingKqfxList(SamsSchedulingKqfx samsSchedulingKqfx);

    /**
     * 新增分析考勤报
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 结果
     */
    public int insertSamsSchedulingKqfx(SamsSchedulingKqfx samsSchedulingKqfx);

    /**
     * 修改分析考勤报
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 结果
     */
    public int updateSamsSchedulingKqfx(SamsSchedulingKqfx samsSchedulingKqfx);

    /**
     * 批量删除分析考勤报
     *
     * @param kqfxIds 需要删除的分析考勤报主键集合
     * @return 结果
     */
    public int deleteSamsSchedulingKqfxByKqfxIds(Long[] kqfxIds);

    /**
     * 删除分析考勤报信息
     *
     * @param kqfxId 分析考勤报主键
     * @return 结果
     */
    public int deleteSamsSchedulingKqfxByKqfxId(Long kqfxId);

    /**
     * 查询分析考勤报导出列表
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 分析考勤报集合
     */
    List<SamsSchedulingKqfxExportVo> selectSamsSchedulingKqfxExportList(SamsSchedulingKqfx samsSchedulingKqfx);
}
