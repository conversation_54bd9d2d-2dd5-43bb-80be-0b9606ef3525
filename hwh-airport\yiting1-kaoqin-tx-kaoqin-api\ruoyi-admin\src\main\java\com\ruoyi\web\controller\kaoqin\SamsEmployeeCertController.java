package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SamsEmployeeCert;
import com.ruoyi.system.service.ISamsEmployeeCertService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 员工证书信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-15
 */
@RestController
@RequestMapping("/system/employeeCert")
public class SamsEmployeeCertController extends BaseController
{
    @Autowired
    private ISamsEmployeeCertService samsEmployeeCertService;

    /**
     * 查询员工证书信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsEmployeeCert samsEmployeeCert)
    {
        startPage();
        List<SamsEmployeeCert> list = samsEmployeeCertService.selectSamsEmployeeCertList(samsEmployeeCert);
        return getDataTable(list);
    }

    /**
     * 导出员工证书信息列表
     */
    @Log(title = "员工证书信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsEmployeeCert samsEmployeeCert)
    {
        List<SamsEmployeeCert> list = samsEmployeeCertService.selectSamsEmployeeCertList(samsEmployeeCert);
        ExcelUtil<SamsEmployeeCert> util = new ExcelUtil<SamsEmployeeCert>(SamsEmployeeCert.class);
        util.exportExcel(response, list, "员工证书信息数据");
    }

    /**
     * 获取员工证书信息详细信息
     */
    @GetMapping(value = "/{employeeCertId}")
    public AjaxResult getInfo(@PathVariable("employeeCertId") Long employeeCertId)
    {
        return success(samsEmployeeCertService.selectSamsEmployeeCertByEmployeeCertId(employeeCertId));
    }

    /**
     * 新增员工证书信息
     */
    @Log(title = "员工证书信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsEmployeeCert samsEmployeeCert)
    {
        return toAjax(samsEmployeeCertService.insertSamsEmployeeCert(samsEmployeeCert));
    }

    /**
     * 修改员工证书信息
     */
    @Log(title = "员工证书信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsEmployeeCert samsEmployeeCert)
    {
        return toAjax(samsEmployeeCertService.updateSamsEmployeeCert(samsEmployeeCert));
    }

    /**
     * 删除员工证书信息
     */
    @Log(title = "员工证书信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{employeeCertIds}")
    public AjaxResult remove(@PathVariable Long[] employeeCertIds)
    {
        return toAjax(samsEmployeeCertService.deleteSamsEmployeeCertByEmployeeCertIds(employeeCertIds));
    }
}
