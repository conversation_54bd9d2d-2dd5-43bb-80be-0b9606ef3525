package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsEmployeeCert;

/**
 * 员工证书信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-15
 */
public interface ISamsEmployeeCertService 
{
    /**
     * 查询员工证书信息
     * 
     * @param employeeCertId 员工证书信息主键
     * @return 员工证书信息
     */
    public SamsEmployeeCert selectSamsEmployeeCertByEmployeeCertId(Long employeeCertId);

    /**
     * 查询员工证书信息列表
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 员工证书信息集合
     */
    public List<SamsEmployeeCert> selectSamsEmployeeCertList(SamsEmployeeCert samsEmployeeCert);

    /**
     * 新增员工证书信息
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 结果
     */
    public int insertSamsEmployeeCert(SamsEmployeeCert samsEmployeeCert);

    /**
     * 修改员工证书信息
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 结果
     */
    public int updateSamsEmployeeCert(SamsEmployeeCert samsEmployeeCert);

    /**
     * 批量删除员工证书信息
     * 
     * @param employeeCertIds 需要删除的员工证书信息主键集合
     * @return 结果
     */
    public int deleteSamsEmployeeCertByEmployeeCertIds(Long[] employeeCertIds);

    /**
     * 删除员工证书信息信息
     * 
     * @param employeeCertId 员工证书信息主键
     * @return 结果
     */
    public int deleteSamsEmployeeCertByEmployeeCertId(Long employeeCertId);
}
