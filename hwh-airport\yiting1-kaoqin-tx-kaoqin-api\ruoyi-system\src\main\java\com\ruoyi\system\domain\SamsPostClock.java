package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 岗位打卡信息对象 sams_post_clock
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsPostClock extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 岗位打卡ID
     */
    private Long postClockId;

    /**
     * 岗位ID
     */
//    @Excel(name = "岗位ID")
    private Long postId;

    /**
     * 打卡开始时间
     */
    @Excel(name = "打卡开始时间")
    private String startTime;

    /**
     * 打卡开始时间前
     */
    @Excel(name = "打卡开始时间前")
    private String startTimeBefore;
    /**
     * 打卡开始时间后
     */
    @Excel(name = "打卡开始时间后")
    private String startTimeAfter;

    /**
     * 打卡结束时间
     */
    @Excel(name = "打卡结束时间")
    private String endTime;

    /**
     * 打卡结束时间前
     */
    @Excel(name = "打卡结束时间前")
    private String endTimeBefore;

    /**
     * 打卡结束时间后
     */
    @Excel(name = "打卡结束时间后")
    private String endTimeAfter;

    /**
     * 打卡地点
     */
    @Excel(name = "打卡地点")
    private String clockAddress;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String latitude;

    /**
     * 维度
     */
    @Excel(name = "维度")
    private String longitude;

    /**
     * 打卡范围
     */
    @Excel(name = "打卡范围")
    private Long clockRange;

    /**
     * 工作范围
     */
    @Excel(name = "工作范围")
    private Long workRange;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    @Excel(name = "打卡名称")
    private String clockName;
    @Excel(name = "地点命名")
    private String ddmm;

    /**
     * 次日 (0否 1是)
     */
    private int nextDay;

    /**
     *标识员工是否可以任意时间打卡(0 否 1 是)
     */

    private int isAnytimeCheckIn;

    private String clockEmp;
}
