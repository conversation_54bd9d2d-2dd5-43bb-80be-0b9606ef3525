package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 打卡状态
 *
 * <AUTHOR>
 */
public enum DkStatus
{
    NOT_PUNCHED(0, "未打卡"),
    PUNCHED(1, "已打卡"),
    LATE(3, "迟到"),
    ABNORMAL_PUNCH(4, "打卡异常"),
    LEFT_EARLY(5, "早退"),
    ON_TIME(6, "上班未打卡"),
    OFF_TIME(7, "下班未打卡"),
    ;

    private final Integer code;
    private final String info;

    DkStatus(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static DkStatus getStatus(Integer code) {
        for (DkStatus sex : DkStatus.values()) {
            if (Objects.equals(sex.getCode(), code)) {
                return sex;
            }
        }
        return null;
    }

    public static DkStatus getSex(String info) {
        for (DkStatus sex : DkStatus.values()) {
            if (Objects.equals(sex.getInfo(), info)) {
                return sex;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
