package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SamsEmployeeApply;
import com.ruoyi.system.service.ISamsEmployeeApplyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 员工申请信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-03
 */
@RestController
@RequestMapping("/system/employeeApply")
public class SamsEmployeeApplyController extends BaseController
{
    @Autowired
    private ISamsEmployeeApplyService samsEmployeeApplyService;

    /**
     * 查询员工申请信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsEmployeeApply samsEmployeeApply)
    {
        startPage();
        List<SamsEmployeeApply> list = samsEmployeeApplyService.selectSamsEmployeeApplyList(samsEmployeeApply);
        return getDataTable(list);
    }

    /**
     * 导出员工申请信息列表
     */
    @Log(title = "员工申请信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsEmployeeApply samsEmployeeApply)
    {
        List<SamsEmployeeApply> list = samsEmployeeApplyService.selectSamsEmployeeApplyList(samsEmployeeApply);
        ExcelUtil<SamsEmployeeApply> util = new ExcelUtil<SamsEmployeeApply>(SamsEmployeeApply.class);
        util.exportExcel(response, list, "员工申请信息数据");
    }

    /**
     * 获取员工申请信息详细信息
     */
    @GetMapping(value = "/{employeeApplyId}")
    public AjaxResult getInfo(@PathVariable("employeeApplyId") Long employeeApplyId)
    {
        return success(samsEmployeeApplyService.selectSamsEmployeeApplyByEmployeeApplyId(employeeApplyId));
    }

    /**
     * 新增员工申请信息
     */
    @Log(title = "员工申请信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsEmployeeApply samsEmployeeApply)
    {
        return toAjax(samsEmployeeApplyService.insertSamsEmployeeApply(samsEmployeeApply));
    }

    /**
     * 修改员工申请信息
     */
    @Log(title = "员工申请信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsEmployeeApply samsEmployeeApply)
    {
        return toAjax(samsEmployeeApplyService.updateSamsEmployeeApply(samsEmployeeApply));
    }

    /**
     * 删除员工申请信息
     */
    @Log(title = "员工申请信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{employeeApplyIds}")
    public AjaxResult remove(@PathVariable Long[] employeeApplyIds)
    {
        return toAjax(samsEmployeeApplyService.deleteSamsEmployeeApplyByEmployeeApplyIds(employeeApplyIds));
    }
}
