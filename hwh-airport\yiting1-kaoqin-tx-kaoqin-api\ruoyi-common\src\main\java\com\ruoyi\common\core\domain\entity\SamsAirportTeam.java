package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 机场组别信息对象 sams_airport_team
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsAirportTeam extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 小组ID
     */
    private Long teamId;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场ID
     */
    @Excel(name = "机场ID")
    private Long airportId;

    /**
     * 区域ID
     */
    @Excel(name = "区域ID")
    private Long areaId;

    /**
     * 大队ID
     */
    @Excel(name = "大队ID")
    private Long groupId;

    /**
     * 组别名称
     */
    @Excel(name = "组别名称")
    private String teamName;

    /**
     * 组别编码
     */
    @Excel(name = "组别编码")
    private String teamCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SysDept sysDept;
    private SamsAirport samsAirport;
    private SamsAirportArea samsAirportArea;
    private SamsAirportGroup samsAirportGroup;

}
