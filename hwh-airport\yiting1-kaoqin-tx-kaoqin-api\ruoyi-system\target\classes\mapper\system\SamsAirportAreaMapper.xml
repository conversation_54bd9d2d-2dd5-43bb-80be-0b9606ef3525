<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsAirportAreaMapper">

    <resultMap type="SamsAirportArea" id="SamsAirportAreaResult">
        <result property="areaId"    column="area_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="airportId"    column="airport_id"    />
        <result property="areaName"    column="area_name"    />
        <result property="areaCode"    column="area_code"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSamsAirportAreaVo">
        select area_id, dept_id, airport_id, area_name, area_code, del_flag, create_by, create_time, update_by, update_time, remark from sams_airport_area
    </sql>

    <select id="selectSamsAirportAreaList" parameterType="SamsAirportArea" resultMap="SamsAirportAreaResult">
        <include refid="selectSamsAirportAreaVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="airportId != null "> and airport_id = #{airportId}</if>
            <if test="areaName != null  and areaName != ''"> and area_name like concat('%', #{areaName}, '%')</if>
            <if test="areaCode != null  and areaCode != ''"> and area_code = #{areaCode}</if>
        </where>
    </select>

    <select id="selectSamsAirportAreaByAreaId" parameterType="Long" resultMap="SamsAirportAreaResult">
        <include refid="selectSamsAirportAreaVo"/>
        where area_id = #{areaId}
    </select>

    <select id="countAirportArea" resultType="integer">
        select count(1) from sams_airport_area
    </select>
    <select id="selectSysAreaByDeptIdAndAirportIdAndAreaName" resultMap="SamsAirportAreaResult">
        <include refid="selectSamsAirportAreaVo"/>
        <where>
            <if test="deptId != null">
                dept_id = #{deptId}
            </if>
            <if test="airportId != null">
                and airport_id = #{airportId}
            </if>
            <if test="area != null">
                and area_name = #{area}
            </if>
        </where>
    </select>

    <insert id="insertSamsAirportArea" parameterType="SamsAirportArea" useGeneratedKeys="true" keyProperty="areaId">
        insert into sams_airport_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="airportId != null">airport_id,</if>
            <if test="areaName != null">area_name,</if>
            <if test="areaCode != null">area_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="airportId != null">#{airportId},</if>
            <if test="areaName != null">#{areaName},</if>
            <if test="areaCode != null">#{areaCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsAirportArea" parameterType="SamsAirportArea">
        update sams_airport_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="airportId != null">airport_id = #{airportId},</if>
            <if test="areaName != null">area_name = #{areaName},</if>
            <if test="areaCode != null">area_code = #{areaCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where area_id = #{areaId}
    </update>

    <delete id="deleteSamsAirportAreaByAreaId" parameterType="Long">
        delete from sams_airport_area where area_id = #{areaId}
    </delete>

    <delete id="deleteSamsAirportAreaByAreaIds" parameterType="String">
        delete from sams_airport_area where area_id in
        <foreach item="areaId" collection="array" open="(" separator="," close=")">
            #{areaId}
        </foreach>
    </delete>
</mapper>
