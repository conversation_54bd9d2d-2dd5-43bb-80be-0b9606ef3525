package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 供应商信息对象 sams_supplier
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
public class SamsSupplier extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 供应商ID */
    private Long supplierId;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 联系人名称 */
    @Excel(name = "联系人名称")
    private String contactsName;

    /** 联系人电话 */
    @Excel(name = "联系人电话")
    private String contactsPhone;

    /** 有效开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startValidDate;

    /** 有效结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endValidDate;

    /** 联系人角色 */
    @Excel(name = "联系人角色")
    private String contactsRole;

    /** 审核状态 0待审核，1已通过，2未通过 */
    @Excel(name = "审核状态 0待审核，1已通过，2未通过")
    private Integer authStatus;

    /** 状态（0启用 1禁用） */
    @Excel(name = "状态", readConverterExp = "0=启用,1=禁用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setSupplierId(Long supplierId) 
    {
        this.supplierId = supplierId;
    }

    public Long getSupplierId() 
    {
        return supplierId;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setContactsName(String contactsName) 
    {
        this.contactsName = contactsName;
    }

    public String getContactsName() 
    {
        return contactsName;
    }
    public void setContactsPhone(String contactsPhone) 
    {
        this.contactsPhone = contactsPhone;
    }

    public String getContactsPhone() 
    {
        return contactsPhone;
    }
    public void setStartValidDate(Date startValidDate) 
    {
        this.startValidDate = startValidDate;
    }

    public Date getStartValidDate() 
    {
        return startValidDate;
    }
    public void setEndValidDate(Date endValidDate) 
    {
        this.endValidDate = endValidDate;
    }

    public Date getEndValidDate() 
    {
        return endValidDate;
    }
    public void setContactsRole(String contactsRole) 
    {
        this.contactsRole = contactsRole;
    }

    public String getContactsRole() 
    {
        return contactsRole;
    }
    public void setAuthStatus(Integer authStatus) 
    {
        this.authStatus = authStatus;
    }

    public Integer getAuthStatus() 
    {
        return authStatus;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("supplierId", getSupplierId())
            .append("companyName", getCompanyName())
            .append("contactsName", getContactsName())
            .append("contactsPhone", getContactsPhone())
            .append("startValidDate", getStartValidDate())
            .append("endValidDate", getEndValidDate())
            .append("contactsRole", getContactsRole())
            .append("authStatus", getAuthStatus())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
