package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 机场信息对象 sams_airport
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsAirport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 机场ID
     */
    private Long airportId;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场名称
     */
    @Excel(name = "机场名称")
    private String airportName;

    /**
     * 机场编码
     */
    @Excel(name = "机场编码")
    private String airportCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SysDept sysDept;

}
