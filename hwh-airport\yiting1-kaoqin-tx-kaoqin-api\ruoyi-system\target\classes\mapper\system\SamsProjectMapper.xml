<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsProjectMapper">

    <resultMap type="SamsProject" id="SamsProjectResult">
        <result property="projectId"    column="project_id"    />
        <result property="typeRemark"    column="type_remark"    />
        <result property="deptId"    column="dept_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="projectContacts"    column="project_contacts"    />
        <result property="projectContactsPhone"    column="project_contacts_phone"    />
        <result property="other"    column="other"    />
        <result property="otherDept"    column="other_dept"    />
        <result property="otherContacts"    column="other_contacts"    />
        <result property="otherContactsPhone"    column="other_contacts_phone"    />
        <result property="contractNo"    column="contract_no"    />
        <result property="contractTitle"    column="contract_title"    />
        <result property="startContractDate"    column="start_contract_date"    />
        <result property="endContractDate"    column="end_contract_date"    />
        <result property="contractPostNum"    column="contract_post_num"    />
        <result property="contractPostCost"    column="contract_post_cost"    />
        <result property="actualPostNum"    column="actual_post_num"    />
        <result property="actualPostCost"    column="actual_post_cost"    />
        <result property="contractTotalPrice"    column="contract_total_price"    />
        <result property="contractYearPrice"    column="contract_year_price"    />
        <result property="priceDetail"    column="price_detail"    />
        <result property="clockAddress"    column="clock_address"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="clockRange"    column="clock_range"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="gwgs"    column="gwgs"    />
        <result property="qwms"    column="qwms"    />
        <result property="jbbz"    column="jbbz"    />
        <result property="dkfs"    column="dkfs"    />
        <result property="airportId"    column="airport_id"    />
        <result property="areaId"    column="area_id"    />
        <result property="groupId"    column="group_id"    />
    </resultMap>

    <sql id="selectSamsProjectVo">
        select project_id, type_remark, dept_id,gwgs,qwms, project_name, project_contacts, project_contacts_phone, other, other_dept, other_contacts, other_contacts_phone,
               contract_no, contract_title, start_contract_date, end_contract_date, contract_post_num, contract_post_cost, actual_post_num, actual_post_cost,
               contract_total_price, contract_year_price, price_detail, clock_address, latitude, longitude, clock_range, del_flag, create_by, create_time,
               update_by, update_time, remark, jbbz, dkfs, airport_id, area_id, group_id from sams_project
    </sql>

    <select id="selectSamsProjectList" parameterType="SamsProject" resultMap="SamsProjectResult">
        <include refid="selectSamsProjectVo"/>
        <where>
            <if test="typeRemark != null  and typeRemark != ''"> and type_remark = #{typeRemark}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectContacts != null  and projectContacts != ''"> and project_contacts = #{projectContacts}</if>
            <if test="projectContactsPhone != null  and projectContactsPhone != ''"> and project_contacts_phone = #{projectContactsPhone}</if>
            <if test="other != null  and other != ''"> and other = #{other}</if>
            <if test="otherDept != null  and otherDept != ''"> and other_dept like concat('%', #{otherDept}, '%')</if>
            <if test="otherContacts != null  and otherContacts != ''"> and other_contacts = #{otherContacts}</if>
            <if test="otherContactsPhone != null  and otherContactsPhone != ''"> and other_contacts_phone = #{otherContactsPhone}</if>
            <if test="contractNo != null  and contractNo != ''"> and contract_no = #{contractNo}</if>
            <if test="contractTitle != null  and contractTitle != ''"> and contract_title = #{contractTitle}</if>
            <if test="startContractDate != null "> and start_contract_date = #{startContractDate}</if>
            <if test="endContractDate != null "> and end_contract_date = #{endContractDate}</if>
            <if test="contractPostNum != null "> and contract_post_num = #{contractPostNum}</if>
            <if test="contractPostCost != null "> and contract_post_cost = #{contractPostCost}</if>
            <if test="actualPostNum != null "> and actual_post_num = #{actualPostNum}</if>
            <if test="actualPostCost != null "> and actual_post_cost = #{actualPostCost}</if>
            <if test="contractTotalPrice != null "> and contract_total_price = #{contractTotalPrice}</if>
            <if test="contractYearPrice != null "> and contract_year_price = #{contractYearPrice}</if>
            <if test="priceDetail != null  and priceDetail != ''"> and price_detail = #{priceDetail}</if>
            <if test="clockAddress != null  and clockAddress != ''"> and clock_address = #{clockAddress}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="clockRange != null "> and clock_range = #{clockRange}</if>
            <if test="gwgs != null "> and gwgs = #{gwgs}</if>
            <if test="jbbz != null "> and jbbz = #{jbbz}</if>
            <if test="dkfs != null "> and dkfs = #{dkfs}</if>
            <if test="airportId != null"> and airport_id = #{airportId}</if>
            <if test="areaId != null"> and area_id = #{areaId}</if>
            <if test="groupId != null"> and group_id = #{groupId}</if>
        </where>
    </select>

    <select id="kqxmlist" parameterType="long" resultMap="SamsProjectResult">
        select p.* from sams_project p join sams_project_employee e on p.project_id = e.project_id
        where e.employee_id = #{employeeId}
    </select>


    <select id="selectSamsProjectByProjectId" parameterType="Long" resultMap="SamsProjectResult">
        <include refid="selectSamsProjectVo"/>
        where project_id = #{projectId}
    </select>

    <insert id="insertSamsProject" parameterType="SamsProject" useGeneratedKeys="true" keyProperty="projectId">
        insert into sams_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeRemark != null and typeRemark != ''">type_remark,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="projectName != null">project_name,</if>
            <if test="projectContacts != null">project_contacts,</if>
            <if test="projectContactsPhone != null">project_contacts_phone,</if>
            <if test="other != null">other,</if>
            <if test="otherDept != null">other_dept,</if>
            <if test="otherContacts != null">other_contacts,</if>
            <if test="otherContactsPhone != null">other_contacts_phone,</if>
            <if test="contractNo != null">contract_no,</if>
            <if test="contractTitle != null">contract_title,</if>
            <if test="startContractDate != null">start_contract_date,</if>
            <if test="endContractDate != null">end_contract_date,</if>
            <if test="contractPostNum != null">contract_post_num,</if>
            <if test="contractPostCost != null">contract_post_cost,</if>
            <if test="actualPostNum != null">actual_post_num,</if>
            <if test="actualPostCost != null">actual_post_cost,</if>
            <if test="contractTotalPrice != null">contract_total_price,</if>
            <if test="contractYearPrice != null">contract_year_price,</if>
            <if test="priceDetail != null">price_detail,</if>
            <if test="clockAddress != null">clock_address,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="clockRange != null">clock_range,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="gwgs != null">gwgs,</if>
            <if test="qwms != null">qwms,</if>
            <if test="jbbz != null">jbbz,</if>
            <if test="dkfs != null">dkfs,</if>
            <if test="airportId != null">airport_id,</if>
            <if test="areaId != null">area_id,</if>
            <if test="groupId != null">group_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeRemark != null and typeRemark != ''">#{typeRemark},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="projectContacts != null">#{projectContacts},</if>
            <if test="projectContactsPhone != null">#{projectContactsPhone},</if>
            <if test="other != null">#{other},</if>
            <if test="otherDept != null">#{otherDept},</if>
            <if test="otherContacts != null">#{otherContacts},</if>
            <if test="otherContactsPhone != null">#{otherContactsPhone},</if>
            <if test="contractNo != null">#{contractNo},</if>
            <if test="contractTitle != null">#{contractTitle},</if>
            <if test="startContractDate != null">#{startContractDate},</if>
            <if test="endContractDate != null">#{endContractDate},</if>
            <if test="contractPostNum != null">#{contractPostNum},</if>
            <if test="contractPostCost != null">#{contractPostCost},</if>
            <if test="actualPostNum != null">#{actualPostNum},</if>
            <if test="actualPostCost != null">#{actualPostCost},</if>
            <if test="contractTotalPrice != null">#{contractTotalPrice},</if>
            <if test="contractYearPrice != null">#{contractYearPrice},</if>
            <if test="priceDetail != null">#{priceDetail},</if>
            <if test="clockAddress != null">#{clockAddress},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="clockRange != null">#{clockRange},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="gwgs != null">#{gwgs},</if>
            <if test="qwms != null">#{qwms},</if>
            <if test="jbbz != null">#{jbbz},</if>
            <if test="dkfs != null">#{dkfs},</if>
            <if test="airportId != null">#{airportId},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="groupId != null">#{groupId},</if>
         </trim>
    </insert>

    <update id="updateSamsProject" parameterType="SamsProject">
        update sams_project
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeRemark != null and typeRemark != ''">type_remark = #{typeRemark},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="projectContacts != null">project_contacts = #{projectContacts},</if>
            <if test="projectContactsPhone != null">project_contacts_phone = #{projectContactsPhone},</if>
            <if test="other != null">other = #{other},</if>
            <if test="otherDept != null">other_dept = #{otherDept},</if>
            <if test="otherContacts != null">other_contacts = #{otherContacts},</if>
            <if test="otherContactsPhone != null">other_contacts_phone = #{otherContactsPhone},</if>
            <if test="contractNo != null">contract_no = #{contractNo},</if>
            <if test="contractTitle != null">contract_title = #{contractTitle},</if>
            <if test="startContractDate != null">start_contract_date = #{startContractDate},</if>
            <if test="endContractDate != null">end_contract_date = #{endContractDate},</if>
            <if test="contractPostNum != null">contract_post_num = #{contractPostNum},</if>
            <if test="contractPostCost != null">contract_post_cost = #{contractPostCost},</if>
            <if test="actualPostNum != null">actual_post_num = #{actualPostNum},</if>
            <if test="actualPostCost != null">actual_post_cost = #{actualPostCost},</if>
            <if test="contractTotalPrice != null">contract_total_price = #{contractTotalPrice},</if>
            <if test="contractYearPrice != null">contract_year_price = #{contractYearPrice},</if>
            <if test="priceDetail != null">price_detail = #{priceDetail},</if>
            <if test="clockAddress != null">clock_address = #{clockAddress},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="clockRange != null">clock_range = #{clockRange},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="gwgs != null">gwgs = #{gwgs},</if>
            <if test="qwms != null">qwms = #{qwms},</if>
            <if test="jbbz != null">jbbz = #{jbbz},</if>
            <if test="dkfs != null">dkfs = #{dkfs},</if>
            <if test="airportId != null">airport_id = #{airportId},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
        </trim>
        where project_id = #{projectId}
    </update>

    <delete id="deleteSamsProjectByProjectId" parameterType="Long">
        delete from sams_project where project_id = #{projectId}
    </delete>

    <delete id="deleteSamsProjectByProjectIds" parameterType="String">
        delete from sams_project where project_id in
        <foreach item="projectId" collection="array" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </delete>


    <resultMap type="SamsProjectExportVo" id="SamsProjectExportVoResult">
        <result property="projectName"    column="project_name"    />
        <result property="deptName"    column="dept_name"    />
        <result property="airport"    column="airport_name"    />
        <result property="area"    column="area_name"    />
        <result property="other"    column="other"    />
        <result property="typeRemark"    column="type_remark"    />
        <result property="projectContacts"    column="project_contacts"    />
        <result property="projectContactsPhone"    column="project_contacts_phone"    />
        <result property="otherDept"    column="other_dept"    />
        <result property="gwgs"    column="gwgs"    />
        <result property="jbbz"    column="jbbz"    />
        <result property="remark"    column="remark"    />
        <result property="contractNo"    column="contract_no"    />
        <result property="contractTitle"    column="contract_title"    />
        <result property="startContractDate"    column="start_contract_date"    />
        <result property="endContractDate"    column="end_contract_date"    />
    </resultMap>

    <select id="selectSamsProjectExportVoList" parameterType="SamsEmployee" resultMap="SamsProjectExportVoResult">
        select sp.project_name, sp.other, sp.type_remark, sp.project_contacts, sp.project_contacts_phone, sp.other_dept,
            sp.gwgs, case when sp.jbbz = 1 then '不是' when sp.jbbz = 2 then '是' end as jbbz, sp.remark,
            sd.dept_name, sa.airport_name, saa.area_name, sp.contract_no, sp.contract_title, sp.start_contract_date, sp.end_contract_date
        from sams_project sp
        left join sys_dept sd on sp.dept_id = sd.dept_id
        left join sams_airport sa on sp.airport_id = sa.airport_id
        left join sams_airport_area saa on sp.area_id = saa.area_id
        <where>
            <if test="typeRemark != null  and typeRemark != ''"> and sp.type_remark = #{typeRemark}</if>
            <if test="deptId != null "> and sp.dept_id = #{deptId}</if>
            <if test="projectName != null  and projectName != ''"> and sp.project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectContacts != null  and projectContacts != ''"> and sp.project_contacts = #{projectContacts}</if>
            <if test="projectContactsPhone != null  and projectContactsPhone != ''"> and sp.project_contacts_phone = #{projectContactsPhone}</if>
            <if test="other != null  and other != ''"> and sp.other = #{other}</if>
            <if test="otherDept != null  and otherDept != ''"> and sp.other_dept like concat('%', #{otherDept}, '%')</if>
            <if test="otherContacts != null  and otherContacts != ''"> and sp.other_contacts = #{otherContacts}</if>
            <if test="otherContactsPhone != null  and otherContactsPhone != ''"> and sp.other_contacts_phone = #{otherContactsPhone}</if>
            <if test="contractNo != null  and contractNo != ''"> and sp.contract_no = #{contractNo}</if>
            <if test="contractTitle != null  and contractTitle != ''"> and sp.contract_title = #{contractTitle}</if>
            <if test="startContractDate != null "> and sp.start_contract_date = #{startContractDate}</if>
            <if test="endContractDate != null "> and sp.end_contract_date = #{endContractDate}</if>
            <if test="contractPostNum != null "> and sp.contract_post_num = #{contractPostNum}</if>
            <if test="contractPostCost != null "> and sp.contract_post_cost = #{contractPostCost}</if>
            <if test="actualPostNum != null "> and sp.actual_post_num = #{actualPostNum}</if>
            <if test="actualPostCost != null "> and sp.actual_post_cost = #{actualPostCost}</if>
            <if test="contractTotalPrice != null "> and sp.contract_total_price = #{contractTotalPrice}</if>
            <if test="contractYearPrice != null "> and sp.contract_year_price = #{contractYearPrice}</if>
            <if test="priceDetail != null  and priceDetail != ''"> and sp.price_detail = #{priceDetail}</if>
            <if test="clockAddress != null  and clockAddress != ''"> and sp.clock_address = #{clockAddress}</if>
            <if test="latitude != null  and latitude != ''"> and sp.latitude = #{latitude}</if>
            <if test="longitude != null  and longitude != ''"> and sp.longitude = #{longitude}</if>
            <if test="clockRange != null "> and sp.clock_range = #{clockRange}</if>
            <if test="gwgs != null "> and sp.gwgs = #{gwgs}</if>
            <if test="jbbz != null "> and sp.jbbz = #{jbbz}</if>
            <if test="dkfs != null "> and sp.dkfs = #{dkfs}</if>
            <if test="airportId != null"> and sp.airport_id = #{airportId}</if>
            <if test="areaId != null"> and sp.area_id = #{areaId}</if>
            <if test="groupId != null"> and sp.group_id = #{groupId}</if>
        </where>
    </select>

</mapper>
