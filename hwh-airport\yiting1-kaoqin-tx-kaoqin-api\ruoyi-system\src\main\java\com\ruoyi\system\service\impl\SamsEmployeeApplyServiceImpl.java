package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.SamsEmployeeApply;
import com.ruoyi.system.domain.SamsPost;
import com.ruoyi.system.domain.SamsProject;
import com.ruoyi.system.domain.SamsSchedulingEmployee;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxQueryVo;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsEmployeeApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 员工申请信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Service
public class SamsEmployeeApplyServiceImpl implements ISamsEmployeeApplyService {
    @Autowired
    private SamsEmployeeApplyMapper samsEmployeeApplyMapper;
    @Autowired
    private SamsProjectEmployeeMapper samsProjectEmployeeMapper;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SamsProjectMapper samsProjectMapper;
    @Autowired
    private SamsPostMapper samsPostMapper;
    @Autowired
    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;
    @Autowired
    private SamsSchedulingKqfxMapper samsSchedulingKqfxMapper;

    /**
     * 查询员工申请信息
     *
     * @param employeeApplyId 员工申请信息主键
     * @return 员工申请信息
     */
    @Override
    public SamsEmployeeApply selectSamsEmployeeApplyByEmployeeApplyId(Long employeeApplyId) {
        return samsEmployeeApplyMapper.selectSamsEmployeeApplyByEmployeeApplyId(employeeApplyId);
    }

    /**
     * 查询员工申请信息列表
     *
     * @param samsEmployeeApply 员工申请信息
     * @return 员工申请信息
     */
    @Override
    public List<SamsEmployeeApply> selectSamsEmployeeApplyList(SamsEmployeeApply samsEmployeeApply) {
        List<SamsEmployeeApply> list = samsEmployeeApplyMapper.selectSamsEmployeeApplyList(samsEmployeeApply);
//        if (list != null && list.size() > 0) {
//            for (SamsEmployeeApply employeeApply : list) {
//                SamsProject samsProject = samsProjectMapper.selectSamsProjectByProjectId(employeeApply.getProjectId());
//                employeeApply.setSamsProject(samsProject);
//                SamsPost samsPost = samsPostMapper.selectSamsPostByPostId(employeeApply.getPostId());
//                employeeApply.setSamsPost(samsPost);
//                SamsEmployee samsEmployee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(employeeApply.getEmployeeId());
//                employeeApply.setSamsEmployee(samsEmployee);
//                employeeApply.setSysDept(sysDeptMapper.selectDeptById(samsEmployee.getDeptId()));
//            }
//        }
        return list;
    }

    /**
     * 新增员工申请信息
     *
     * @param samsEmployeeApply 员工申请信息
     * @return 结果
     */
    @Override
    public int insertSamsEmployeeApply(SamsEmployeeApply samsEmployeeApply) {
        samsEmployeeApply.setCreateTime(DateUtils.getNowDate());
        return samsEmployeeApplyMapper.insertSamsEmployeeApply(samsEmployeeApply);
    }

    /**
     * 修改员工申请信息
     *
     * @param samsEmployeeApply 员工申请信息
     * @return 结果
     */
    @Override
    public int updateSamsEmployeeApply(SamsEmployeeApply samsEmployeeApply) {
        samsEmployeeApply.setUpdateTime(DateUtils.getNowDate());
        // 审核处理 1 通过 考勤状态变更
        if (samsEmployeeApply.getAuthStatus() == 1) {
            // 查询考勤信息
            SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
            samsSchedulingEmployee.setDutyDate(samsEmployeeApply.getApplyTime());
            samsSchedulingEmployee.setPostId(samsEmployeeApply.getPostId());
            samsSchedulingEmployee.setEmployeeId(samsEmployeeApply.getEmployeeId());
            samsSchedulingEmployee.setDelFlag("0");

            List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectByEmployeeId3(samsSchedulingEmployee);

            if (schedulingEmployeeList != null && !schedulingEmployeeList.isEmpty()) {
                // 处理考勤分析
                SamsSchedulingKqfxQueryVo queryVo = new SamsSchedulingKqfxQueryVo();
                queryVo.setDeptId(samsEmployeeApply.getSamsProject().getDeptId());
                queryVo.setPostId(samsEmployeeApply.getPostId());
                queryVo.setEmployeeId(samsEmployeeApply.getEmployeeId());
                queryVo.setCxny(DateUtils.parseDateToStr("yyyy-MM", org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -1)));
                SamsSchedulingKqfx samsSchedulingKqfx = samsSchedulingKqfxMapper.selectByQueryVo(queryVo);
                //            申请的日期大于考勤分析报表的更新时间就不处理考勤分析统计
                if (samsEmployeeApply.getApplyTime().before(samsSchedulingKqfx.getUpdateTime())) {
                    boolean kgFlag = false;
                    boolean cdFlag = false;
                    boolean ztFlag = false;
                    // 查询考勤记录判断是迟到还是旷工
                    if (schedulingEmployeeList != null && !schedulingEmployeeList.isEmpty()) {
                        for (SamsSchedulingEmployee schedulingEmployee : schedulingEmployeeList) {
                            if (schedulingEmployee.getDkStatus() == null || schedulingEmployee.getDkStatus() == 6 || schedulingEmployee.getDkStatus() == 7) {// 旷工
                                kgFlag = true;
                            } else if (schedulingEmployee.getDkStatus() == 3) {// 迟到
                                cdFlag = true;
                            }else if (schedulingEmployee.getDkStatus() == 5) {// 早退
                                ztFlag = true;
                            }
                        }
                    }

                    //                考勤分析统计
                    samsSchedulingKqfx.setKgcs(kgFlag ? samsSchedulingKqfx.getKgcs() - 1 : samsSchedulingKqfx.getKgcs());
                    samsSchedulingKqfx.setCdcs(cdFlag ? samsSchedulingKqfx.getCdcs() - 1 : samsSchedulingKqfx.getCdcs());
                    samsSchedulingKqfx.setZtcs(ztFlag ? samsSchedulingKqfx.getZtcs() - 1 : samsSchedulingKqfx.getZtcs());
                    samsSchedulingKqfx.setUpdateTime(new Date());
                    samsSchedulingKqfxMapper.updateSamsSchedulingKqfx(samsSchedulingKqfx);
                }

                for (SamsSchedulingEmployee schedulingEmployee : schedulingEmployeeList) {
                    schedulingEmployee.setDkStatus(1);
                    schedulingEmployee.setAuthTime(DateUtils.getNowDate());
                    schedulingEmployee.setAuthStatus(samsEmployeeApply.getAuthStatus());
                    schedulingEmployee.setAuthRemark(samsEmployeeApply.getApplyReson());
                    samsSchedulingEmployeeMapper.updateSamsSchedulingEmployee(schedulingEmployee);
                }
            }

        }
        return samsEmployeeApplyMapper.updateSamsEmployeeApply(samsEmployeeApply);
    }

    public static void main(String[] args) {
        System.out.println(DateUtils.parseDate("2025-02-11").before(DateUtils.parseDate("2025-02-15 01:00:00")));
    }

    /**
     * 批量删除员工申请信息
     *
     * @param employeeApplyIds 需要删除的员工申请信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsEmployeeApplyByEmployeeApplyIds(Long[] employeeApplyIds) {
        return samsEmployeeApplyMapper.deleteSamsEmployeeApplyByEmployeeApplyIds(employeeApplyIds);
    }

    /**
     * 删除员工申请信息信息
     *
     * @param employeeApplyId 员工申请信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsEmployeeApplyByEmployeeApplyId(Long employeeApplyId) {
        return samsEmployeeApplyMapper.deleteSamsEmployeeApplyByEmployeeApplyId(employeeApplyId);
    }
}
