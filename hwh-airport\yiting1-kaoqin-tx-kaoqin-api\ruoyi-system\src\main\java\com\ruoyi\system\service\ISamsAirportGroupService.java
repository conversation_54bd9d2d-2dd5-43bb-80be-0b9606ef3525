package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsAirportGroup;

/**
 * 机场大队信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface ISamsAirportGroupService 
{
    /**
     * 查询机场大队信息
     * 
     * @param groupId 机场大队信息主键
     * @return 机场大队信息
     */
    public SamsAirportGroup selectSamsAirportGroupByGroupId(Long groupId);

    /**
     * 查询机场大队信息列表
     * 
     * @param samsAirportGroup 机场大队信息
     * @return 机场大队信息集合
     */
    public List<SamsAirportGroup> selectSamsAirportGroupList(SamsAirportGroup samsAirportGroup);

    /**
     * 新增机场大队信息
     * 
     * @param samsAirportGroup 机场大队信息
     * @return 结果
     */
    public int insertSamsAirportGroup(SamsAirportGroup samsAirportGroup);

    /**
     * 修改机场大队信息
     * 
     * @param samsAirportGroup 机场大队信息
     * @return 结果
     */
    public int updateSamsAirportGroup(SamsAirportGroup samsAirportGroup);

    /**
     * 批量删除机场大队信息
     * 
     * @param groupIds 需要删除的机场大队信息主键集合
     * @return 结果
     */
    public int deleteSamsAirportGroupByGroupIds(Long[] groupIds);

    /**
     * 删除机场大队信息信息
     * 
     * @param groupId 机场大队信息主键
     * @return 结果
     */
    public int deleteSamsAirportGroupByGroupId(Long groupId);
}
