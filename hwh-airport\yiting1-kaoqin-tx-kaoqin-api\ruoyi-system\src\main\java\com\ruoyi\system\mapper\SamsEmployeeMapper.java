package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.domain.vo.SamsEmployeeExportVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsEmployeeMapper {
    /**
     * 查询员工信息
     *
     * @param employeeId 员工信息主键
     * @return 员工信息
     */
    public SamsEmployee selectSamsEmployeeByEmployeeId(Long employeeId);

    SamsEmployee selectByBindPhone(String phone);

    /**
     * 查询员工信息列表
     *
     * @param samsEmployee 员工信息
     * @return 员工信息集合
     */
    public List<SamsEmployee> selectSamsEmployeeList(SamsEmployee samsEmployee);

    /**
     * 查询员工信息列表(去掉项目绑定的)
     *
     * @param samsEmployee 员工信息
     * @return 员工信息集合
     */
    public List<SamsEmployee> selectSamsEmployeeList2(SamsEmployee samsEmployee);
    int totalRs();

    /**
     * 查询指定年龄的人数
     * @param age1
     * @param age2
     * @return
     */
    int cxnlrys(@Param("age1") Integer age1,@Param("age2")Integer age2);

    /**
     * 查询指定学历的人数
     * @param education
     * @return
     */
    int cxxlrys(Integer education);
    /**
     *
     * @param sex
     * @return
     */
    int nxRs(Integer sex);

    int typeRs(Integer typeName);
    /**
     * 新增员工信息
     *
     * @param samsEmployee 员工信息
     * @return 结果
     */
    public int insertSamsEmployee(SamsEmployee samsEmployee);

    /**
     * 修改员工信息
     *
     * @param samsEmployee 员工信息
     * @return 结果
     */
    public int updateSamsEmployee(SamsEmployee samsEmployee);

    int updateAvate(@Param("openId") String openId, @Param("avate") String avate);

    /**
     * 解除微信绑定
     * @param employeeId
     * @return
     */
    int jcwxbd(Long employeeId);
    /**
     * 删除员工信息
     *
     * @param employeeId 员工信息主键
     * @return 结果
     */
    public int deleteSamsEmployeeByEmployeeId(Long employeeId);

    /**
     * 批量删除员工信息
     *
     * @param employeeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsEmployeeByEmployeeIds(Long[] employeeIds);

    List<SamsEmployeeExportVo> selectSamsEmployeeExportVoList(SamsEmployee samsEmployee);
}
