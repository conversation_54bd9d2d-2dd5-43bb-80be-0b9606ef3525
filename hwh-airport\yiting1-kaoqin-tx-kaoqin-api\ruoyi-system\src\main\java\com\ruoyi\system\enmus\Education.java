package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 学历
 *
 * <AUTHOR>
 */
public enum Education
{
    PRIMARY_SCHOOL(0, "小学"),
    MIDDLE_SCHOOL(1, "初中"),
    HIGH_SCHOOL(2, "高中"),
    JUNIOR_COLLEGE(3, "大专"),
    UNDERGRADUATE(4, "本科"),
    MASTER(5, "硕士"),
    DOCTOR(7, "博士");

    private final Integer code;
    private final String info;

    Education(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static Education getEducation(Integer code) {
        for (Education education : Education.values()) {
            if (Objects.equals(education.getCode(), code)) {
                return education;
            }
        }
        return null;
    }

    public static Education getEducation(String info) {
        for (Education education : Education.values()) {
            if (Objects.equals(education.getInfo(), info)) {
                return education;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
