
package com.ruoyi.common.utils;

import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import com.tencentcloudapi.iai.v20200303.models.*;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Slf4j
public class TxRlsbUtils {
    private static IaiClient client;

    static {
        // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
        // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
        // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
//        Credential cred = new Credential("AKID52jIoBKF1Zt0nNlvsqqdCZljT6lGZ1X7", "rPn482nNnzyjIdkofKTQEqFcy98wc8Uc"); //原登弘
        Credential cred = new Credential("AKIDsa5MQL3Zv9XiiV5ScaNxMYWIsHVWKvIS", "zlJeokTyGLEwTmslW5RaXIp9FF4jAORH");
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("iai.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        client = new IaiClient(cred, "ap-shanghai", clientProfile);
    }

    /**
     * 人脸识别
     *
     * @param userId
     * @param url
     * @return
     */
    public static VerifyPersonResponse verifyPerson(String userId, String url) {
        log.info("开始人脸识别 {},{}", userId, url);
        try {
            // 实例化一个请求对象,每个接口都会对应一个request对象
            VerifyPersonRequest req = new VerifyPersonRequest();
            req.setPersonId(userId);
            url = url.substring(0, url.lastIndexOf("/")) + "/" + URLEncoder.encode(url.substring(url.lastIndexOf("/") + 1), "UTF-8");
            req.setUrl(url);
            // 返回的resp是一个VerifyPersonResponse的实例，与请求对象对应
            VerifyPersonResponse resp = client.VerifyPerson(req);
            // 输出json格式的字符串回包
            log.info(AbstractModel.toJsonString(resp));
            return resp;
        } catch (Exception e) {
            log.error("人脸识别失败", e);
        }
        return null;
    }

    /**
     * 新增员工到腾讯
     *
     * @param name
     * @param url
     * @param userId
     */
    public static void addPerson(String name, String url, String userId) {
        log.info("开始新增腾讯人脸 {}，{}，{}", name, url, userId);
        try {
            // 实例化一个请求对象,每个接口都会对应一个request对象
            CreatePersonRequest req = new CreatePersonRequest();
            req.setGroupId("1");
            req.setPersonName(name);
            req.setPersonId(userId);
            url = url.substring(0, url.lastIndexOf("/")) + "/" + URLEncoder.encode(url.substring(url.lastIndexOf("/") + 1), "UTF-8");
            req.setUrl(url);
            // 返回的resp是一个CreatePersonResponse的实例，与请求对象对应
            CreatePersonResponse resp = client.CreatePerson(req);
            // 输出json格式的字符串回包
            log.info(AbstractModel.toJsonString(resp));
        } catch (Exception e) {
            log.error("新增腾讯人脸失败", e);
        }
    }

    /**
     * 删除腾讯人脸关联人
     *
     * @param userId
     */
    public static void deletePerson(String userId) {
        log.info("开始删除腾讯人脸 {}", userId);
        try {
            // 实例化一个请求对象,每个接口都会对应一个request对象
            DeletePersonRequest req = new DeletePersonRequest();
            req.setPersonId(userId);
            // 返回的resp是一个DeletePersonResponse的实例，与请求对象对应
            DeletePersonResponse resp = client.DeletePerson(req);
            // 输出json格式的字符串回包
            log.info(AbstractModel.toJsonString(resp));
        } catch (Exception e) {
            log.error("删除腾讯人脸失败", e);
        }
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String url = "https://huitiaokeji.oss-cn-shenzhen.aliyuncs.com/2021_4/微信图片_20240626094250.jpg";
        url = url.substring(0, url.lastIndexOf("/")) + "/" + URLEncoder.encode(url.substring(url.lastIndexOf("/") + 1), "UTF-8");
        System.out.println(url);
    }
}
