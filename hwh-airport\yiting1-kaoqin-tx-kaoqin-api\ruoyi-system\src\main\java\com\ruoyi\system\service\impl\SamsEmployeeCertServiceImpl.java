package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SamsEmployeeCertMapper;
import com.ruoyi.common.core.domain.entity.SamsEmployeeCert;
import com.ruoyi.system.service.ISamsEmployeeCertService;

/**
 * 员工证书信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-15
 */
@Service
public class SamsEmployeeCertServiceImpl implements ISamsEmployeeCertService 
{
    @Autowired
    private SamsEmployeeCertMapper samsEmployeeCertMapper;

    /**
     * 查询员工证书信息
     * 
     * @param employeeCertId 员工证书信息主键
     * @return 员工证书信息
     */
    @Override
    public SamsEmployeeCert selectSamsEmployeeCertByEmployeeCertId(Long employeeCertId)
    {
        return samsEmployeeCertMapper.selectSamsEmployeeCertByEmployeeCertId(employeeCertId);
    }

    /**
     * 查询员工证书信息列表
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 员工证书信息
     */
    @Override
    public List<SamsEmployeeCert> selectSamsEmployeeCertList(SamsEmployeeCert samsEmployeeCert)
    {
        return samsEmployeeCertMapper.selectSamsEmployeeCertList(samsEmployeeCert);
    }

    /**
     * 新增员工证书信息
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 结果
     */
    @Override
    public int insertSamsEmployeeCert(SamsEmployeeCert samsEmployeeCert)
    {
        samsEmployeeCert.setCreateTime(DateUtils.getNowDate());
        return samsEmployeeCertMapper.insertSamsEmployeeCert(samsEmployeeCert);
    }

    /**
     * 修改员工证书信息
     * 
     * @param samsEmployeeCert 员工证书信息
     * @return 结果
     */
    @Override
    public int updateSamsEmployeeCert(SamsEmployeeCert samsEmployeeCert)
    {
        samsEmployeeCert.setUpdateTime(DateUtils.getNowDate());
        return samsEmployeeCertMapper.updateSamsEmployeeCert(samsEmployeeCert);
    }

    /**
     * 批量删除员工证书信息
     * 
     * @param employeeCertIds 需要删除的员工证书信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsEmployeeCertByEmployeeCertIds(Long[] employeeCertIds)
    {
        return samsEmployeeCertMapper.deleteSamsEmployeeCertByEmployeeCertIds(employeeCertIds);
    }

    /**
     * 删除员工证书信息信息
     * 
     * @param employeeCertId 员工证书信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsEmployeeCertByEmployeeCertId(Long employeeCertId)
    {
        return samsEmployeeCertMapper.deleteSamsEmployeeCertByEmployeeCertId(employeeCertId);
    }
}
