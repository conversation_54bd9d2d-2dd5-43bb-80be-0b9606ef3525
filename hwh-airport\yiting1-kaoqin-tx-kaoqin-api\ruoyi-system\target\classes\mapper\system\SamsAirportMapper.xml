<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsAirportMapper">

    <resultMap type="SamsAirport" id="SamsAirportResult">
        <result property="airportId"    column="airport_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="airportName"    column="airport_name"    />
        <result property="airportCode"    column="airport_code"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSamsAirportVo">
        select airport_id, dept_id, airport_name, airport_code, del_flag, create_by, create_time, update_by, update_time, remark from sams_airport
    </sql>

    <select id="selectSamsAirportList" parameterType="SamsAirport" resultMap="SamsAirportResult">
        <include refid="selectSamsAirportVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="airportName != null  and airportName != ''"> and airport_name like concat('%', #{airportName}, '%')</if>
            <if test="airportCode != null  and airportCode != ''"> and airport_code = #{airportCode}</if>
        </where>
    </select>

    <select id="selectSamsAirportByAirportId" parameterType="Long" resultMap="SamsAirportResult">
        <include refid="selectSamsAirportVo"/>
        where airport_id = #{airportId}
    </select>

    <select id="countAirport" resultType="integer">
        select count(1) from sams_airport
    </select>
    <select id="selectSamsAirportByDeptIdAndAirportName"
            resultMap="SamsAirportResult">
        <include refid="selectSamsAirportVo"/>
        <where>
            <if test="deptId != null">dept_id = #{deptId}</if>
            <if test="airport != null  and airport != ''"> and airport_name = #{airport}</if>
        </where>
    </select>

    <insert id="insertSamsAirport" parameterType="SamsAirport" useGeneratedKeys="true" keyProperty="airportId">
        insert into sams_airport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="airportName != null">airport_name,</if>
            <if test="airportCode != null">airport_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="airportName != null">#{airportName},</if>
            <if test="airportCode != null">#{airportCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsAirport" parameterType="SamsAirport">
        update sams_airport
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="airportName != null">airport_name = #{airportName},</if>
            <if test="airportCode != null">airport_code = #{airportCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where airport_id = #{airportId}
    </update>

    <delete id="deleteSamsAirportByAirportId" parameterType="Long">
        delete from sams_airport where airport_id = #{airportId}
    </delete>

    <delete id="deleteSamsAirportByAirportIds" parameterType="String">
        delete from sams_airport where airport_id in
        <foreach item="airportId" collection="array" open="(" separator="," close=")">
            #{airportId}
        </foreach>
    </delete>
</mapper>
