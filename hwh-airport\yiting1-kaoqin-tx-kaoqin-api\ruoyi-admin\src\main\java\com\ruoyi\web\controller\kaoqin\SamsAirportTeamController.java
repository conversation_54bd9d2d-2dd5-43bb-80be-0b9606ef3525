package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SamsAirportTeam;
import com.ruoyi.system.service.ISamsAirportTeamService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 机场组别信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/airportTeam")
public class SamsAirportTeamController extends BaseController
{
    @Autowired
    private ISamsAirportTeamService samsAirportTeamService;

    /**
     * 查询机场组别信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirportTeam samsAirportTeam)
    {
        startPage();
        List<SamsAirportTeam> list = samsAirportTeamService.selectSamsAirportTeamList(samsAirportTeam);
        return getDataTable(list);
    }

    /**
     * 导出机场组别信息列表
     */
    @Log(title = "机场组别信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsAirportTeam samsAirportTeam)
    {
        List<SamsAirportTeam> list = samsAirportTeamService.selectSamsAirportTeamList(samsAirportTeam);
        ExcelUtil<SamsAirportTeam> util = new ExcelUtil<SamsAirportTeam>(SamsAirportTeam.class);
        util.exportExcel(response, list, "机场组别信息数据");
    }

    /**
     * 获取机场组别信息详细信息
     */
    @GetMapping(value = "/{teamId}")
    public AjaxResult getInfo(@PathVariable("teamId") Long teamId)
    {
        return success(samsAirportTeamService.selectSamsAirportTeamByTeamId(teamId));
    }

    /**
     * 新增机场组别信息
     */
    @Log(title = "机场组别信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsAirportTeam samsAirportTeam)
    {
        return toAjax(samsAirportTeamService.insertSamsAirportTeam(samsAirportTeam));
    }

    /**
     * 修改机场组别信息
     */
    @Log(title = "机场组别信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsAirportTeam samsAirportTeam)
    {
        return toAjax(samsAirportTeamService.updateSamsAirportTeam(samsAirportTeam));
    }

    /**
     * 删除机场组别信息
     */
    @Log(title = "机场组别信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teamIds}")
    public AjaxResult remove(@PathVariable Long[] teamIds)
    {
        return toAjax(samsAirportTeamService.deleteSamsAirportTeamByTeamIds(teamIds));
    }
}
