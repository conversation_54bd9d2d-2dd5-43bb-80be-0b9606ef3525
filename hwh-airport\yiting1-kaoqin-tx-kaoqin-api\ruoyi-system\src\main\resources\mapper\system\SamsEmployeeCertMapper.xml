<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsEmployeeCertMapper">
    
    <resultMap type="SamsEmployeeCert" id="SamsEmployeeCertResult">
        <result property="employeeCertId"    column="employee_cert_id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="certDict"    column="cert_dict"    />
        <result property="certName"    column="cert_name"    />
        <result property="certNo"    column="cert_no"    />
        <result property="certVali"    column="cert_vali"    />
        <result property="certImg"    column="cert_img"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="certLevel"    column="cert_level"    />
    </resultMap>

    <sql id="selectSamsEmployeeCertVo">
        select employee_cert_id, employee_id, cert_dict, cert_name, cert_no, cert_vali, cert_img, del_flag, create_by, create_time, update_by, update_time, remark,cert_level from sams_employee_cert
    </sql>

    <select id="selectSamsEmployeeCertList" parameterType="SamsEmployeeCert" resultMap="SamsEmployeeCertResult">
        <include refid="selectSamsEmployeeCertVo"/>
        <where>  
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="certDict != null  and certDict != ''"> and cert_dict = #{certDict}</if>
            <if test="certName != null  and certName != ''"> and cert_name like concat('%', #{certName}, '%')</if>
            <if test="certNo != null  and certNo != ''"> and cert_no = #{certNo}</if>
            <if test="certVali != null  and certVali != ''"> and cert_vali = #{certVali}</if>
            <if test="certImg != null  and certImg != ''"> and cert_img = #{certImg}</if>
        </where>
    </select>
    
    <select id="selectSamsEmployeeCertByEmployeeCertId" parameterType="Long" resultMap="SamsEmployeeCertResult">
        <include refid="selectSamsEmployeeCertVo"/>
        where employee_cert_id = #{employeeCertId}
    </select>

    <select id="countNum" parameterType="string" resultType="integer">
        select count(1) from sams_employee_cert where 1=1
        <if test="certDict != null  and certDict != ''"> and cert_dict = #{certDict}</if>
    </select>

    <insert id="insertSamsEmployeeCert" parameterType="SamsEmployeeCert" useGeneratedKeys="true" keyProperty="employeeCertId">
        insert into sams_employee_cert
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">employee_id,</if>
            <if test="certDict != null">cert_dict,</if>
            <if test="certName != null">cert_name,</if>
            <if test="certNo != null">cert_no,</if>
            <if test="certVali != null">cert_vali,</if>
            <if test="certImg != null">cert_img,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="certLevel != null">cert_level,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeId != null">#{employeeId},</if>
            <if test="certDict != null">#{certDict},</if>
            <if test="certName != null">#{certName},</if>
            <if test="certNo != null">#{certNo},</if>
            <if test="certVali != null">#{certVali},</if>
            <if test="certImg != null">#{certImg},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="certLevel != null">#{certLevel},</if>
         </trim>
    </insert>

    <update id="updateSamsEmployeeCert" parameterType="SamsEmployeeCert">
        update sams_employee_cert
        <trim prefix="SET" suffixOverrides=",">
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="certDict != null">cert_dict = #{certDict},</if>
            <if test="certName != null">cert_name = #{certName},</if>
            <if test="certNo != null">cert_no = #{certNo},</if>
            <if test="certVali != null">cert_vali = #{certVali},</if>
            <if test="certImg != null">cert_img = #{certImg},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="certLevel != null">cert_level = #{certLevel},</if>
        </trim>
        where employee_cert_id = #{employeeCertId}
    </update>

    <delete id="deleteSamsEmployeeCertByEmployeeCertId" parameterType="Long">
        delete from sams_employee_cert where employee_cert_id = #{employeeCertId}
    </delete>


    <delete id="deleteByEmployeeId" parameterType="Long">
        delete from sams_employee_cert where employee_id = #{employeeId}
    </delete>

    <delete id="deleteSamsEmployeeCertByEmployeeCertIds" parameterType="String">
        delete from sams_employee_cert where employee_cert_id in 
        <foreach item="employeeCertId" collection="array" open="(" separator="," close=")">
            #{employeeCertId}
        </foreach>
    </delete>
</mapper>