package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SamsProject;
import com.ruoyi.system.domain.SamsScheduling;
import com.ruoyi.system.domain.SamsSchedulingEmployee;
import com.ruoyi.system.domain.vo.SamsSchedulingEmployeeAuthVo;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxQueryVo;
import com.ruoyi.system.mapper.SamsProjectMapper;
import com.ruoyi.system.mapper.SamsSchedulingEmployeeMapper;
import com.ruoyi.system.mapper.SamsSchedulingKqfxMapper;
import com.ruoyi.system.mapper.SamsSchedulingMapper;
import com.ruoyi.system.service.ISamsSchedulingEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工排班信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@RestController
@RequestMapping("/system/schedulingEmployee")
public class SamsSchedulingEmployeeController extends BaseController {
    @Autowired
    private ISamsSchedulingEmployeeService samsSchedulingEmployeeService;
    @Autowired
    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;
    @Autowired
    private SamsSchedulingKqfxMapper samsSchedulingKqfxMapper;
    @Autowired
    private SamsProjectMapper samsProjectMapper;
    @Autowired
    private SamsSchedulingMapper samsSchedulingMapper;

    /**
     * 查询员工排班信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsSchedulingEmployee samsSchedulingEmployee) {
        startPage();
        List<SamsSchedulingEmployee> list = samsSchedulingEmployeeService.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
        return getDataTable(list);
    }

    /**
     * 导出员工排班信息列表
     */
    @Log(title = "员工排班信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsSchedulingEmployee samsSchedulingEmployee) {
        List<SamsSchedulingEmployee> list = samsSchedulingEmployeeService.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
        ExcelUtil<SamsSchedulingEmployee> util = new ExcelUtil<SamsSchedulingEmployee>(SamsSchedulingEmployee.class);
        util.exportExcel(response, list, "员工排班信息数据");
    }

    /**
     * 获取员工排班信息详细信息
     */
    @GetMapping(value = "/{schedulingEmployeeId}")
    public AjaxResult getInfo(@PathVariable("schedulingEmployeeId") Long schedulingEmployeeId) {
        return success(samsSchedulingEmployeeService.selectSamsSchedulingEmployeeBySchedulingEmployeeId(schedulingEmployeeId));
    }

    /**
     * 新增员工排班信息
     */
    @Log(title = "员工排班信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsSchedulingEmployee samsSchedulingEmployee) {
        return toAjax(samsSchedulingEmployeeService.insertSamsSchedulingEmployee(samsSchedulingEmployee));
    }

    /**
     * 修改员工排班信息
     */
    @Log(title = "员工排班信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsSchedulingEmployee samsSchedulingEmployee) {
        return toAjax(samsSchedulingEmployeeService.updateSamsSchedulingEmployee(samsSchedulingEmployee));
    }

    /**
     * 考勤记录审批
     *
     * @param samsSchedulingEmployeeAuthVo
     * @return
     */
    @PutMapping("/auth")
    public AjaxResult auth(@RequestBody SamsSchedulingEmployeeAuthVo samsSchedulingEmployeeAuthVo) {
        SamsSchedulingEmployee samsSchedulingEmployee = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeBySchedulingEmployeeId(samsSchedulingEmployeeAuthVo.getSchedulingEmployeeId());
        samsSchedulingEmployee.setAuthTime(new Date());
        samsSchedulingEmployee.setAuthStatus(samsSchedulingEmployeeAuthVo.getAuthStatus());
        samsSchedulingEmployee.setAuthRemark(samsSchedulingEmployeeAuthVo.getAuthRemark());
        samsSchedulingEmployee.setKqtz(samsSchedulingEmployeeAuthVo.getKqtz());

        if (samsSchedulingEmployeeAuthVo.getAuthStatus() == 1 && "5".equals(samsSchedulingEmployeeAuthVo.getKqtz())) {
            // 处理考勤分析
            SamsSchedulingKqfxQueryVo queryVo = new SamsSchedulingKqfxQueryVo();
            // 根据排班查询部门id
            SamsScheduling samsScheduling = samsSchedulingMapper.selectSamsSchedulingBySchedulingId(samsSchedulingEmployee.getSchedulingId());
            queryVo.setDeptId(samsScheduling.getDeptId());
            queryVo.setPostId(samsScheduling.getPostId());
            queryVo.setEmployeeId(samsSchedulingEmployee.getEmployeeId());
            queryVo.setCxny(samsSchedulingEmployee.getScheduleDate().substring(0,7));
//            queryVo.setCxny(DateUtils.parseDateToStr("yyyy-MM", org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -1)));
            SamsSchedulingKqfx samsSchedulingKqfx = samsSchedulingKqfxMapper.selectByQueryVo(queryVo);
            if (samsSchedulingKqfx != null) {
                Date nowDate = new Date();
                if (samsSchedulingKqfx.getUpdateTime() == null ? nowDate.after(samsSchedulingKqfx.getCreateTime()) : nowDate.after(samsSchedulingKqfx.getUpdateTime())) {
                    // 根据当前记录处理考勤分析报表：旷工需要两条记录都处理，迟到只处理一条记录，早退只处理一条记录
                    if (samsSchedulingEmployee.getDkStatus() == null || samsSchedulingEmployee.getDkStatus() == 6 || samsSchedulingEmployee.getDkStatus() == 7) {
                        // 查询对应得上班或下班记录
                        List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectByDkTag(samsSchedulingEmployee.getDkTag());
                        // 过滤掉当前审批得数据
                        List<SamsSchedulingEmployee> newList = schedulingEmployeeList.stream().filter(schedulingEmployee -> !schedulingEmployee.getSchedulingEmployeeId().equals(samsSchedulingEmployee.getSchedulingEmployeeId())).collect(Collectors.toList());
                        // 如果剩下得数据打卡状态都为成功，则处理考勤分析报表
                        List<SamsSchedulingEmployee> samsSchedulingEmployees = newList.stream().filter(schedulingEmployee -> schedulingEmployee.getDkStatus()==null || schedulingEmployee.getDkStatus()!= 1).collect(Collectors.toList());
                        if (samsSchedulingEmployees.isEmpty() && samsSchedulingKqfx.getKgcs() > 0) {
                            // 旷工
                            samsSchedulingKqfx.setKgcs(samsSchedulingKqfx.getKgcs() - 1);
                        }
                    } else if (samsSchedulingEmployee.getDkStatus() == 5 && samsSchedulingKqfx.getZtcs() > 0) {
                        // 早退
                        samsSchedulingKqfx.setZtcs(samsSchedulingKqfx.getZtcs() - 1);
                    } else if (samsSchedulingEmployee.getDkStatus() == 3 && samsSchedulingKqfx.getCdcs() > 0) {
                        // 迟到
                        samsSchedulingKqfx.setCdcs(samsSchedulingKqfx.getCdcs() - 1);
                    }

                    samsSchedulingKqfx.setUpdateTime(new Date());
                    samsSchedulingKqfxMapper.updateSamsSchedulingKqfx(samsSchedulingKqfx);
                }
            }

            samsSchedulingEmployee.setDkStatus(1);
        }
        return toAjax(samsSchedulingEmployeeMapper.updateSamsSchedulingEmployee(samsSchedulingEmployee));
    }


    /**
     * 删除员工排班信息
     */
    @Log(title = "员工排班信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{schedulingEmployeeIds}")
    public AjaxResult remove(@PathVariable Long[] schedulingEmployeeIds) {
        return toAjax(samsSchedulingEmployeeService.deleteSamsSchedulingEmployeeBySchedulingEmployeeIds(schedulingEmployeeIds));
    }
}
