package com.ruoyi.web.controller.user;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.entity.SamsAirportGroup;
import com.ruoyi.system.service.ISamsAirportGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机场大队信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/airportGroup")
public class SamsAirportGroupController extends BaseController
{
    @Autowired
    private ISamsAirportGroupService samsAirportGroupService;

    /**
     * 查询机场大队信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirportGroup samsAirportGroup)
    {
        startPage();
        List<SamsAirportGroup> list = samsAirportGroupService.selectSamsAirportGroupList(samsAirportGroup);
        return getDataTable(list);
    }

    /**
     * 获取机场大队信息详细信息
     */
    @GetMapping(value = "/{groupId}")
    public AjaxResult getInfo(@PathVariable("groupId") Long groupId)
    {
        return success(samsAirportGroupService.selectSamsAirportGroupByGroupId(groupId));
    }
}
