//package com.ruoyi.system.schedule;
//
//import com.ruoyi.system.domain.SamsSchedulingEmployee;
//import com.ruoyi.system.mapper.SamsSchedulingEmployeeMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * Description: 订单延时队列
// */
//@Component
//@Slf4j
//public class SchedulingEmployeeDelayQueue extends AbstractDelayQueueFactory {
//
//    @Lazy
//    @Autowired
//    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;
//
//    @Override
//    public void invoke(String jobId) {
//        log.info("定时任务_待打卡记录（超时）未打卡，自动更新打卡状态，排班任务id{}", jobId);
//        // 查询任务
//        SamsSchedulingEmployee schedulingEmployee = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeBySchedulingEmployeeId(Long.valueOf(jobId));
//
//        if (schedulingEmployee != null) {
//            List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectBySchedulingIdTask(schedulingEmployee.getSchedulingId());
//            if (schedulingEmployeeList != null && !schedulingEmployeeList.isEmpty()) {
//                for (SamsSchedulingEmployee samsSchedulingEmployee : schedulingEmployeeList) {
//                    if (null == samsSchedulingEmployee.getDkStatus()) {
//                        if (samsSchedulingEmployee.getDkType() == 1) {
//                            // 更新状态为“未打卡”
//                            samsSchedulingEmployee.setDkStatus(6);
//                        } else {
//                            // 更新状态为“未打卡”
//                            samsSchedulingEmployee.setDkStatus(7);
//                        }
//                    }
//
//                    samsSchedulingEmployee.setUpdateTime(new Date());
//                    samsSchedulingEmployeeMapper.updateSamsSchedulingEmployee(samsSchedulingEmployee);
//                }
//            }
//        }
//
//    }
//
//    @Override
//    public String setDelayQueueName() {
//        return "CHANGE_SCHEDULING_EMPLOYEE_DK_STATUS_QUEUE";
//    }
//
//}
