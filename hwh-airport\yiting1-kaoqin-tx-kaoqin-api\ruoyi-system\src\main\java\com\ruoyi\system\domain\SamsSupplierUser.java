package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 供应商用户信息对象 sams_supplier_user
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsSupplierUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 供应商用户ID
     */
    private Long supplierUserId;

    /**
     * 用户账号
     */
    @Excel(name = "用户账号")
    private String userName;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickName;

    /**
     * 密码
     */
    @Excel(name = "密码")
    private String password;

    /**
     * 手机号码
     */
    @Excel(name = "手机号码")
    private String phonenumber;

    /**
     * 绑定微信账户
     */
    @Excel(name = "绑定微信账户")
    private String wechat;

    /**
     * 供应商ID
     */
    @Excel(name = "供应商ID")
    private Long supplierId;

    /**
     * 状态（0启用 1禁用）
     */
    @Excel(name = "状态", readConverterExp = "0=启用,1=禁用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SamsSupplier samsSupplier;
}
