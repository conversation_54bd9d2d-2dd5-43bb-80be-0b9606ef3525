package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SamsAirportArea;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.SamsAirportAreaMapper;
import com.ruoyi.system.mapper.SamsAirportMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISamsAirportAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机场区域信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsAirportAreaServiceImpl implements ISamsAirportAreaService {
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询机场区域信息
     *
     * @param areaId 机场区域信息主键
     * @return 机场区域信息
     */
    @Override
    public SamsAirportArea selectSamsAirportAreaByAreaId(Long areaId) {
        return samsAirportAreaMapper.selectSamsAirportAreaByAreaId(areaId);
    }

    /**
     * 查询机场区域信息列表
     *
     * @param samsAirportArea 机场区域信息
     * @return 机场区域信息
     */
    @Override
    public List<SamsAirportArea> selectSamsAirportAreaList(SamsAirportArea samsAirportArea) {
        List<SamsAirportArea> list = samsAirportAreaMapper.selectSamsAirportAreaList(samsAirportArea);
        if (list != null && list.size() > 0) {
            for (SamsAirportArea airportArea : list) {
                airportArea.setSysDept(sysDeptMapper.selectDeptById(airportArea.getDeptId()));
                airportArea.setSamsAirport(samsAirportMapper.selectSamsAirportByAirportId(airportArea.getAirportId()));
            }
        }
        return list;
    }

    /**
     * 新增机场区域信息
     *
     * @param samsAirportArea 机场区域信息
     * @return 结果
     */
    @Override
    public int insertSamsAirportArea(SamsAirportArea samsAirportArea) {
        samsAirportArea.setCreateTime(DateUtils.getNowDate());
        return samsAirportAreaMapper.insertSamsAirportArea(samsAirportArea);
    }

    /**
     * 修改机场区域信息
     *
     * @param samsAirportArea 机场区域信息
     * @return 结果
     */
    @Override
    public int updateSamsAirportArea(SamsAirportArea samsAirportArea) {
        samsAirportArea.setUpdateTime(DateUtils.getNowDate());
        return samsAirportAreaMapper.updateSamsAirportArea(samsAirportArea);
    }

    /**
     * 批量删除机场区域信息
     *
     * @param areaIds 需要删除的机场区域信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportAreaByAreaIds(Long[] areaIds) {
        return samsAirportAreaMapper.deleteSamsAirportAreaByAreaIds(areaIds);
    }

    /**
     * 删除机场区域信息信息
     *
     * @param areaId 机场区域信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportAreaByAreaId(Long areaId) {
        return samsAirportAreaMapper.deleteSamsAirportAreaByAreaId(areaId);
    }
}
