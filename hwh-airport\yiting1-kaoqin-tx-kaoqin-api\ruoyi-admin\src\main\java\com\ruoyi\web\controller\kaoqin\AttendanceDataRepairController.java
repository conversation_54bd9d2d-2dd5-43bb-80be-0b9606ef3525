package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.job.KqfxJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 考勤数据修复Controller
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/system/attendanceRepair")
public class AttendanceDataRepairController extends BaseController {
    
    @Autowired
    private KqfxJob kqfxJob;
    
    /**
     * 修复历史考勤分析数据
     */
    @PreAuthorize("@ss.hasPermi('system:attendanceRepair:repair')")
    @Log(title = "考勤数据修复", businessType = BusinessType.UPDATE)
    @PostMapping("/repairHistory")
    public AjaxResult repairHistoryData(@RequestParam String startDate, @RequestParam String endDate) {
        try {
            // 验证日期格式
            if (!isValidDateFormat(startDate) || !isValidDateFormat(endDate)) {
                return AjaxResult.error("日期格式错误，请使用 yyyy-MM-dd 格式");
            }
            
            // 执行数据修复
            kqfxJob.repairHistoryAttendanceData(startDate, endDate);
            
            return AjaxResult.success("历史考勤数据修复任务已启动，请查看日志了解进度");
        } catch (Exception e) {
            logger.error("修复历史考勤数据失败", e);
            return AjaxResult.error("修复失败：" + e.getMessage());
        }
    }
    
    /**
     * 重新计算指定月份的考勤数据
     */
    @PreAuthorize("@ss.hasPermi('system:attendanceRepair:repair')")
    @Log(title = "考勤数据修复", businessType = BusinessType.UPDATE)
    @PostMapping("/repairMonth")
    public AjaxResult repairMonthData(@RequestParam String yearMonth) {
        try {
            // 验证年月格式
            if (!isValidYearMonthFormat(yearMonth)) {
                return AjaxResult.error("年月格式错误，请使用 yyyy-MM 格式");
            }
            
            // 执行单月数据修复
            kqfxJob.repairHistoryAttendanceData(yearMonth + "-01", yearMonth + "-31");
            
            return AjaxResult.success("指定月份考勤数据修复任务已启动，请查看日志了解进度");
        } catch (Exception e) {
            logger.error("修复指定月份考勤数据失败", e);
            return AjaxResult.error("修复失败：" + e.getMessage());
        }
    }
    
    /**
     * 验证日期格式
     */
    private boolean isValidDateFormat(String date) {
        if (date == null || date.length() != 10) {
            return false;
        }
        return date.matches("\\d{4}-\\d{2}-\\d{2}");
    }
    
    /**
     * 验证年月格式
     */
    private boolean isValidYearMonthFormat(String yearMonth) {
        if (yearMonth == null || yearMonth.length() != 7) {
            return false;
        }
        return yearMonth.matches("\\d{4}-\\d{2}");
    }
}
