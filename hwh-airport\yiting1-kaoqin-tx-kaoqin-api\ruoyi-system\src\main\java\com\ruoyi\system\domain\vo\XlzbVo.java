package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 学历占比
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
public class XlzbVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "小学人数")
    private Integer cat1;
    @Excel(name = "小学人数比例")
    private String cat1St;

    @Excel(name = "初中人数")
    private Integer cat2;
    @Excel(name = "初中人数比例")
    private String cat2St;

    @Excel(name = "高中人数")
    private Integer cat3;
    @Excel(name = "高中人数比例")
    private String cat3St;

    @Excel(name = "大专人数")
    private Integer cat4;
    @Excel(name = "大专人数比例")
    private String cat4St;

    @Excel(name = "本科人数")
    private Integer cat5;
    @Excel(name = "本科人数比例")
    private String cat5St;

    @Excel(name = "硕士人数")
    private Integer cat6;
    @Excel(name = "硕士人数比例")
    private String cat6St;

    @Excel(name = "博士人数")
    private Integer cat7;
    @Excel(name = "博士人数比例")
    private String cat7St;

}
