package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 户口性质
 *
 * <AUTHOR>
 */
public enum DomicileType
{
    URBAN_HUKOU(0, "城市户口"),
    AGRICULTURAL(1, "农业户口");

    private final Integer code;
    private final String info;

    DomicileType(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static DomicileType getDomicileType(Integer code) {
        for (DomicileType domicileType : DomicileType.values()) {
            if (Objects.equals(domicileType.getCode(), code)) {
                return domicileType;
            }
        }
        return null;
    }

    public static DomicileType getDomicileType(String info) {
        for (DomicileType domicileType : DomicileType.values()) {
            if (Objects.equals(domicileType.getInfo(), info)) {
                return domicileType;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
