<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsAirportTeamMapper">

    <resultMap type="SamsAirportTeam" id="SamsAirportTeamResult">
        <result property="teamId"    column="team_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="airportId"    column="airport_id"    />
        <result property="areaId"    column="area_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="teamName"    column="team_name"    />
        <result property="teamCode"    column="team_code"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSamsAirportTeamVo">
        select team_id, dept_id, airport_id, area_id, group_id, team_name, team_code, del_flag, create_by, create_time, update_by, update_time, remark from sams_airport_team
    </sql>

    <select id="selectSamsAirportTeamList" parameterType="SamsAirportTeam" resultMap="SamsAirportTeamResult">
        <include refid="selectSamsAirportTeamVo"/>
        <where>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="airportId != null "> and airport_id = #{airportId}</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="groupId != null "> and group_id = #{groupId}</if>
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
            <if test="teamCode != null  and teamCode != ''"> and team_code = #{teamCode}</if>
        </where>
    </select>

    <select id="selectSamsAirportTeamByTeamId" parameterType="Long" resultMap="SamsAirportTeamResult">
        <include refid="selectSamsAirportTeamVo"/>
        where team_id = #{teamId}
    </select>

    <select id="countAirportTeam" resultType="integer">
        select count(1) from sams_airport_team
    </select>
    <select id="selectSysTeamByDeptIdAndAirportIdAndAreaIdAndGroupIdAndTeamName" resultMap="SamsAirportTeamResult">
        <include refid="selectSamsAirportTeamVo"/>
        <where>
            <if test="deptId != null">
                dept_id = #{deptId}
            </if>
            <if test="airportId != null">
                and airport_id = #{airportId}
            </if>
            <if test="areaId != null">
                and area_id = #{areaId}
            </if>
            <if test="groupId != null">
                and group_id = #{groupId}
            </if>
            <if test="team != null">
                and team_name = #{team}
            </if>
        </where>
    </select>


    <insert id="insertSamsAirportTeam" parameterType="SamsAirportTeam" useGeneratedKeys="true" keyProperty="teamId">
        insert into sams_airport_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="airportId != null">airport_id,</if>
            <if test="areaId != null">area_id,</if>
            <if test="groupId != null">group_id,</if>
            <if test="teamName != null">team_name,</if>
            <if test="teamCode != null">team_code,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="airportId != null">#{airportId},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="teamCode != null">#{teamCode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsAirportTeam" parameterType="SamsAirportTeam">
        update sams_airport_team
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="airportId != null">airport_id = #{airportId},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="teamCode != null">team_code = #{teamCode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where team_id = #{teamId}
    </update>

    <delete id="deleteSamsAirportTeamByTeamId" parameterType="Long">
        delete from sams_airport_team where team_id = #{teamId}
    </delete>

    <delete id="deleteSamsAirportTeamByTeamIds" parameterType="String">
        delete from sams_airport_team where team_id in
        <foreach item="teamId" collection="array" open="(" separator="," close=")">
            #{teamId}
        </foreach>
    </delete>
</mapper>
