package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SamsAirport;
import com.ruoyi.system.service.ISamsAirportService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 机场信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/airport")
public class SamsAirportController extends BaseController
{
    @Autowired
    private ISamsAirportService samsAirportService;

    /**
     * 查询机场信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirport samsAirport)
    {
        startPage();
        List<SamsAirport> list = samsAirportService.selectSamsAirportList(samsAirport);
        return getDataTable(list);
    }

    /**
     * 导出机场信息列表
     */
    @Log(title = "机场信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsAirport samsAirport)
    {
        List<SamsAirport> list = samsAirportService.selectSamsAirportList(samsAirport);
        ExcelUtil<SamsAirport> util = new ExcelUtil<SamsAirport>(SamsAirport.class);
        util.exportExcel(response, list, "机场信息数据");
    }

    /**
     * 获取机场信息详细信息
     */
    @GetMapping(value = "/{airportId}")
    public AjaxResult getInfo(@PathVariable("airportId") Long airportId)
    {
        return success(samsAirportService.selectSamsAirportByAirportId(airportId));
    }

    /**
     * 新增机场信息
     */
    @Log(title = "机场信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsAirport samsAirport)
    {
        return toAjax(samsAirportService.insertSamsAirport(samsAirport));
    }

    /**
     * 修改机场信息
     */
    @Log(title = "机场信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsAirport samsAirport)
    {
        return toAjax(samsAirportService.updateSamsAirport(samsAirport));
    }

    /**
     * 删除机场信息
     */
    @Log(title = "机场信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{airportIds}")
    public AjaxResult remove(@PathVariable Long[] airportIds)
    {
        return toAjax(samsAirportService.deleteSamsAirportByAirportIds(airportIds));
    }
}
