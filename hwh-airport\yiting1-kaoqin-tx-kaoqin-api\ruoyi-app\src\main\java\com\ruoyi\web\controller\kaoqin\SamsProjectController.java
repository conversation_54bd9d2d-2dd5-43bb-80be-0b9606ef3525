package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SamsProject;
import com.ruoyi.system.service.ISamsProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 项目信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/project")
public class SamsProjectController extends BaseController {
    @Autowired
    private ISamsProjectService samsProjectService;

    /**
     * 查询项目信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsProject samsProject) {
        startPage();
        List<SamsProject> list = samsProjectService.selectSamsProjectList(samsProject);
        return getDataTable(list);
    }

    /**
     * 当前登录人的项目信息列表
     */
    @GetMapping("/kqxmlist")
    public AjaxResult kqxmlist() {
        List<SamsProject> list = samsProjectService.kqxmlist(getUserId());
        return success(list);
    }

    /**
     * 导出项目信息列表
     */
    @Log(title = "项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsProject samsProject) {
        List<SamsProject> list = samsProjectService.selectSamsProjectList(samsProject);
        ExcelUtil<SamsProject> util = new ExcelUtil<SamsProject>(SamsProject.class);
        util.exportExcel(response, list, "项目信息数据");
    }

    /**
     * 获取项目信息详细信息
     */
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId") Long projectId) {
        return success(samsProjectService.selectSamsProjectByProjectId(projectId));
    }

    /**
     * 新增项目信息
     */
    @Log(title = "项目信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsProject samsProject) {
        return toAjax(samsProjectService.insertSamsProject(samsProject));
    }

    /**
     * 修改项目信息
     */
    @Log(title = "项目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsProject samsProject) {
        return toAjax(samsProjectService.updateSamsProject(samsProject));
    }

    /**
     * 删除项目信息
     */
    @Log(title = "项目信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectIds}")
    public AjaxResult remove(@PathVariable Long[] projectIds) {
        return toAjax(samsProjectService.deleteSamsProjectByProjectIds(projectIds));
    }
}
