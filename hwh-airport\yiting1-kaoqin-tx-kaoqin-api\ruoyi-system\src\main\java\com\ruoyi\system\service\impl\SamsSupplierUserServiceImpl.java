package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.SamsSupplierUser;
import com.ruoyi.system.mapper.SamsSupplierMapper;
import com.ruoyi.system.mapper.SamsSupplierUserMapper;
import com.ruoyi.system.service.ISamsSupplierUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 供应商用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsSupplierUserServiceImpl implements ISamsSupplierUserService {
    @Autowired
    private SamsSupplierUserMapper samsSupplierUserMapper;
    @Autowired
    private SamsSupplierMapper samsSupplierMapper;

    /**
     * 查询供应商用户信息
     *
     * @param supplierUserId 供应商用户信息主键
     * @return 供应商用户信息
     */
    @Override
    public SamsSupplierUser selectSamsSupplierUserBySupplierUserId(Long supplierUserId) {
        return samsSupplierUserMapper.selectSamsSupplierUserBySupplierUserId(supplierUserId);
    }

    /**
     * 查询供应商用户信息列表
     *
     * @param samsSupplierUser 供应商用户信息
     * @return 供应商用户信息
     */
    @Override
    public List<SamsSupplierUser> selectSamsSupplierUserList(SamsSupplierUser samsSupplierUser) {
        List<SamsSupplierUser> list = samsSupplierUserMapper.selectSamsSupplierUserList(samsSupplierUser);
        if(list!=null && list.size()>0){
            for (SamsSupplierUser supplierUser : list) {
                supplierUser.setSamsSupplier(samsSupplierMapper.selectSamsSupplierBySupplierId(supplierUser.getSupplierId()));
            }
        }
        return list;
    }

    /**
     * 新增供应商用户信息
     *
     * @param samsSupplierUser 供应商用户信息
     * @return 结果
     */
    @Override
    public int insertSamsSupplierUser(SamsSupplierUser samsSupplierUser) {
        samsSupplierUser.setCreateTime(DateUtils.getNowDate());
        return samsSupplierUserMapper.insertSamsSupplierUser(samsSupplierUser);
    }

    /**
     * 修改供应商用户信息
     *
     * @param samsSupplierUser 供应商用户信息
     * @return 结果
     */
    @Override
    public int updateSamsSupplierUser(SamsSupplierUser samsSupplierUser) {
        samsSupplierUser.setUpdateTime(DateUtils.getNowDate());
        return samsSupplierUserMapper.updateSamsSupplierUser(samsSupplierUser);
    }

    /**
     * 批量删除供应商用户信息
     *
     * @param supplierUserIds 需要删除的供应商用户信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSupplierUserBySupplierUserIds(Long[] supplierUserIds) {
        return samsSupplierUserMapper.deleteSamsSupplierUserBySupplierUserIds(supplierUserIds);
    }

    /**
     * 删除供应商用户信息信息
     *
     * @param supplierUserId 供应商用户信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSupplierUserBySupplierUserId(Long supplierUserId) {
        return samsSupplierUserMapper.deleteSamsSupplierUserBySupplierUserId(supplierUserId);
    }
}
