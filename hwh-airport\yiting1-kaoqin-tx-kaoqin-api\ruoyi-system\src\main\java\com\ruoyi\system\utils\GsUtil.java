package com.ruoyi.system.utils;

import java.util.Date;

/**
 * @Description: 司龄工具类
 * @author: Dragon
 * @date: 2024/12/6 21:53
 */
public class GsUtil {
    /**
     * 获取司龄
     * @param date 开始日期
     * @return
     */
    public static String getSn(Date date) {
        Date currentTime = new Date();
        // 计算时间差（毫秒）
        long timeDifferenceInMilliseconds = currentTime.getTime() - date.getTime();

        // 将毫秒转换为天数
        long timeDifferenceInDays = timeDifferenceInMilliseconds / (1000 * 60 * 60 * 24);

        // 为了简化计算，我们假设每年有365.25天（考虑到闰年的平均效应）
        // 注意：这是一个近似值，用于快速计算。对于更精确的计算，应该考虑实际的闰年和每个月的天数。
        float averageDaysPerYear = 365.25f;

        // 计算年数差
        float yearsDifference = timeDifferenceInDays / averageDaysPerYear;
        yearsDifference = yearsDifference > 0 ? yearsDifference : 0;
        // 保留小数点后一位
       return String.format("%.1f", yearsDifference);
    }
}
