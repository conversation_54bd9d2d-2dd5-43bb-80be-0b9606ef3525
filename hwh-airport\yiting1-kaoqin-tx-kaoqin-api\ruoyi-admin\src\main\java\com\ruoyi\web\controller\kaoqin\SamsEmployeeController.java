package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.vo.SamsEmployeeExportVo;
import com.ruoyi.system.domain.vo.SamsEmployeeImportVo;
import com.ruoyi.system.mapper.SamsEmployeeMapper;
import com.ruoyi.system.service.ISamsEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 员工信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/employee")
public class SamsEmployeeController extends BaseController {
    @Autowired
    private ISamsEmployeeService samsEmployeeService;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;

    /**
     * 查询员工信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsEmployee samsEmployee) {
        startPage();
        List<SamsEmployee> list = samsEmployeeService.selectSamsEmployeeList(samsEmployee);
        return getDataTable(list);
    }

    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SamsEmployeeImportVo> util = new ExcelUtil<>(SamsEmployeeImportVo.class);
        List<SamsEmployeeImportVo> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = samsEmployeeService.importUser(userList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SamsEmployeeImportVo> util = new ExcelUtil<>(SamsEmployeeImportVo.class);
        util.importTemplateExcel(response, "员工数据");
    }

    /**
     * 导出员工信息列表
     */
    @Log(title = "员工信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsEmployee samsEmployee) {
//        List<SamsEmployee> list = samsEmployeeService.selectSamsEmployeeList(samsEmployee);
//        ExcelUtil<SamsEmployee> util = new ExcelUtil<SamsEmployee>(SamsEmployee.class);
//        util.exportExcel(response, list, "员工信息数据");
        List<SamsEmployeeExportVo> list = samsEmployeeService.selectSamsEmployeeExportList(samsEmployee);
        ExcelUtil<SamsEmployeeExportVo> util = new ExcelUtil<SamsEmployeeExportVo>(SamsEmployeeExportVo.class);
        util.exportExcel(response, list, "员工信息数据");
    }

    /**
     * 获取员工信息详细信息
     */
    @GetMapping(value = "/{employeeId}")
    public AjaxResult getInfo(@PathVariable("employeeId") Long employeeId) {
        return success(samsEmployeeService.selectSamsEmployeeByEmployeeId(employeeId));
    }

    /**
     * 统计报表员工人数
     */
    @GetMapping(value = "/ygrs")
    public AjaxResult ygrs() {
        return success(samsEmployeeService.ygrs());
    }

    /**
     * 统计报表年龄占比
     */
    @GetMapping(value = "/nlzb")
    public AjaxResult nlzb() {
        return success(samsEmployeeService.nlzb());
    }

    /**
     * 统计报表学历占比
     */
    @GetMapping(value = "/xlzb")
    public AjaxResult xlzb() {
        return success(samsEmployeeService.xlzb());
    }

    /**
     * 统计报表证书占比
     */
    @GetMapping(value = "/zszb")
    public AjaxResult zszb() {
        return success(samsEmployeeService.zszb());
    }

    /**
     * 统计报表组织架构信息
     * 统计报表的部门/机场/区域/大队/组别 是几个部门 几个机场 几个区域的显示
     */
    @GetMapping(value = "/zzjg")
    public AjaxResult zzjg() {
        return success(samsEmployeeService.zzjg());
    }

    /**
     * 新增员工信息
     */
    @Log(title = "员工信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsEmployee samsEmployee) {
        return toAjax(samsEmployeeService.insertSamsEmployee(samsEmployee));
    }

    /**
     * 修改员工信息
     */
    @Log(title = "员工信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsEmployee samsEmployee) {
        return toAjax(samsEmployeeService.updateSamsEmployee(samsEmployee));
    }

    /**
     * 解除微信绑定
     *
     * @param samsEmployee
     * @return
     */
    @PutMapping("/jcwxbd")
    public AjaxResult jcwxbd(@RequestBody SamsEmployee samsEmployee) {
        return toAjax(samsEmployeeMapper.jcwxbd(samsEmployee.getEmployeeId()));
    }

    /**
     * 删除员工信息
     */
    @Log(title = "员工信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{employeeIds}")
    public AjaxResult remove(@PathVariable Long[] employeeIds) {
        return toAjax(samsEmployeeService.deleteSamsEmployeeByEmployeeIds(employeeIds));
    }
}
