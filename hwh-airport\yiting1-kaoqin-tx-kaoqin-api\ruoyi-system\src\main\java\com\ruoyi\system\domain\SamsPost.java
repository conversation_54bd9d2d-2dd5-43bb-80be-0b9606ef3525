package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.*;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 岗位信息对象 sams_post
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsPost extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 岗位ID
     */
    private Long postId;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 岗位名称
     */
    @Excel(name = "岗位名称")
    private String postName;

    /**
     * 岗位性质
     */
    @Excel(name = "岗位性质")
    private String postNature;

    /**
     * 岗位人数
     */
    @Excel(name = "岗位人数")
    private Integer postNum;

    /**
     * 岗位职责
     */
    @Excel(name = "岗位职责")
    private String postDuty;

    /**
     * 考勤开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "考勤开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 考勤结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "考勤结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    @Excel(name = "开始工作时间")
    private String startWorkTime;
    @Excel(name = "结束工作时间")
    private String endWorkTime;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "默认排班日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date defaultPbrq;
    @Excel(name = "最后排班日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date lastPbrq;
    @Excel(name = "排班")
    private Integer pb;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场ID
     */
    @Excel(name = "机场ID")
    private Long airportId;

    /**
     * 区域ID
     */
    @Excel(name = "区域ID")
    private Long areaId;

    /**
     * 大队ID
     */
    @Excel(name = "大队ID")
    private Long groupId;

    /**
     * 小组ID
     */
    @Excel(name = "小组ID")
    private Long teamId;


    private SamsProject samsProject;
    private List<SamsPostClock> samsPostClockList;

    private SysDept sysDept;
    private SamsAirport samsAirport;
    private SamsAirportArea samsAirportArea;
    private SamsAirportGroup samsAirportGroup;
    private SamsAirportTeam samsAirportTeam;


}
