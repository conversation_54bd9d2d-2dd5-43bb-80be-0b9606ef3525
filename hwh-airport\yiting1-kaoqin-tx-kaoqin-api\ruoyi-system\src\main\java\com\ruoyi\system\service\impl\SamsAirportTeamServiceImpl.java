package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SamsAirportTeam;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsAirportTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机场组别信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsAirportTeamServiceImpl implements ISamsAirportTeamService {
    @Autowired
    private SamsAirportTeamMapper samsAirportTeamMapper;
    @Autowired
    private SamsAirportGroupMapper samsAirportGroupMapper;
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询机场组别信息
     *
     * @param teamId 机场组别信息主键
     * @return 机场组别信息
     */
    @Override
    public SamsAirportTeam selectSamsAirportTeamByTeamId(Long teamId) {
        return samsAirportTeamMapper.selectSamsAirportTeamByTeamId(teamId);
    }

    /**
     * 查询机场组别信息列表
     *
     * @param samsAirportTeam 机场组别信息
     * @return 机场组别信息
     */
    @Override
    public List<SamsAirportTeam> selectSamsAirportTeamList(SamsAirportTeam samsAirportTeam) {
        List<SamsAirportTeam> list = samsAirportTeamMapper.selectSamsAirportTeamList(samsAirportTeam);
        if (list != null && list.size() > 0) {
            for (SamsAirportTeam team : list) {
                team.setSamsAirportArea(samsAirportAreaMapper.selectSamsAirportAreaByAreaId(team.getAreaId()));
                team.setSamsAirport(samsAirportMapper.selectSamsAirportByAirportId(team.getAirportId()));
                team.setSysDept(sysDeptMapper.selectDeptById(team.getDeptId()));
                team.setSamsAirportGroup(samsAirportGroupMapper.selectSamsAirportGroupByGroupId(team.getGroupId()));
            }
        }
        return list;
    }

    /**
     * 新增机场组别信息
     *
     * @param samsAirportTeam 机场组别信息
     * @return 结果
     */
    @Override
    public int insertSamsAirportTeam(SamsAirportTeam samsAirportTeam) {
        samsAirportTeam.setCreateTime(DateUtils.getNowDate());
        return samsAirportTeamMapper.insertSamsAirportTeam(samsAirportTeam);
    }

    /**
     * 修改机场组别信息
     *
     * @param samsAirportTeam 机场组别信息
     * @return 结果
     */
    @Override
    public int updateSamsAirportTeam(SamsAirportTeam samsAirportTeam) {
        samsAirportTeam.setUpdateTime(DateUtils.getNowDate());
        return samsAirportTeamMapper.updateSamsAirportTeam(samsAirportTeam);
    }

    /**
     * 批量删除机场组别信息
     *
     * @param teamIds 需要删除的机场组别信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportTeamByTeamIds(Long[] teamIds) {
        return samsAirportTeamMapper.deleteSamsAirportTeamByTeamIds(teamIds);
    }

    /**
     * 删除机场组别信息信息
     *
     * @param teamId 机场组别信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportTeamByTeamId(Long teamId) {
        return samsAirportTeamMapper.deleteSamsAirportTeamByTeamId(teamId);
    }
}
