<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsSupplierMapper">
    
    <resultMap type="SamsSupplier" id="SamsSupplierResult">
        <result property="supplierId"    column="supplier_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="contactsName"    column="contacts_name"    />
        <result property="contactsPhone"    column="contacts_phone"    />
        <result property="startValidDate"    column="start_valid_date"    />
        <result property="endValidDate"    column="end_valid_date"    />
        <result property="contactsRole"    column="contacts_role"    />
        <result property="authStatus"    column="auth_status"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSamsSupplierVo">
        select supplier_id, company_name, contacts_name, contacts_phone, start_valid_date, end_valid_date, contacts_role, auth_status, status, del_flag, create_by, create_time, update_by, update_time, remark from sams_supplier
    </sql>

    <select id="selectSamsSupplierList" parameterType="SamsSupplier" resultMap="SamsSupplierResult">
        <include refid="selectSamsSupplierVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="contactsName != null  and contactsName != ''"> and contacts_name like concat('%', #{contactsName}, '%')</if>
            <if test="contactsPhone != null  and contactsPhone != ''"> and contacts_phone = #{contactsPhone}</if>
            <if test="startValidDate != null "> and start_valid_date = #{startValidDate}</if>
            <if test="endValidDate != null "> and end_valid_date = #{endValidDate}</if>
            <if test="contactsRole != null  and contactsRole != ''"> and contacts_role = #{contactsRole}</if>
            <if test="authStatus != null "> and auth_status = #{authStatus}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSamsSupplierBySupplierId" parameterType="Long" resultMap="SamsSupplierResult">
        <include refid="selectSamsSupplierVo"/>
        where supplier_id = #{supplierId}
    </select>
        
    <insert id="insertSamsSupplier" parameterType="SamsSupplier" useGeneratedKeys="true" keyProperty="supplierId">
        insert into sams_supplier
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="contactsName != null and contactsName != ''">contacts_name,</if>
            <if test="contactsPhone != null and contactsPhone != ''">contacts_phone,</if>
            <if test="startValidDate != null">start_valid_date,</if>
            <if test="endValidDate != null">end_valid_date,</if>
            <if test="contactsRole != null">contacts_role,</if>
            <if test="authStatus != null">auth_status,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="contactsName != null and contactsName != ''">#{contactsName},</if>
            <if test="contactsPhone != null and contactsPhone != ''">#{contactsPhone},</if>
            <if test="startValidDate != null">#{startValidDate},</if>
            <if test="endValidDate != null">#{endValidDate},</if>
            <if test="contactsRole != null">#{contactsRole},</if>
            <if test="authStatus != null">#{authStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsSupplier" parameterType="SamsSupplier">
        update sams_supplier
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="contactsName != null and contactsName != ''">contacts_name = #{contactsName},</if>
            <if test="contactsPhone != null and contactsPhone != ''">contacts_phone = #{contactsPhone},</if>
            <if test="startValidDate != null">start_valid_date = #{startValidDate},</if>
            <if test="endValidDate != null">end_valid_date = #{endValidDate},</if>
            <if test="contactsRole != null">contacts_role = #{contactsRole},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where supplier_id = #{supplierId}
    </update>

    <delete id="deleteSamsSupplierBySupplierId" parameterType="Long">
        delete from sams_supplier where supplier_id = #{supplierId}
    </delete>

    <delete id="deleteSamsSupplierBySupplierIds" parameterType="String">
        delete from sams_supplier where supplier_id in 
        <foreach item="supplierId" collection="array" open="(" separator="," close=")">
            #{supplierId}
        </foreach>
    </delete>
</mapper>