package com.ruoyi.system.service;

import com.ruoyi.system.domain.SamsProject;
import com.ruoyi.system.domain.vo.SamsProjectExportVo;

import java.util.List;

/**
 * 项目信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface ISamsProjectService
{
    /**
     * 查询项目信息
     *
     * @param projectId 项目信息主键
     * @return 项目信息
     */
    public SamsProject selectSamsProjectByProjectId(Long projectId);

    /**
     * 查询项目信息列表
     *
     * @param samsProject 项目信息
     * @return 项目信息集合
     */
    public List<SamsProject> selectSamsProjectList(SamsProject samsProject) ;

    /**
     * 查询指定人的项目列表
     * @param employeeId
     * @return
     */
    public List<SamsProject> kqxmlist(Long employeeId);

    /**
     * 新增项目信息
     *
     * @param samsProject 项目信息
     * @return 结果
     */
    public int insertSamsProject(SamsProject samsProject);

    /**
     * 修改项目信息
     *
     * @param samsProject 项目信息
     * @return 结果
     */
    public int updateSamsProject(SamsProject samsProject);

    /**
     * 批量删除项目信息
     *
     * @param projectIds 需要删除的项目信息主键集合
     * @return 结果
     */
    public int deleteSamsProjectByProjectIds(Long[] projectIds);

    /**
     * 删除项目信息信息
     *
     * @param projectId 项目信息主键
     * @return 结果
     */
    public int deleteSamsProjectByProjectId(Long projectId);

    /**
     *  查询导出项目信息集合
     *
     * @param samsProject
     * @return
     */
    List<SamsProjectExportVo> selectSamsProjectExportList(SamsProject samsProject);
}
