package com.ruoyi.system.mapper;

import java.util.List;

import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.system.domain.SamsProject;
import com.ruoyi.system.domain.vo.SamsProjectExportVo;

/**
 * 项目信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsProjectMapper
{
    /**
     * 查询项目信息
     *
     * @param projectId 项目信息主键
     * @return 项目信息
     */
    public SamsProject selectSamsProjectByProjectId(Long projectId);

    /**
     * 查询项目信息列表
     *
     * @param samsProject 项目信息
     * @return 项目信息集合
     */
    public List<SamsProject> selectSamsProjectList(SamsProject samsProject);
    /**
     * 查询指定人的项目列表
     * @param employeeId
     * @return
     */
    public List<SamsProject> kqxmlist(Long employeeId);
    /**
     * 新增项目信息
     *
     * @param samsProject 项目信息
     * @return 结果
     */
    public int insertSamsProject(SamsProject samsProject);

    /**
     * 修改项目信息
     *
     * @param samsProject 项目信息
     * @return 结果
     */
    public int updateSamsProject(SamsProject samsProject);

    /**
     * 删除项目信息
     *
     * @param projectId 项目信息主键
     * @return 结果
     */
    public int deleteSamsProjectByProjectId(Long projectId);

    /**
     * 批量删除项目信息
     *
     * @param projectIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsProjectByProjectIds(Long[] projectIds);

    /**
     * 查询项目导出列表
     * @param samsProject
     * @return
     */
    List<SamsProjectExportVo> selectSamsProjectExportVoList(SamsProject samsProject);
}
