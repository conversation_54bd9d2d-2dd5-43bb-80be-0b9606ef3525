package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsAirport;
import org.apache.ibatis.annotations.Param;

/**
 * 机场信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsAirportMapper
{
    /**
     * 查询机场信息
     *
     * @param airportId 机场信息主键
     * @return 机场信息
     */
    public SamsAirport selectSamsAirportByAirportId(Long airportId);

    /**
     * 查询机场信息列表
     *
     * @param samsAirport 机场信息
     * @return 机场信息集合
     */
    public List<SamsAirport> selectSamsAirportList(SamsAirport samsAirport);

    /**
     * 新增机场信息
     *
     * @param samsAirport 机场信息
     * @return 结果
     */
    public int insertSamsAirport(SamsAirport samsAirport);
    int countAirport();
    /**
     * 修改机场信息
     *
     * @param samsAirport 机场信息
     * @return 结果
     */
    public int updateSamsAirport(SamsAirport samsAirport);

    /**
     * 删除机场信息
     *
     * @param airportId 机场信息主键
     * @return 结果
     */
    public int deleteSamsAirportByAirportId(Long airportId);

    /**
     * 批量删除机场信息
     *
     * @param airportIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsAirportByAirportIds(Long[] airportIds);

    SamsAirport selectSamsAirportByDeptIdAndAirportName(@Param("deptId") Long deptId, @Param("airport") String airport);
}
