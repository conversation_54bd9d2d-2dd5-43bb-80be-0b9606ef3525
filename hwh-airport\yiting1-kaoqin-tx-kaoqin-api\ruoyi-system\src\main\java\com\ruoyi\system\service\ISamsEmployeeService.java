package com.ruoyi.system.service;

import java.util.List;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.system.domain.ZzjgVo;
import com.ruoyi.system.domain.vo.*;

/**
 * 员工信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface ISamsEmployeeService
{
    /**
     * 查询员工信息
     *
     * @param employeeId 员工信息主键
     * @return 员工信息
     */
    public SamsEmployee selectSamsEmployeeByEmployeeId(Long employeeId);

    /**
     * 查询员工信息列表
     *
     * @param samsEmployee 员工信息
     * @return 员工信息集合
     */
    public List<SamsEmployee> selectSamsEmployeeList(SamsEmployee samsEmployee);

    /**
     * 查询员工信息导出列表
     *
     * @param samsEmployee 员工信息
     * @return 员工信息集合
     */
    List<SamsEmployeeExportVo> selectSamsEmployeeExportList(SamsEmployee samsEmployee);

    /**
     * 员工人数
     * @return
     */
    YgrsVo ygrs();

    /**
     * 年龄占比
     * @return
     */
    YlzbVo nlzb();

    /**
     * 学历占比
     * @return
     */
    XlzbVo xlzb();

    /**
     * 证书占比
     * @return
     */
    List<ZszbVo> zszb();

    /**
     * 组织架构
     * @return
     */
    ZzjgVo zzjg();

    /**
     * 新增员工信息
     *
     * @param samsEmployee 员工信息
     * @return 结果
     */
    public int insertSamsEmployee(SamsEmployee samsEmployee);

    /**
     * 修改员工信息
     *
     * @param samsEmployee 员工信息
     * @return 结果
     */
    public int updateSamsEmployee(SamsEmployee samsEmployee);

    /**
     * 批量删除员工信息
     *
     * @param employeeIds 需要删除的员工信息主键集合
     * @return 结果
     */
    public int deleteSamsEmployeeByEmployeeIds(Long[] employeeIds);

    /**
     * 删除员工信息信息
     *
     * @param employeeId 员工信息主键
     * @return 结果
     */
    public int deleteSamsEmployeeByEmployeeId(Long employeeId);

    String importUser(List<SamsEmployeeImportVo> userList, boolean updateSupport, String operName);
}
