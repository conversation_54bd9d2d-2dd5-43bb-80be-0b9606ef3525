package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.SamsSchedulingEmployee;
import com.ruoyi.system.domain.vo.CxkqVo;
import com.ruoyi.system.domain.vo.SamsSchedulingPostEmployeeVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 员工排班信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
public interface SamsSchedulingEmployeeMapper {
    /**
     * 查询员工排班信息
     *
     * @param schedulingEmployeeId 员工排班信息主键
     * @return 员工排班信息
     */
    public SamsSchedulingEmployee selectSamsSchedulingEmployeeBySchedulingEmployeeId(Long schedulingEmployeeId);

    /**
     * 查询员工排班信息列表
     *
     * @param samsSchedulingEmployee 员工排班信息
     * @return 员工排班信息集合
     */
    public List<SamsSchedulingEmployee> selectSamsSchedulingEmployeeList(SamsSchedulingEmployee samsSchedulingEmployee);

    List<SamsSchedulingEmployee> selectByEmployeeId(Long employeeId);

    List<SamsSchedulingEmployee> selectByEmployeeId2(@Param("employeeId") Long employeeId, @Param("postId")Long postId, @Param("schedulingId") Long schedulingId);

    // 前两日
    List<SamsSchedulingEmployee> selectByEmployeeId2Day(@Param("employeeId") Long employeeId, @Param("postId")Long postId, @Param("schedulingId") Long schedulingId);

    List<SamsSchedulingEmployee> selectByEmployeeId4(@Param("employeeId") Long employeeId, @Param("postId")Long postId, @Param("schedulingId") Long schedulingId);

    /**
     * 查询昨天的考勤数据
     *
     * @return
     */
    List<Long> selectYestDayKqsj();

    /**
     * 查询昨天的考勤数据
     *
     * @return
     */
    List<SamsSchedulingPostEmployeeVo> selectYestDayKqsj2();

    /**
     * 查询昨天和前天的考勤数据
     *
     * @return
     */
    List<SamsSchedulingPostEmployeeVo> selectYestDayKqsj2Day();

    /**
     * 统计班次信息
     *
     * @param employeeId
     * @param cxny
     * @return
     */
    int tjbcData(@Param("employeeId") Long employeeId, @Param("cxny") String cxny);

    /**
     * @param cxkqVo
     * @return
     */
    List<SamsSchedulingEmployee> mykqdk(CxkqVo cxkqVo);

    /**
     * 通过排班ID查询所有已经安排的排班
     *
     * @param schedulingId
     * @return
     */
    List<SamsSchedulingEmployee> selectBySchedulingId(Long schedulingId);

    /**
     * 新增员工排班信息
     *
     * @param samsSchedulingEmployee 员工排班信息
     * @return 结果
     */
    public int insertSamsSchedulingEmployee(SamsSchedulingEmployee samsSchedulingEmployee);

    /**
     * 修改员工排班信息
     *
     * @param samsSchedulingEmployee 员工排班信息
     * @return 结果
     */
    public int updateSamsSchedulingEmployee(SamsSchedulingEmployee samsSchedulingEmployee);

    /**
     * 删除员工排班信息
     *
     * @param schedulingEmployeeId 员工排班信息主键
     * @return 结果
     */
    public int deleteSamsSchedulingEmployeeBySchedulingEmployeeId(Long schedulingEmployeeId);

    /**
     * 修改排班时删除未来的排班信息（时间从修改排班的日期开始）
     *
     * @param schedulingId
     * @param afterDutyDate
     * @return
     */
    public int deleteSamsSchedulingEmployeeBySchedulingIdAndAfterDutyDate(@Param("schedulingId") Long schedulingId, @Param("afterDutyDate") String afterDutyDate);

    public int deleteBySchedulingIdAndAfterDutyDate(@Param("schedulingId") Long schedulingId, @Param("afterDutyDate") String afterDutyDate, @Param("dkTags") List<String> dkTags);

    /**
     * 批量删除员工排班信息
     *
     * @param schedulingEmployeeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsSchedulingEmployeeBySchedulingEmployeeIds(Long[] schedulingEmployeeIds);

    Long[] selectSchedulingIdByDutyDate(@Param("dutyDate") String dutyDate);

    List<SamsSchedulingEmployee> selectByDkTag(String dkTag);

    int deleteSamsSchedulingEmployeeBySchedulingIds(@Param("schedulingIds") Long[] schedulingIds);

    /**
     * 根据排班id和排班日期
     *
     * @param schedulingId
     * @param dutyDate
     * @return
     */
    int deleteSamsSchedulingEmployeeBySchedulingIdAndDutyDate(@Param("schedulingId") Long schedulingId, @Param("dutyDate") String dutyDate);

    int deleteSamsSchedulingEmployeeOffWorkBySchedulingIdAndDutyDate(@Param("schedulingId") Long schedulingId, @Param("dutyDate") String futureSecondDate);

    /**
     * 查询员工排班信息列表
     *
     * @param samsSchedulingEmployee 员工排班信息
     * @return 员工排班信息集合
     */
    public List<SamsSchedulingEmployee> selectSamsSchedulingEmployeeList2(SamsSchedulingEmployee samsSchedulingEmployee);

    List<SamsSchedulingEmployee> selectSamsSchedulingEmployeeOffWorkBySchedulingIdAndDutyDate(SamsSchedulingEmployee samsSchedulingEmployee);

    List<SamsSchedulingEmployee> selectBySchedulingIdTask(Long schedulingId);

    List<SamsSchedulingEmployee> selectByEmployeeId3(SamsSchedulingEmployee samsSchedulingEmployee);

    List<String> selectBySchedulingIdAndAfterDutyDate(@Param("schedulingId") Long schedulingId, @Param("afterDutyDate") String afterDutyDate);

    void deleteSamsSchedulingEmployeeBySchedulingIdAndScheduleDate(@Param("schedulingId") Long schedulingId, @Param("dutyDate") String dutyDate);

    /**
     * 根据月份查询考勤数据
     * @param yearMonth 年月 格式：yyyy-MM
     * @return 员工岗位信息列表
     */
    List<SamsSchedulingPostEmployeeVo> selectAttendanceDataByMonth(@Param("yearMonth") String yearMonth);

    /**
     * 根据员工ID、岗位ID和月份查询考勤记录
     * @param employeeId 员工ID
     * @param postId 岗位ID
     * @param yearMonth 年月 格式：yyyy-MM
     * @return 员工排班信息列表
     */
    List<SamsSchedulingEmployee> selectEmployeeAttendanceByMonth(@Param("employeeId") Long employeeId,
                                                               @Param("postId") Long postId,
                                                               @Param("yearMonth") String yearMonth);
}
