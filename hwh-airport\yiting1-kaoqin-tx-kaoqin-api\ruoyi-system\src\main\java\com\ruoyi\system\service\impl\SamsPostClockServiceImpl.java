package com.ruoyi.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SamsPostClockMapper;
import com.ruoyi.system.domain.SamsPostClock;
import com.ruoyi.system.service.ISamsPostClockService;

/**
 * 岗位打卡信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsPostClockServiceImpl implements ISamsPostClockService 
{
    @Autowired
    private SamsPostClockMapper samsPostClockMapper;

    /**
     * 查询岗位打卡信息
     * 
     * @param postClockId 岗位打卡信息主键
     * @return 岗位打卡信息
     */
    @Override
    public SamsPostClock selectSamsPostClockByPostClockId(Long postClockId)
    {
        return samsPostClockMapper.selectSamsPostClockByPostClockId(postClockId);
    }

    /**
     * 查询岗位打卡信息列表
     * 
     * @param samsPostClock 岗位打卡信息
     * @return 岗位打卡信息
     */
    @Override
    public List<SamsPostClock> selectSamsPostClockList(SamsPostClock samsPostClock)
    {
        return samsPostClockMapper.selectSamsPostClockList(samsPostClock);
    }

    /**
     * 新增岗位打卡信息
     * 
     * @param samsPostClock 岗位打卡信息
     * @return 结果
     */
    @Override
    public int insertSamsPostClock(SamsPostClock samsPostClock)
    {
        samsPostClock.setCreateTime(DateUtils.getNowDate());
        return samsPostClockMapper.insertSamsPostClock(samsPostClock);
    }

    /**
     * 修改岗位打卡信息
     * 
     * @param samsPostClock 岗位打卡信息
     * @return 结果
     */
    @Override
    public int updateSamsPostClock(SamsPostClock samsPostClock)
    {
        samsPostClock.setUpdateTime(DateUtils.getNowDate());
        return samsPostClockMapper.updateSamsPostClock(samsPostClock);
    }

    /**
     * 批量删除岗位打卡信息
     * 
     * @param postClockIds 需要删除的岗位打卡信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsPostClockByPostClockIds(Long[] postClockIds)
    {
        return samsPostClockMapper.deleteSamsPostClockByPostClockIds(postClockIds);
    }

    /**
     * 删除岗位打卡信息信息
     * 
     * @param postClockId 岗位打卡信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsPostClockByPostClockId(Long postClockId)
    {
        return samsPostClockMapper.deleteSamsPostClockByPostClockId(postClockId);
    }
}
