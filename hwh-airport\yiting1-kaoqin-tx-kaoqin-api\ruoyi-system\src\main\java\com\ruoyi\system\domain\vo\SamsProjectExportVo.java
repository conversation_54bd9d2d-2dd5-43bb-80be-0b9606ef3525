package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 项目导出对象
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
public class SamsProjectExportVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectName;
    /**
     * 部门
     */
    @Excel(name = "部门")
    private String deptName;

    /**
     * 机场机场
     */
    @Excel(name = "机场")
    private String airport;

    /**
     * 区域
     */
    @Excel(name = "区域")
    private String area;

    /**
     * 类别
     */
    @Excel(name = "类别")
    private String typeRemark;

    /**
     * 项目负责人
     */
    @Excel(name = "项目负责人")
    private String projectContacts;

    /**
     * 负责人手机号
     */
    @Excel(name = "负责人手机号")
    private String projectContactsPhone;


    @Excel(name = "岗位数量")
    private Integer gwgs;

    @Excel(name = "是否加班项目")
    private String jbbz;

    @Excel(name = "备注")
    private String remark;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称")
    private String contractTitle;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    private String contractNo;

    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startContractDate;

    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endContractDate;

    /**
     * 相对方
     */
    @Excel(name = "甲方单位")
    private String other;

    /**
     * 甲方单位监督部门
     */
    @Excel(name = "甲方单位监督部门")
    private String otherDept;
}

