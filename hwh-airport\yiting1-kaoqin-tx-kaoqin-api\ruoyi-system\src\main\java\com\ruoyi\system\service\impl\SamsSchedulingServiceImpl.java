package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.vo.PbCxVo;
import com.ruoyi.system.domain.vo.SamsSchedulingVo;
import com.ruoyi.system.enmus.DkStatus;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsSchedulingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 排班信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Slf4j
@Service
public class SamsSchedulingServiceImpl implements ISamsSchedulingService {
    @Autowired
    private SamsSchedulingMapper samsSchedulingMapper;
    @Autowired
    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;
    @Autowired
    private SamsPostClockMapper samsPostClockMapper;
    @Autowired
    private SamsProjectEmployeeMapper samsProjectEmployeeMapper;
    @Autowired
    private SamsProjectMapper samsProjectMapper;
    @Autowired
    private SamsPostMapper samsPostMapper;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;

    /**
     * 查询排班信息
     *
     * @param schedulingId 排班信息主键
     * @return 排班信息
     */
    @Override
    public SamsScheduling selectSamsSchedulingBySchedulingId(Long schedulingId) {
        SamsScheduling scheduling = samsSchedulingMapper.selectSamsSchedulingBySchedulingId(schedulingId);
        scheduling.setSamsProject(samsProjectMapper.selectSamsProjectByProjectId(scheduling.getProjectId()));
        scheduling.setSamsPost(samsPostMapper.selectSamsPostByPostId(scheduling.getPostId()));
        SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
        samsSchedulingEmployee.setSchedulingId(scheduling.getSchedulingId());
//                scheduling.setSamsPostClock(samsPostClockMapper.selectSamsPostClockByPostClockId(scheduling.getPostClockId()));
        List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
        for (SamsSchedulingEmployee schedulingEmployee : schedulingEmployeeList) {
            schedulingEmployee.setSamsEmployee(samsEmployeeMapper.selectSamsEmployeeByEmployeeId(schedulingEmployee.getEmployeeId()));
        }
        scheduling.setSchedulingEmployeeList(schedulingEmployeeList);
        return scheduling;
    }

    /**
     * 查询排班信息列表
     *
     * @param samsScheduling 排班信息
     * @return 排班信息
     */
    @Override
    public List<SamsScheduling> selectSamsSchedulingList(SamsScheduling samsScheduling) {
        List<SamsScheduling> list = samsSchedulingMapper.selectSamsSchedulingList(samsScheduling);
        if (list != null && list.size() > 0) {
            for (SamsScheduling scheduling : list) {
                scheduling.setSamsProject(samsProjectMapper.selectSamsProjectByProjectId(scheduling.getProjectId()));
                scheduling.setSamsPost(samsPostMapper.selectSamsPostByPostId(scheduling.getPostId()));
                SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
                samsSchedulingEmployee.setSchedulingId(scheduling.getSchedulingId());
//                scheduling.setSamsPostClock(samsPostClockMapper.selectSamsPostClockByPostClockId(scheduling.getPostClockId()));
                List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
                for (SamsSchedulingEmployee schedulingEmployee : schedulingEmployeeList) {
                    if(null != schedulingEmployee.getEmployeeId() && schedulingEmployee.getEmployeeId().longValue() >0)
                        schedulingEmployee.setSamsEmployee(samsEmployeeMapper.selectSamsEmployeeByEmployeeId(schedulingEmployee.getEmployeeId()));
                }
                scheduling.setSchedulingEmployeeList(schedulingEmployeeList);
                List<SamsSchedulingEmployee> schedulingEmployeeList2 = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeList2(samsSchedulingEmployee);
                Map<Long, List<SamsSchedulingEmployee>> resultSchedulingEmployeeMap = new HashMap<>();
//                if (!CollectionUtils.isEmpty(schedulingEmployeeList2)) {
//                    resultSchedulingEmployeeMap.put(schedulingEmployeeList2.get(0).getEmployeeId(), schedulingEmployeeList2.stream().sorted(Comparator.comparingInt(SamsSchedulingEmployee::getDkType)).collect(Collectors.toList()));
//                }

                if (!CollectionUtils.isEmpty(schedulingEmployeeList2)) {
                    Map<String, List<SamsSchedulingEmployee>> schedulingEmployeeMap = schedulingEmployeeList2.stream()
                            .filter(item -> StringUtils.isNotBlank(item.getDkTag()))
                            .collect(Collectors.groupingBy(SamsSchedulingEmployee::getDkTag));

                    // 循环map
                    schedulingEmployeeMap.forEach((dkTag, valueList) -> {
                        if (valueList.size() == 2) {
                            List<SamsSchedulingEmployee> newValueList = valueList.stream().sorted(Comparator.comparingInt
                            (SamsSchedulingEmployee::getDkType)).collect(Collectors.toList());
                            resultSchedulingEmployeeMap.put(newValueList.get(0).getEmployeeId(), newValueList);
                            // 获取员工id作为key
//                            Long employeeId = newValueList.get(0).getEmployeeId();
//                            resultSchedulingEmployeeMap.put(employeeId, newValueList);
//                            resultSchedulingEmployeeMap.put(onItem.getEmployeeId(), onItem);
//                                            SamsSchedulingEmployee onItem = newValueList.get(0);
//                            SamsSchedulingEmployee offWork = newValueList.get(1);
//                            if (null == onItem.getDkStatus() && null == offWork.getDkStatus()) {
//                                // 未打卡
//                                onItem.setDkStatusDesc(DkStatus.NOT_PUNCHED.getInfo());
//                            } else if (null == onItem.getDkStatus()) {
//                                if (5 == offWork.getDkStatus()) {
//                                    // 早退
//                                    onItem.setDkStatusDesc(DkStatus.LEFT_EARLY.getInfo());
//                                } else {
//                                    onItem.setDkStatusDesc(DkStatus.ABNORMAL_PUNCH.getInfo());
//                                    onItem.setDkDate(offWork.getDkDate());
//                                }
//                            } else if (null == offWork.getDkStatus()) {
//                                if (3 == onItem.getDkStatus()) {
//                                    // 迟到
//                                    onItem.setDkStatusDesc(DkStatus.LATE.getInfo());
//                                } else {
//                                    onItem.setDkStatusDesc(DkStatus.ABNORMAL_PUNCH.getInfo());
//                                    onItem.setDkDate(offWork.getDkDate());
//                                }
//                            } else if (1 == onItem.getDkStatus() && 1 == offWork.getDkStatus()) {
//                                // 正常打卡
//                                onItem.setDkStatusDesc(DkStatus.PUNCHED.getInfo());
//                            } else {
//                                onItem.setDkStatusDesc(DkStatus.ABNORMAL_PUNCH.getInfo());
//                            }
//                            resultSchedulingEmployeeMap.put(onItem.getEmployeeId(), onItem);
                        }
                    });
                }
                scheduling.setSchedulingEmployeeMap(resultSchedulingEmployeeMap);
            }
        }
        return list;
    }

    @Override
    public List<SamsScheduling> list4cxTB(SamsScheduling samsScheduling) {
        List<SamsScheduling> list = samsSchedulingMapper.list4cxTB(samsScheduling);
        if (list != null && list.size() > 0) {
            for (SamsScheduling scheduling : list) {
                scheduling.setSamsProject(samsProjectMapper.selectSamsProjectByProjectId(scheduling.getProjectId()));
                scheduling.setSamsPost(samsPostMapper.selectSamsPostByPostId(scheduling.getPostId()));
                SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
                samsSchedulingEmployee.setSchedulingId(scheduling.getSchedulingId());
//                scheduling.setSamsPostClock(samsPostClockMapper.selectSamsPostClockByPostClockId(scheduling.getPostClockId()));
                List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
                for (SamsSchedulingEmployee schedulingEmployee : schedulingEmployeeList) {
                    schedulingEmployee.setSamsEmployee(samsEmployeeMapper.selectSamsEmployeeByEmployeeId(schedulingEmployee.getEmployeeId()));
                }
                scheduling.setSchedulingEmployeeList(schedulingEmployeeList);
            }
        }
        return list;
    }

    @Override
    public List<PbCxVo> selectSamsSchedulingList4Cx(SamsScheduling samsScheduling) {
        List<PbCxVo> list = samsSchedulingMapper.selectSamsSchedulingList4Cx(samsScheduling);
        if (samsScheduling.getCxLb().equals(2)) {
            for (PbCxVo pbCxVo : list) {
                samsScheduling.setCxStartDate(pbCxVo.getDatelist());
                pbCxVo.setSamsPostList(samsPostMapper.selectAll4Cx(samsScheduling));
            }
        }
        return list;
    }

    @Override
    public List<PbCxVo> selectSamsSchedulingList4Cx2(SamsScheduling samsScheduling) {
        List<PbCxVo> resultList = new ArrayList<>();
        Map<String, PbCxVo> map = new HashMap<>();
        List<SamsSchedulingVo> list = samsSchedulingMapper.selectSamsSchedulingList4Cx2(samsScheduling);
        if (!CollectionUtils.isEmpty(list)) {
            // 按日期分组
            Map<String, List<SamsSchedulingVo>> mapList = list.stream().collect(Collectors.groupingBy(SamsSchedulingVo::getDutyDate));
            for (Map.Entry<String, List<SamsSchedulingVo>> entry : mapList.entrySet()) {
                PbCxVo pbCxVo = new PbCxVo();
                String key = entry.getKey();
                List<SamsSchedulingVo> value = entry.getValue();
                // 获取岗位数
                long postCount = value.stream().map(SamsSchedulingVo::getPostId).distinct().count();
                long employeeCount = 0;
                // 提取出schedulingId集合
                List<Long> schedulingIdList = value.stream().map(SamsSchedulingVo::getSchedulingId).collect(Collectors.toList());
                // 获取人数
                SamsSchedulingEmployee schedulingEmployee = new SamsSchedulingEmployee();
                schedulingEmployee.setDutyDate(DateUtils.parseDate(key));
                Long[] schedulingIds = schedulingIdList.toArray(new Long[0]);
//                Long[] schedulingIds = samsSchedulingEmployeeMapper.selectSchedulingIdByDutyDate(key);
                if (null != schedulingIds && schedulingIds.length > 0) {
                    List<SamsScheduling> samsSchedulings = samsSchedulingMapper.selectSamsSchedulingListBySchedulingIds(schedulingIds);
                    if (!CollectionUtils.isEmpty(samsSchedulings)) {
                        for (SamsScheduling samsSchedulingVo : samsSchedulings) {
                            String postClock = samsSchedulingVo.getPostClock();
                            if (StringUtils.isNotBlank(postClock)) {
                                JSONArray postClockArray = JSON.parseArray(postClock);
                                if (!postClockArray.isEmpty()) {
                                    for (int i = 0; i < postClockArray.size(); i++) {
                                        JSONObject postClockObject = postClockArray.getJSONObject(i);
                                        if (postClockObject.containsKey("clockEmp")) {
                                            JSONArray clockEmpArray = postClockObject.getJSONArray("clockEmp");
                                            employeeCount += clockEmpArray.size();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                pbCxVo.setDatelist(key);

                if (map.containsKey(key)) {
                    pbCxVo.setGwtotal((int) postCount + map.get(key).getGwtotal());
                    pbCxVo.setRstotal((int) employeeCount + map.get(key).getRstotal());
                } else {
                    pbCxVo.setGwtotal((int) postCount);
                    pbCxVo.setRstotal((int) employeeCount);
                }
                map.put(key, pbCxVo);
            }

        }
        resultList = new ArrayList<>(map.values());
        if (samsScheduling.getCxLb().equals(2)) {
            for (PbCxVo pbCxVo : resultList) {
                samsScheduling.setCxStartDate(pbCxVo.getDatelist());
                pbCxVo.setSamsPostList(samsPostMapper.selectAll4Cx(samsScheduling));
            }
        }
        return resultList;
    }

    /**
     * 新增排班信息
     *
     * @param samsScheduling 排班信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertSamsScheduling(SamsScheduling samsScheduling) {
        samsScheduling.setCreateTime(DateUtils.getNowDate());
        int rows = samsSchedulingMapper.insertSamsScheduling(samsScheduling);
        SamsPost dbPost = samsPostMapper.selectSamsPostByPostId(samsScheduling.getPostId());
        dbPost.setPb(2);
        dbPost.setLastPbrq(DateUtils.getNowDate());
        samsPostMapper.updateSamsPost(dbPost);

        String[] dutyDateArr = samsScheduling.getDutyDate().split(",");
        // 打卡点信息
        JSONArray clockPointInfo = JSONArray.parseArray(samsScheduling.getPostClock());
        log.info("=============clockPointInfo:{}=============", clockPointInfo);
        // 转实体bean
        List<SamsPostClock> samsPostClocks = clockPointInfo.toJavaList(SamsPostClock.class);
        samsPostClocks.forEach(samsPostClock -> {
            samsPostClockMapper.updateSamsPostClock(samsPostClock);
        });
        // 考勤人员
        Map<String, String> kqEmpMap = new HashMap<>();
        for (String s1 : dutyDateArr) {
            log.info("=============s1:{}===========" , s1);
            // 对象：key: 员工id#是否加班#postClockId；obj: postClock,其中clockEmp应该只有该用户
            JSONObject jsonObject1 = new JSONObject();
            for (int i = 0; i < clockPointInfo.size(); i++) {
                // postClock
                JSONObject jsonObject = clockPointInfo.getJSONObject(i);
                log.info("=============jsonObject:" + jsonObject);
                // clockEmp
                JSONArray clockArray = jsonObject.getJSONArray("clockEmp");
                for (int i1 = 0; i1 < clockArray.size(); i1++) {
                    // 排班员工
                    JSONObject people = clockArray.getJSONObject(i1);
                    // s格式：排班员工id#是否加班
                    String s = people.getString("value");
                    String objKey = s + "#" + jsonObject.getLong("postClockId");
                    JSONArray jsonArray1 = jsonObject1.getJSONArray(objKey);
                    if (jsonArray1 == null) {
                        jsonArray1 = new JSONArray();
                    }
                    JSONObject newJsonObject = new JSONObject(jsonObject);
                    newJsonObject.put("clockEmp", JSONArray.of(people));
                    jsonArray1.add(newJsonObject);
                    jsonObject1.put(objKey, jsonArray1);
                }
            }
            log.info("=============jsonObject1:" + jsonObject1);
            for (String s : jsonObject1.keySet()) {
                // 定时任务运行时间
//                Date scheduleRunTime = new Date();
                String[] strArray = s.split("#");
                JSONArray clockArray = jsonObject1.getJSONArray(s);
                String dkTag = UUID.randomUUID().toString();
                for (int k = 1; k <= 2; k++) {
                    if (strArray.length > 1) {
                        if (strArray[1].equalsIgnoreCase("0")) {
                            JSONArray otherArray = jsonObject1.getJSONArray(strArray[0] + "#1" + "#" + strArray[2]);
                            if (otherArray != null) {
                                clockArray.addAll(otherArray);
                            }
                        } else {
                            JSONArray otherArray = jsonObject1.getJSONArray(strArray[0] + "#0" + "#" + strArray[2]);
                            if (otherArray != null) {
                                clockArray.addAll(otherArray);
                            }
                        }
                    } else {
                        JSONArray otherArray = jsonObject1.getJSONArray(strArray[0]);
                        if (otherArray != null) {
                            clockArray.addAll(otherArray);
                        }
                    }

//                clockArray.addAll(strArray[1].equalsIgnoreCase("0") ? jsonObject1.getJSONArray(strArray[0] + "#1") : jsonObject1.getJSONArray(strArray[0] + "#0"));
                    SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
                    samsSchedulingEmployee.setDkTag(dkTag);
                    samsSchedulingEmployee.setSchedulingId(samsScheduling.getSchedulingId());
                    SamsProjectEmployee samsProjectEmployee = samsProjectEmployeeMapper.selectByEmployeeIdAndProjectId(Long.parseLong(strArray[0]), samsScheduling.getProjectId());
                    samsSchedulingEmployee.setProjectEmployeeId(samsProjectEmployee.getProjectEmployeeId());
                    samsSchedulingEmployee.setEmployeeId(Long.parseLong(strArray[0]));
                    samsSchedulingEmployee.setScheduleDate(s1);
                    String dutyDate = s1;
                    if (null != clockArray && clockArray.size() > 0) {
                        JSONObject clockObject = clockArray.getJSONObject(0);
                        if (StringUtils.isNotBlank(clockObject.getString("startTime")) && StringUtils.isNotBlank(clockObject.getString("endTime"))) {

                            samsSchedulingEmployee.setStartTime(clockObject.getString("startTime"));
                            samsSchedulingEmployee.setStartTimeBefore(clockObject.getString("startTimeBefore"));
                            samsSchedulingEmployee.setStartTimeAfter(clockObject.getString("startTimeAfter"));
                            samsSchedulingEmployee.setEndTime(clockObject.getString("endTime"));
                            samsSchedulingEmployee.setEndTimeBefore(clockObject.getString("endTimeBefore"));
                            samsSchedulingEmployee.setEndTimeAfter(clockObject.getString("endTimeAfter"));
                            Date startTime = DateUtils.parseDate(s1 + " " + clockObject.getString("startTime"));
                            Date endTime = DateUtils.parseDate(s1 + " " + clockObject.getString("endTime"));
//                            scheduleRunTime = endTime;
                            if (k == 2 && clockObject.getInteger("nextDay") == 1) {
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(endTime);
                                calendar.add(Calendar.DAY_OF_MONTH, 1);
                                dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd", calendar.getTime());
                            }
                        }

                        samsSchedulingEmployee.setNextDay(clockObject.getInteger("nextDay"));
                        samsSchedulingEmployee.setIsAnytimeCheckIn(clockObject.getInteger("isAnytimeCheckIn"));
                    }
                    samsSchedulingEmployee.setDutyDate(DateUtils.parseDate(dutyDate));
                    samsSchedulingEmployee.setPostClock(clockArray.toString());
                    samsSchedulingEmployee.setCreateTime(DateUtils.getNowDate());
                    samsSchedulingEmployee.setDkType(k);
                    if (strArray.length > 1) {
                        samsSchedulingEmployee.setSfjb(Integer.parseInt(strArray[1]));
                    }
                    samsSchedulingEmployeeMapper.insertSamsSchedulingEmployee(samsSchedulingEmployee);

                }

            }
            for (String s : jsonObject1.keySet()) {
                String[] strArray = s.split("#");
                kqEmpMap.put(strArray[0], strArray[0]);
            }
        }
        String str = kqEmpMap.entrySet().stream().map(e -> e.getKey())
                .collect(Collectors.joining(","));
        samsScheduling.setKqEmp(str);
        samsSchedulingMapper.updateSamsScheduling(samsScheduling);
        return rows;
    }

//    /**
//     * 修改排班信息
//     *
//     * @param samsScheduling 排班信息
//     * @return 结果
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public int updateSamsScheduling(SamsScheduling samsScheduling) {
//        samsScheduling.setUpdateTime(DateUtils.getNowDate());
//        SamsPost dbPost = samsPostMapper.selectSamsPostByPostId(samsScheduling.getPostId());
//        if (!samsScheduling.getPostId().equals(dbPost.getPostId())) {
//            int num = samsSchedulingMapper.countByPostIdAndSchedulingId(dbPost.getPostId(), samsScheduling.getSchedulingId());
//            dbPost.setLastPbrq(DateUtils.getNowDate());
//            if (num == 0) {
//                dbPost.setPb(1);
//                samsPostMapper.updateSamsPost(dbPost);
//            }
//            SamsPost samsPost = samsPostMapper.selectSamsPostByPostId(samsScheduling.getPostId());
//            samsPost.setPb(2);
//            samsPostMapper.updateSamsPost(samsPost);
//        }
//
//        samsPostMapper.updateSamsPost(dbPost);
//        int rows = samsSchedulingMapper.updateSamsScheduling(samsScheduling);
//        String[] split2 = samsScheduling.getDutyDate().split(",");
//        samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingIdAndAfterDutyDate(samsScheduling.getSchedulingId(), split2[0]);
//        JSONArray jsonArray = JSONArray.parseArray(samsScheduling.getPostClock());
//        log.info("=============jsonArray:" + jsonArray);
//        StringBuilder kqEmp = new StringBuilder();
//        Map<String, String> kqEmpMap = new HashMap<>();
//        for (String s1 : split2) {
//            // 对象：key: 员工id#是否加班#postClockId；obj: postClock,其中clockEmp应该只有该用户
//            JSONObject jsonObject1 = new JSONObject();
//            for (int i = 0; i < jsonArray.size(); i++) {
//                // postClock
//                JSONObject jsonObject = jsonArray.getJSONObject(i);
//                log.info("=============jsonObject:" + jsonObject);
//                // clockEmp
//                JSONArray clockArray = jsonObject.getJSONArray("clockEmp");
//                for (int i1 = 0; i1 < clockArray.size(); i1++) {
//                    // 排班员工
//                    JSONObject people = clockArray.getJSONObject(i1);
//                    // s格式：排班员工id#是否加班
//                    String s = people.getString("value");
//                    String objKey = s + "#" + jsonObject.getLong("postClockId");
//                    JSONArray jsonArray1 = jsonObject1.getJSONArray(objKey);
//                    if (jsonArray1 == null) {
//                        jsonArray1 = new JSONArray();
//                    }
//                    JSONObject newJsonObject = new JSONObject(jsonObject);
//                    newJsonObject.put("clockEmp",  JSONArray.of(people));
//                    jsonArray1.add(newJsonObject);
//                    jsonObject1.put(objKey, jsonArray1);
//                }
//            }
//            log.info("=============jsonObject1:" + jsonObject1);
//            for (String s : jsonObject1.keySet()) {
//                // 定时任务运行时间
////                Date scheduleRunTime = new Date();
//                String[] strArray = s.split("#");
//                JSONArray clockArray = jsonObject1.getJSONArray(s);
//                // 2024-12-17 改成了上下班，需要添加两个打卡记录
//                for (int i1 = 1; i1 <= 2; i1++) {
//                    if (strArray.length > 1) {
//                        if (strArray[1].equalsIgnoreCase("0")) {
//                            JSONArray otherArray = jsonObject1.getJSONArray(strArray[0] + "#1");
//                            if (otherArray != null) {
//                                clockArray.addAll(otherArray);
//                            }
//                        } else {
//                            JSONArray otherArray = jsonObject1.getJSONArray(strArray[0] + "#0");
//                            if (otherArray != null) {
//                                clockArray.addAll(otherArray);
//                            }
//                        }
//                    } else {
//                        JSONArray otherArray = jsonObject1.getJSONArray(strArray[0]);
//                        if (otherArray != null) {
//                            clockArray.addAll(otherArray);
//                        }
//                    }
////                clockArray.addAll(strArray[1].equalsIgnoreCase("0") ? jsonObject1.getJSONArray(strArray[0] + "#1") : jsonObject1.getJSONArray(strArray[0] + "#0"));
//                    SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
//                    samsSchedulingEmployee.setSchedulingId(samsScheduling.getSchedulingId());
//                    SamsProjectEmployee samsProjectEmployee = samsProjectEmployeeMapper.selectByEmployeeIdAndProjectId(Long.parseLong(strArray[0]), samsScheduling.getProjectId());
//                    samsSchedulingEmployee.setProjectEmployeeId(samsProjectEmployee.getProjectEmployeeId());
//                    samsSchedulingEmployee.setEmployeeId(Long.parseLong(strArray[0]));
//                    String dutyDate = s1;
//                    if (null != clockArray && clockArray.size() > 0) {
//                        JSONObject clockObject = clockArray.getJSONObject(0);
//                        if (StringUtils.isNotBlank(clockObject.getString("startTime")) && StringUtils.isNotBlank(clockObject.getString("endTime"))) {
//                            samsSchedulingEmployee.setStartTime(clockObject.getString("startTime"));
//                            samsSchedulingEmployee.setEndTime(clockObject.getString("endTime"));
//                            Date startTime = DateUtils.parseDate(s1 + " " + clockObject.getString("startTime"));
//                            Date endTime = DateUtils.parseDate(s1 + " " + clockObject.getString("endTime"));
////                            scheduleRunTime = endTime;
//                            if (i1 == 2 && startTime.getTime() > endTime.getTime()) {
//                                Calendar calendar = Calendar.getInstance();
//                                calendar.setTime(endTime);
//                                calendar.add(Calendar.DAY_OF_MONTH, 1);
//                                dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd", calendar.getTime());
//                            }
//                        }
//                    }
//                    samsSchedulingEmployee.setDutyDate(DateUtils.parseDate(dutyDate));
//                    samsSchedulingEmployee.setPostClock(clockArray.toString());
//                    samsSchedulingEmployee.setCreateTime(DateUtils.getNowDate());
//                    samsSchedulingEmployee.setDkType(i1);
//                    if (strArray.length > 1) {
//                        samsSchedulingEmployee.setSfjb(Integer.parseInt(strArray[1]));
//                    }
//                    samsSchedulingEmployeeMapper.insertSamsSchedulingEmployee(samsSchedulingEmployee);
//
////                    if (i1 == 2){
////                        // 下班卡插入后启动定时任务
////                        Integer time = Math.toIntExact(DateUtils.getTime4fenzhong(new Date(), scheduleRunTime) * 60);
////                        schedulingEmployeeDelayQueue.addJobId(String.valueOf(samsSchedulingEmployee.getSchedulingEmployeeId()), time);
////                        log.info("定时任务_待打卡记录（超时）未打卡，添加排班任务，排班任务id：{}", samsSchedulingEmployee.getSchedulingEmployeeId());
////                    }
//                }
//
//
//            }
////            if (kqEmp.length() == 0) {
//            for (String s : jsonObject1.keySet()) {
//                String[] strArray = s.split("#");
////                    kqEmp.append(strArray[0]).append(",");
//                kqEmpMap.put(strArray[0], strArray[0]);
//            }
////            }
//        }
////        int index = kqEmp.length() - 1;
////        kqEmp.deleteCharAt(index);
////        samsScheduling.setKqEmp(kqEmp.toString());
//        String str = kqEmpMap.entrySet().stream().map(e -> e.getKey())
//                .collect(Collectors.joining(","));
////        System.out.println(str);
//        samsScheduling.setKqEmp(str);
//        samsSchedulingMapper.updateSamsScheduling(samsScheduling);
//        return rows;
//    }

    /**
     * 修改排班信息
     *
     * @param samsScheduling 排班信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateSamsScheduling(SamsScheduling samsScheduling) throws ParseException {
        try {
            samsScheduling.setUpdateTime(DateUtils.getNowDate());
            SamsPost dbPost = samsPostMapper.selectSamsPostByPostId(samsScheduling.getPostId());
            if (dbPost == null) {
                throw new IllegalArgumentException("Post not found for postId: " + samsScheduling.getPostId());
            }

            if (!samsScheduling.getPostId().equals(dbPost.getPostId())) {
                int num = samsSchedulingMapper.countByPostIdAndSchedulingId(dbPost.getPostId(), samsScheduling.getSchedulingId());
                dbPost.setLastPbrq(DateUtils.getNowDate());
                if (num == 0) {
                    dbPost.setPb(1);
                    samsPostMapper.updateSamsPost(dbPost);
                }
                SamsPost samsPost = samsPostMapper.selectSamsPostByPostId(samsScheduling.getPostId());
                if (samsPost != null) {
                    samsPost.setPb(2);
                    samsPostMapper.updateSamsPost(samsPost);
                }
            }

            // 获取原始排班信息，用于比较日期变化
            SamsScheduling originalScheduling = samsSchedulingMapper.selectSamsSchedulingBySchedulingId(samsScheduling.getSchedulingId());

            samsPostMapper.updateSamsPost(dbPost);
            int rows = samsSchedulingMapper.updateSamsScheduling(samsScheduling);

            String[] newDutyDates = samsScheduling.getDutyDate().split(",");
            if (newDutyDates.length == 0) {
                throw new IllegalArgumentException("Duty date is empty");
            }

            // 处理日期变化：删除不再需要的排班员工记录
            if (originalScheduling != null && StringUtils.isNotBlank(originalScheduling.getDutyDate())) {
                String[] originalDutyDates = originalScheduling.getDutyDate().split(",");
                Set<String> newDutyDateSet = new HashSet<>(Arrays.asList(newDutyDates));

                // 找出被删除的日期，删除对应的排班员工记录
                for (String originalDate : originalDutyDates) {
                    if (!newDutyDateSet.contains(originalDate)) {
                        samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingIdAndDutyDate(
                            samsScheduling.getSchedulingId(), originalDate);
                    }
                }
            }

            // 删除新日期范围内从第一个日期开始的所有记录（原有逻辑）
            samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingIdAndAfterDutyDate(samsScheduling.getSchedulingId(), newDutyDates[0]);

            JSONArray jsonArray = JSONArray.parseArray(samsScheduling.getPostClock());
            log.info("=============jsonArray:{}=============", jsonArray);
            // 转实体bean
            List<SamsPostClock> samsPostClocks = jsonArray.toJavaList(SamsPostClock.class);
            samsPostClocks.forEach(samsPostClock -> {
                samsPostClockMapper.updateSamsPostClock(samsPostClock);
            });

            StringBuilder kqEmp = new StringBuilder();
            Map<String, String> kqEmpMap = new HashMap<>();

            processJsonArray(jsonArray, newDutyDates, samsScheduling, kqEmpMap);

            String str = kqEmpMap.entrySet().stream().map(e -> e.getKey()).collect(Collectors.joining(","));
            samsScheduling.setKqEmp(str);
            samsSchedulingMapper.updateSamsScheduling(samsScheduling);

            return rows;
        } catch (Exception e) {
            log.error("Error updating SamsScheduling", e);
            throw e;
        }
    }

    private void processJsonArray(JSONArray jsonArray, String[] split2, SamsScheduling samsScheduling, Map<String, String> kqEmpMap) throws ParseException {
        for (String s1 : split2) {
            JSONObject jsonObject1 = new JSONObject();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                JSONArray clockArray = jsonObject.getJSONArray("clockEmp");
                for (int i1 = 0; i1 < clockArray.size(); i1++) {
                    JSONObject people = clockArray.getJSONObject(i1);
                    String s = people.getString("value");
                    String objKey = s + "#" + jsonObject.getLong("postClockId");
                    JSONArray jsonArray1 = jsonObject1.getJSONArray(objKey);
                    if (jsonArray1 == null) {
                        jsonArray1 = new JSONArray();
                    }
                    JSONObject newJsonObject = new JSONObject(jsonObject);
                    newJsonObject.put("clockEmp", JSONArray.of(people));
                    jsonArray1.add(newJsonObject);
                    jsonObject1.put(objKey, jsonArray1);
                }
            }

            for (String key : jsonObject1.keySet()) {
                String[] strArray = key.split("#");
                JSONArray clockArray = jsonObject1.getJSONArray(key);
                String dkTag = UUID.randomUUID().toString();
                for (int i1 = 1; i1 <= 2; i1++) {
                    if (strArray.length > 1) {
                        JSONArray otherArray = jsonObject1.getJSONArray(strArray[0] + "#" + (strArray[1].equalsIgnoreCase("0") ? "1" : "0"));
                        if (otherArray != null) {
                            clockArray.addAll(otherArray);
                        }
                    } else {
                        JSONArray otherArray = jsonObject1.getJSONArray(strArray[0]);
                        if (otherArray != null) {
                            clockArray.addAll(otherArray);
                        }
                    }

                    SamsSchedulingEmployee samsSchedulingEmployee = createSamsSchedulingEmployee(clockArray, samsScheduling, strArray, s1, i1);
                    samsSchedulingEmployee.setDkTag(dkTag);
                    samsSchedulingEmployeeMapper.insertSamsSchedulingEmployee(samsSchedulingEmployee);
                    kqEmpMap.put(strArray[0], strArray[0]);
                }
            }
        }
    }

    private SamsSchedulingEmployee createSamsSchedulingEmployee(JSONArray clockArray, SamsScheduling samsScheduling, String[] strArray, String dutyDate, int dkType) throws ParseException {
        SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
        samsSchedulingEmployee.setSchedulingId(samsScheduling.getSchedulingId());
        SamsProjectEmployee samsProjectEmployee = samsProjectEmployeeMapper.selectByEmployeeIdAndProjectId(Long.parseLong(strArray[0]), samsScheduling.getProjectId());
        if (samsProjectEmployee == null) {
            throw new IllegalArgumentException("Project employee not found for employeeId: " + strArray[0] + " and projectId: " + samsScheduling.getProjectId());
        }
        samsSchedulingEmployee.setProjectEmployeeId(samsProjectEmployee.getProjectEmployeeId());
        samsSchedulingEmployee.setEmployeeId(Long.parseLong(strArray[0]));
        samsSchedulingEmployee.setScheduleDate(dutyDate);

        if (null != clockArray && clockArray.size() > 0) {
            JSONObject clockObject = clockArray.getJSONObject(0);
            if (StringUtils.isNotBlank(clockObject.getString("startTime")) && StringUtils.isNotBlank(clockObject.getString("endTime"))) {

                samsSchedulingEmployee.setStartTime(clockObject.getString("startTime"));
                samsSchedulingEmployee.setStartTimeBefore(clockObject.getString("startTimeBefore"));
                samsSchedulingEmployee.setStartTimeAfter(clockObject.getString("startTimeAfter"));
                samsSchedulingEmployee.setEndTime(clockObject.getString("endTime"));
                samsSchedulingEmployee.setEndTimeBefore(clockObject.getString("endTimeBefore"));
                samsSchedulingEmployee.setEndTimeAfter(clockObject.getString("endTimeAfter"));
                Date startTime = DateUtils.parseDate(dutyDate + " " + clockObject.getString("startTime"));
                Date endTime = DateUtils.parseDate(dutyDate + " " + clockObject.getString("endTime"));
                if (dkType == 2 && clockObject.getInteger("nextDay") == 1) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(endTime);
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd", calendar.getTime());
                }
            }

            samsSchedulingEmployee.setNextDay(clockObject.getInteger("nextDay"));
            samsSchedulingEmployee.setIsAnytimeCheckIn(clockObject.getInteger("isAnytimeCheckIn"));
        }
        samsSchedulingEmployee.setDutyDate(DateUtils.parseDate(dutyDate));
        samsSchedulingEmployee.setPostClock(clockArray.toString());
        samsSchedulingEmployee.setCreateTime(DateUtils.getNowDate());
        samsSchedulingEmployee.setDkType(dkType);
        if (strArray.length > 1) {
            samsSchedulingEmployee.setSfjb(Integer.parseInt(strArray[1]));
        }
        return samsSchedulingEmployee;
    }


    /**
     * 批量删除排班信息
     *
     * @param schedulingIds 需要删除的排班信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSchedulingBySchedulingIds(Long[] schedulingIds) {
        int count = samsSchedulingMapper.deleteSamsSchedulingBySchedulingIds(schedulingIds);
        if (count > 0) {
            // 删除排班员工
            samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingIds(schedulingIds);
        }
        return count;
    }

    /**
     * 删除排班信息信息
     *
     * @param schedulingId 排班信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSchedulingBySchedulingId(Long schedulingId) {
        return samsSchedulingMapper.deleteSamsSchedulingBySchedulingId(schedulingId);
    }

    @Override
    public int deleteSamsSchedulingBySchedulingIdAndDutyDate(Long schedulingId, String dutyDate) {
        // 查询排班
        SamsScheduling samsScheduling = samsSchedulingMapper.selectSamsSchedulingBySchedulingId(schedulingId);
        if (null != samsScheduling && StringUtils.isNotBlank(samsScheduling.getDutyDate())) {
            if (samsScheduling.getDutyDate().equals(dutyDate)) {
                // 如果该排班的dutyDate与参数dutyDate相等，直接删除该排班，再删除对应的排班员工
                return this.deleteSamsSchedulingBySchedulingIds(new Long[]{schedulingId});
            } else if (samsScheduling.getDutyDate().contains(dutyDate)) {
                // 如果该排班的dutyDate与参数dutyDate相等，直接删除该排班，再删除对应的排班员工
                String[] dutyDateArray = samsScheduling.getDutyDate().split(",");
                String dutyDateString = Arrays.stream(dutyDateArray).filter(d -> !d.equals(dutyDate)).collect(Collectors.joining(","));
                samsScheduling.setDutyDate(dutyDateString);
                // 修改排班
                int count = samsSchedulingMapper.updateSamsScheduling(samsScheduling);
                if (count > 0) {
                    samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingIdAndScheduleDate(schedulingId, dutyDate);
                }
                return count;
            }
        }
        return 0;
    }

//    public void createScheduling(SamsScheduling samsScheduling, Date endTime) {
//        try {
//            // 创建一次性任务
//            JobKey jobKey = new JobKey("oneTimeJob" + samsScheduling.getSchedulingId(), "group1");
//            JobDetail job = JobBuilder.newJob(SchedulingJob.class)
//                    .withIdentity(jobKey)
//                    .usingJobData("jobId", samsScheduling.getSchedulingId()) // 传递排班ID参数
//                    .build();
//
//            Trigger trigger = TriggerBuilder.newTrigger()
//                    .withIdentity("oneTimeTrigger" + samsScheduling.getSchedulingId(), "group1")
//                    .startAt(endTime) // 指定具体的时间点
//                    .build();
//
//            // 获取调度器
//            Scheduler scheduler = schedulerFactoryBean.getScheduler();
//
//            // 将任务和触发器添加到调度器
//            scheduler.scheduleJob(job, trigger);
//
//            // 启动调度器（如果尚未启动）
//            if (!scheduler.isStarted()) {
//                scheduler.start();
//            }
//        } catch (SchedulerException e) {
//            e.printStackTrace();
//        }
//    }

}
