package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.SamsProject;
import com.ruoyi.system.domain.SamsProjectEmployee;
import com.ruoyi.system.mapper.SamsEmployeeMapper;
import com.ruoyi.system.mapper.SamsProjectEmployeeMapper;
import com.ruoyi.system.mapper.SamsProjectMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISamsProjectEmployeeService;
import com.ruoyi.system.service.ISamsProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目员工Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Slf4j
@Service
public class SamsProjectEmployeeServiceImpl implements ISamsProjectEmployeeService {
    @Autowired
    private SamsProjectEmployeeMapper samsProjectEmployeeMapper;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;
    @Autowired
    private SamsProjectMapper samsProjectMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private ISamsProjectService samsProjectService;;

    /**
     * 查询项目员工
     *
     * @param projectEmployeeId 项目员工主键
     * @return 项目员工
     */
    @Override
    public SamsProjectEmployee selectSamsProjectEmployeeByProjectEmployeeId(Long projectEmployeeId) {
        return samsProjectEmployeeMapper.selectSamsProjectEmployeeByProjectEmployeeId(projectEmployeeId);
    }

    /**
     * 查询项目员工列表
     *
     * @param samsProjectEmployee 项目员工
     * @return 项目员工
     */
    @Override
    public List<SamsProjectEmployee> selectSamsProjectEmployeeList(SamsProjectEmployee samsProjectEmployee) {
        List<SamsProjectEmployee> list = samsProjectEmployeeMapper.selectSamsProjectEmployeeList(samsProjectEmployee);
        if (list != null && list.size() > 0) {
            for (SamsProjectEmployee projectEmployee : list) {
                projectEmployee.setSamsEmployee(samsEmployeeMapper.selectSamsEmployeeByEmployeeId(projectEmployee.getEmployeeId()));
                SamsProject samsProject = samsProjectMapper.selectSamsProjectByProjectId(projectEmployee.getProjectId());
                if (null != samsProject) {
                    projectEmployee.setSamsProject(samsProject);
                    projectEmployee.setProjectName(samsProject.getProjectName());
                }
                // 设置员工部门
                SamsEmployee samsEmployee = projectEmployee.getSamsEmployee();
                if (null != samsEmployee) {
                    samsEmployee.setSysDept(sysDeptMapper.selectDeptById(samsEmployee.getDeptId()));
                }
            }
        }
        return list;
    }

    /**
     * 新增项目员工
     *
     * @param samsProjectEmployee 项目员工
     * @return 结果
     */
    @Override
    public int insertSamsProjectEmployee(SamsProjectEmployee samsProjectEmployee) {
        log.info("新增项目员工 {}", JSON.toJSONString(samsProjectEmployee));
        for (String s : samsProjectEmployee.getEmployeeIds().split(",")) {
            SamsProjectEmployee projectEmployee = new SamsProjectEmployee();
            projectEmployee.setCreateTime(DateUtils.getNowDate());
            SamsEmployee samsEmployee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(Long.parseLong(s));
            projectEmployee.setName(samsEmployee.getName());
            projectEmployee.setEmployeeId(Long.parseLong(s));
            projectEmployee.setNicheng(samsEmployee.getNicheng());
            projectEmployee.setProjectId(samsProjectEmployee.getProjectId());
            projectEmployee.setMobilePhone(samsEmployee.getMobilePhone());
            log.info("新增项目员工 {}", JSON.toJSONString(projectEmployee));
            samsProjectEmployeeMapper.insertSamsProjectEmployee(projectEmployee);
        }
        return 1;
    }

    /**
     * 修改项目员工
     *
     * @param samsProjectEmployee 项目员工
     * @return 结果
     */
    @Override
    public int updateSamsProjectEmployee(SamsProjectEmployee samsProjectEmployee) {
        samsProjectEmployee.setUpdateTime(DateUtils.getNowDate());
        SamsEmployee samsEmployee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(samsProjectEmployee.getEmployeeId());
        samsProjectEmployee.setName(samsEmployee.getName());
        samsProjectEmployee.setNicheng(samsEmployee.getNicheng());
        samsProjectEmployee.setMobilePhone(samsEmployee.getMobilePhone());
        return samsProjectEmployeeMapper.updateSamsProjectEmployee(samsProjectEmployee);
    }

    /**
     * 批量删除项目员工
     *
     * @param projectEmployeeIds 需要删除的项目员工主键
     * @return 结果
     */
    @Override
    public int deleteSamsProjectEmployeeByProjectEmployeeIds(Long[] projectEmployeeIds) {
        return samsProjectEmployeeMapper.deleteSamsProjectEmployeeByProjectEmployeeIds(projectEmployeeIds);
    }

    /**
     * 删除项目员工信息
     *
     * @param projectEmployeeId 项目员工主键
     * @return 结果
     */
    @Override
    public int deleteSamsProjectEmployeeByProjectEmployeeId(Long projectEmployeeId) {
        return samsProjectEmployeeMapper.deleteSamsProjectEmployeeByProjectEmployeeId(projectEmployeeId);
    }

    @Override
    public List<SamsProjectEmployee> selectSamsProjectEmployeeExportList(SamsProjectEmployee samsProjectEmployee) {
        return samsProjectEmployeeMapper.selectSamsProjectEmployeeExportList(samsProjectEmployee);
    }
}
