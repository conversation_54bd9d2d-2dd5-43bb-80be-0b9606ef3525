package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 机场区域信息对象 sams_airport_area
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsAirportArea extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 机场区域ID
     */
    private Long areaId;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场ID
     */
    @Excel(name = "机场ID")
    private Long airportId;

    /**
     * 区域名称
     */
    @Excel(name = "区域名称")
    private String areaName;

    /**
     * 区域编码
     */
    @Excel(name = "区域编码")
    private String areaCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SysDept sysDept;
    private SamsAirport samsAirport;

}
