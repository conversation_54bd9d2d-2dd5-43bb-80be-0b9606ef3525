package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.*;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 排班信息对象 sams_scheduling
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
public class SamsScheduling extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 排班ID
     */
    private Long schedulingId;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场ID
     */
    @Excel(name = "机场ID")
    private Long airportId;

    /**
     * 区域ID
     */
    @Excel(name = "区域ID")
    private Long areaId;

    /**
     * 大队ID
     */
    @Excel(name = "大队ID")
    private Long groupId;

    /**
     * 小组ID
     */
    @Excel(name = "小组ID")
    private Long teamId;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 岗位ID
     */
    @Excel(name = "岗位ID")
    private Long postId;

    /**
     * 排班日期
     */
    @Excel(name = "排班日期")
    private String dutyDate;

    /**
     * 考勤人员ID
     */
    @Excel(name = "考勤人员ID")
    private String kqEmp;

    /**
     * 打卡点ID
     */
    @Excel(name = "打卡点信息")
    private String postClock;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    private String cxStartDate;
    private String cxEndDate;
    /**
     * 查询类别 1查询月，2查询周
     */
    private Integer cxLb;
    /**
     * 查询月份
     */
    private String cxyf;
    private SysDept sysDept;
    private SamsAirport samsAirport;
    private SamsAirportArea samsAirportArea;
    private SamsAirportGroup samsAirportGroup;
    private SamsAirportTeam samsAirportTeam;
    private SamsProject samsProject;
    private SamsPost samsPost;
    private SamsPostClock samsPostClock;
    private List<SamsSchedulingEmployee> schedulingEmployeeList;
    private Map<Long, List<SamsSchedulingEmployee>> schedulingEmployeeMap;
    private Long employeeId;
}
