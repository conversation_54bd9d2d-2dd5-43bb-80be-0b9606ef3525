package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SamsPost;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxExportVo;
import com.ruoyi.system.enmus.CardType;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsSchedulingKqfxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 分析考勤报Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Service
public class SamsSchedulingKqfxServiceImpl implements ISamsSchedulingKqfxService {
    @Autowired
    private SamsSchedulingKqfxMapper samsSchedulingKqfxMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportGroupMapper samsAirportGroupMapper;
    @Autowired
    private SamsAirportTeamMapper samsAirportTeamMapper;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;
    @Autowired
    private SamsPostMapper samsPostMapper;

    /**
     * 查询分析考勤报
     *
     * @param kqfxId 分析考勤报主键
     * @return 分析考勤报
     */
    @Override
    public SamsSchedulingKqfx selectSamsSchedulingKqfxByKqfxId(Long kqfxId) {
        return samsSchedulingKqfxMapper.selectSamsSchedulingKqfxByKqfxId(kqfxId);
    }

    /**
     * 查询分析考勤报列表
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 分析考勤报
     */
    @Override
    public List<SamsSchedulingKqfx> selectSamsSchedulingKqfxList(SamsSchedulingKqfx samsSchedulingKqfx) {
        List<SamsSchedulingKqfx> list = samsSchedulingKqfxMapper.selectSamsSchedulingKqfxList(samsSchedulingKqfx);
//        if(list!=null && list.size()>0){
//            for (SamsSchedulingKqfx schedulingKqfx : list) {
//                schedulingKqfx.setSysDept(sysDeptMapper.selectDeptById(schedulingKqfx.getDeptId()));
//                SamsPost samsPost = samsPostMapper.selectSamsPostByPostId(schedulingKqfx.getPostId());
//                if (null != samsPost) {
//                    schedulingKqfx.setPostName(samsPost.getPostName());
//                }
//                schedulingKqfx.setSamsAirport(samsAirportMapper.selectSamsAirportByAirportId(schedulingKqfx.getAirportId()));
//                schedulingKqfx.setSamsAirportArea(samsAirportAreaMapper.selectSamsAirportAreaByAreaId(schedulingKqfx.getAreaId()));
//                schedulingKqfx.setSamsAirportGroup(samsAirportGroupMapper.selectSamsAirportGroupByGroupId(schedulingKqfx.getGroupId()));
//                schedulingKqfx.setSamsAirportTeam(samsAirportTeamMapper.selectSamsAirportTeamByTeamId(schedulingKqfx.getTeamId()));
//                schedulingKqfx.setSamsEmployee(samsEmployeeMapper.selectSamsEmployeeByEmployeeId(schedulingKqfx.getEmployeeId()));
//            }
//        }
        return list;
    }

    /**
     * 新增分析考勤报
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 结果
     */
    @Override
    public int insertSamsSchedulingKqfx(SamsSchedulingKqfx samsSchedulingKqfx) {
        samsSchedulingKqfx.setCreateTime(DateUtils.getNowDate());
        return samsSchedulingKqfxMapper.insertSamsSchedulingKqfx(samsSchedulingKqfx);
    }

    /**
     * 修改分析考勤报
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 结果
     */
    @Override
    public int updateSamsSchedulingKqfx(SamsSchedulingKqfx samsSchedulingKqfx) {
        samsSchedulingKqfx.setUpdateTime(DateUtils.getNowDate());
        return samsSchedulingKqfxMapper.updateSamsSchedulingKqfx(samsSchedulingKqfx);
    }

    /**
     * 批量删除分析考勤报
     *
     * @param kqfxIds 需要删除的分析考勤报主键
     * @return 结果
     */
    @Override
    public int deleteSamsSchedulingKqfxByKqfxIds(Long[] kqfxIds) {
        return samsSchedulingKqfxMapper.deleteSamsSchedulingKqfxByKqfxIds(kqfxIds);
    }

    /**
     * 删除分析考勤报信息
     *
     * @param kqfxId 分析考勤报主键
     * @return 结果
     */
    @Override
    public int deleteSamsSchedulingKqfxByKqfxId(Long kqfxId) {
        return samsSchedulingKqfxMapper.deleteSamsSchedulingKqfxByKqfxId(kqfxId);
    }

    /**
     * 查询分析考勤报列表
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 分析考勤报
     */
    @Override
    public List<SamsSchedulingKqfxExportVo> selectSamsSchedulingKqfxExportList(SamsSchedulingKqfx samsSchedulingKqfx) {
        List<SamsSchedulingKqfxExportVo> list = samsSchedulingKqfxMapper.selectSamsSchedulingKqfxExportList(samsSchedulingKqfx);
        if (!CollectionUtils.isEmpty(list)) {
            for (SamsSchedulingKqfxExportVo schedulingKqfx : list) {
                CardType certType = CardType.getCertType(schedulingKqfx.getCardType());
                if (certType != null) {
                    schedulingKqfx.setCardTypeName(certType.getInfo());
                }
            }
        }
        return list;
    }
}
