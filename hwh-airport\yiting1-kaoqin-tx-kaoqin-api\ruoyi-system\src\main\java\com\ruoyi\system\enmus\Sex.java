package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 性别
 *
 * <AUTHOR>
 */
public enum Sex
{
    MALE(0, "男"),
    FEMALE(1, "女");

    private final Integer code;
    private final String info;

    Sex(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static Sex getSex(Integer code) {
        for (Sex sex : Sex.values()) {
            if (Objects.equals(sex.getCode(), code)) {
                return sex;
            }
        }
        return null;
    }

    public static Sex getSex(String info) {
        for (Sex sex : Sex.values()) {
            if (Objects.equals(sex.getInfo(), info)) {
                return sex;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
