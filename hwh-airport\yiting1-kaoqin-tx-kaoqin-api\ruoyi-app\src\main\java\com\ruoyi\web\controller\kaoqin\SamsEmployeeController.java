package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.service.ISamsEmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 员工信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/employee")
public class SamsEmployeeController extends BaseController {
    @Autowired
    private ISamsEmployeeService samsEmployeeService;

    /**
     * 查询员工信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsEmployee samsEmployee) {
        samsEmployee.setEmployeeId(getUserId());
        startPage();
        List<SamsEmployee> list = samsEmployeeService.selectSamsEmployeeList(samsEmployee);
        return getDataTable(list);
    }

    /**
     * 导出员工信息列表
     */
    @Log(title = "员工信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsEmployee samsEmployee) {
        List<SamsEmployee> list = samsEmployeeService.selectSamsEmployeeList(samsEmployee);
        ExcelUtil<SamsEmployee> util = new ExcelUtil<SamsEmployee>(SamsEmployee.class);
        util.exportExcel(response, list, "员工信息数据");
    }

    /**
     * 获取员工信息详细信息
     */
    @GetMapping(value = "/{employeeId}")
    public AjaxResult getInfo(@PathVariable("employeeId") Long employeeId) {
        return success(samsEmployeeService.selectSamsEmployeeByEmployeeId(employeeId));
    }

    /**
     * 新增员工信息
     */
    @Log(title = "员工信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsEmployee samsEmployee) {
        return toAjax(samsEmployeeService.insertSamsEmployee(samsEmployee));
    }

    /**
     * 修改员工信息
     */
    @Log(title = "员工信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsEmployee samsEmployee) {
        return toAjax(samsEmployeeService.updateSamsEmployee(samsEmployee));
    }

    /**
     * 删除员工信息
     */
    @Log(title = "员工信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{employeeIds}")
    public AjaxResult remove(@PathVariable Long[] employeeIds) {
        return toAjax(samsEmployeeService.deleteSamsEmployeeByEmployeeIds(employeeIds));
    }
}
