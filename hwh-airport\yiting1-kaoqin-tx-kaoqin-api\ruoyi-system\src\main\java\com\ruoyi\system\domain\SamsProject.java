package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SamsAirport;
import com.ruoyi.common.core.domain.entity.SamsAirportArea;
import com.ruoyi.common.core.domain.entity.SamsAirportGroup;
import com.ruoyi.common.core.domain.entity.SysDept;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目信息对象 sams_project
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsProject extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 类别
     */
    @Excel(name = "类别")
    private String typeRemark;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectName;

    /**
     * 项目负责人
     */
    @Excel(name = "项目负责人")
    private String projectContacts;

    /**
     * 负责人手机号
     */
    @Excel(name = "负责人手机号")
    private String projectContactsPhone;

    /**
     * 相对方
     */
    @Excel(name = "甲方单位")
    private String other;

    /**
     * 甲方单位监督部门
     */
    @Excel(name = "甲方单位监督部门")
    private String otherDept;

    /**
     * 相对方联系人
     */
    @Excel(name = "相对方联系人")
    private String otherContacts;

    /**
     * 相对方联系人手机号
     */
    @Excel(name = "相对方联系人手机号")
    private String otherContactsPhone;

    /**
     * 合同编号
     */
    @Excel(name = "合同编号")
    private String contractNo;

    /**
     * 合同名称
     */
    @Excel(name = "合同名称")
    private String contractTitle;

    /**
     * 合同开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startContractDate;

    /**
     * 合同结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endContractDate;

    /**
     * 合同岗位数
     */
    @Excel(name = "合同岗位数")
    private Long contractPostNum;

    /**
     * 合同岗位成本
     */
    @Excel(name = "合同岗位成本")
    private BigDecimal contractPostCost;

    /**
     * 实际岗位数
     */
    @Excel(name = "实际岗位数")
    private Long actualPostNum;

    /**
     * 实际岗位成本
     */
    @Excel(name = "实际岗位成本")
    private BigDecimal actualPostCost;

    /**
     * 合同总价
     */
    @Excel(name = "合同总价")
    private BigDecimal contractTotalPrice;

    /**
     * 年合同价
     */
    @Excel(name = "年合同价")
    private BigDecimal contractYearPrice;

    /**
     * 价格明细
     */
    @Excel(name = "价格明细")
    private String priceDetail;

    /**
     * 打卡地点
     */
    @Excel(name = "打卡地点")
    private String clockAddress;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String latitude;

    /**
     * 维度
     */
    @Excel(name = "维度")
    private String longitude;

    /**
     * 打卡范围
     */
    @Excel(name = "打卡范围")
    private Long clockRange;
    @Excel(name = "岗位数量")
    private Integer gwgs;
    @Excel(name = "勤务模式")
    private String qwms;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    @Excel(name = "是否加班项目1不是，2是")
    private Integer jbbz;
    @Excel(name = "打卡方式1定位打卡，2拍照打卡，3人脸识别打卡")
    private Integer dkfs;

    private SysDept dept;
    private SamsAirport airport;
    private SamsAirportArea area;
    private SamsAirportGroup group;

    @Excel(name = "机场")
    private Long airportId;
    @Excel(name = "区域")
    private Long areaId;
    @Excel(name = "大队")
    private Long groupId;

}
