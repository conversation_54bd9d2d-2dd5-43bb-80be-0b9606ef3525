package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SamsAirportGroup;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.SamsAirportAreaMapper;
import com.ruoyi.system.mapper.SamsAirportGroupMapper;
import com.ruoyi.system.mapper.SamsAirportMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISamsAirportGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机场大队信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsAirportGroupServiceImpl implements ISamsAirportGroupService {
    @Autowired
    private SamsAirportGroupMapper samsAirportGroupMapper;
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询机场大队信息
     *
     * @param groupId 机场大队信息主键
     * @return 机场大队信息
     */
    @Override
    public SamsAirportGroup selectSamsAirportGroupByGroupId(Long groupId) {
        return samsAirportGroupMapper.selectSamsAirportGroupByGroupId(groupId);
    }

    /**
     * 查询机场大队信息列表
     *
     * @param samsAirportGroup 机场大队信息
     * @return 机场大队信息
     */
    @Override
    public List<SamsAirportGroup> selectSamsAirportGroupList(SamsAirportGroup samsAirportGroup) {
        List<SamsAirportGroup> list = samsAirportGroupMapper.selectSamsAirportGroupList(samsAirportGroup);
        if (list != null && list.size() > 0) {
            for (SamsAirportGroup group : list) {
                group.setSamsAirportArea(samsAirportAreaMapper.selectSamsAirportAreaByAreaId(group.getAreaId()));
                group.setSamsAirport(samsAirportMapper.selectSamsAirportByAirportId(group.getAirportId()));
                group.setSysDept(sysDeptMapper.selectDeptById(group.getDeptId()));
            }
        }
        return list;
    }

    /**
     * 新增机场大队信息
     *
     * @param samsAirportGroup 机场大队信息
     * @return 结果
     */
    @Override
    public int insertSamsAirportGroup(SamsAirportGroup samsAirportGroup) {
        samsAirportGroup.setCreateTime(DateUtils.getNowDate());
        return samsAirportGroupMapper.insertSamsAirportGroup(samsAirportGroup);
    }

    /**
     * 修改机场大队信息
     *
     * @param samsAirportGroup 机场大队信息
     * @return 结果
     */
    @Override
    public int updateSamsAirportGroup(SamsAirportGroup samsAirportGroup) {
        samsAirportGroup.setUpdateTime(DateUtils.getNowDate());
        return samsAirportGroupMapper.updateSamsAirportGroup(samsAirportGroup);
    }

    /**
     * 批量删除机场大队信息
     *
     * @param groupIds 需要删除的机场大队信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportGroupByGroupIds(Long[] groupIds) {
        return samsAirportGroupMapper.deleteSamsAirportGroupByGroupIds(groupIds);
    }

    /**
     * 删除机场大队信息信息
     *
     * @param groupId 机场大队信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportGroupByGroupId(Long groupId) {
        return samsAirportGroupMapper.deleteSamsAirportGroupByGroupId(groupId);
    }
}
