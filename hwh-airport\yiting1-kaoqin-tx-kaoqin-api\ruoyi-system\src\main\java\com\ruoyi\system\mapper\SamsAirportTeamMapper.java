package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsAirportTeam;
import org.apache.ibatis.annotations.Param;

/**
 * 机场组别信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsAirportTeamMapper
{
    /**
     * 查询机场组别信息
     *
     * @param teamId 机场组别信息主键
     * @return 机场组别信息
     */
    public SamsAirportTeam selectSamsAirportTeamByTeamId(Long teamId);

    /**
     * 查询机场组别信息列表
     *
     * @param samsAirportTeam 机场组别信息
     * @return 机场组别信息集合
     */
    public List<SamsAirportTeam> selectSamsAirportTeamList(SamsAirportTeam samsAirportTeam);
    int countAirportTeam();
    /**
     * 新增机场组别信息
     *
     * @param samsAirportTeam 机场组别信息
     * @return 结果
     */
    public int insertSamsAirportTeam(SamsAirportTeam samsAirportTeam);

    /**
     * 修改机场组别信息
     *
     * @param samsAirportTeam 机场组别信息
     * @return 结果
     */
    public int updateSamsAirportTeam(SamsAirportTeam samsAirportTeam);

    /**
     * 删除机场组别信息
     *
     * @param teamId 机场组别信息主键
     * @return 结果
     */
    public int deleteSamsAirportTeamByTeamId(Long teamId);

    /**
     * 批量删除机场组别信息
     *
     * @param teamIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsAirportTeamByTeamIds(Long[] teamIds);

    SamsAirportTeam selectSysTeamByDeptIdAndAirportIdAndAreaIdAndGroupIdAndTeamName(@Param("deptId") Long deptId,
                                                                                    @Param("airportId") Long airportId,
                                                                                    @Param("areaId") Long areaId,
                                                                                    @Param("groupId") Long groupId,
                                                                                    @Param("team") String team);
}
