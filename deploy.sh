#!/bin/bash

# 设置工作目录
WORK_DIR="/usr/local/services/admin"
cd "$WORK_DIR" || {
    echo "错误: 无法切换到工作目录 $WORK_DIR"
    exit 1
}

# 获取当前日期，格式为MMDD
DATE=$(date +%m%d)

echo "开始部署流程..."
echo "工作目录: $WORK_DIR"
echo "当前日期: $DATE"

# 第一步：停止服务
echo "第一步：停止服务..."
./amb.sh stop
if [ $? -eq 0 ]; then
    echo "服务停止成功"
else
    echo "服务停止失败，请检查"
    exit 1
fi

# 第二步：备份当前的airport-admin.jar
echo "第二步：备份当前的airport-admin.jar..."
if [ -f "airport-admin.jar" ]; then
    mv airport-admin.jar airport-admin.jar$DATE
    echo "备份完成: airport-admin.jar -> airport-admin.jar$DATE"
else
    echo "警告: airport-admin.jar 文件不存在，跳过备份"
fi

# 第三步：将ruoyi-admin.jar重命名为airport-admin.jar
echo "第三步：部署新版本..."
if [ -f "ruoyi-admin.jar" ]; then
    mv ruoyi-admin.jar airport-admin.jar
    echo "部署完成: ruoyi-admin.jar -> airport-admin.jar"
else
    echo "错误: ruoyi-admin.jar 文件不存在，无法部署"
    # 如果新文件不存在，恢复备份
    if [ -f "airport-admin.jar$DATE" ]; then
        mv airport-admin.jar$DATE airport-admin.jar
        echo "已恢复备份文件"
    fi
    exit 1
fi

# 第四步：启动服务
echo "第四步：启动服务..."
./amb.sh start
if [ $? -eq 0 ]; then
    echo "服务启动成功"
    echo "部署流程完成！"
else
    echo "服务启动失败，请检查日志"
    exit 1
fi

echo "所有步骤执行完毕"
