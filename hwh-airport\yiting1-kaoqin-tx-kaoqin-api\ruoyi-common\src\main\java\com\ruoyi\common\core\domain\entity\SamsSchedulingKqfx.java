package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 分析考勤报对象 sams_scheduling_kqfx
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
@Data
public class SamsSchedulingKqfx extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 分析ID
     */
    private Long kqfxId;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场ID
     */
    @Excel(name = "机场ID")
    private Long airportId;

    /**
     * 区域ID
     */
    @Excel(name = "区域ID")
    private Long areaId;

    /**
     * 大队ID
     */
    @Excel(name = "大队ID")
    private Long groupId;

    /**
     * 小组ID
     */
    @Excel(name = "小组ID")
    private Long teamId;

    /**
     * 岗位ID
     */
    private Long projectId;

    /**
     * 岗位ID
     */
    @Excel(name = "岗位ID")
    private Long postId;

    /**
     * 员工信息ID
     */
    @Excel(name = "员工信息ID")
    private Long employeeId;
    @Excel(name = "年月")
    private String cxny;

    /**
     * 工作时长（分钟）
     */
    @Excel(name = "工作时长")
    private Long gzsc;

    /**
     * 加班时长（分钟）
     */
    @Excel(name = "加班时长")
    private Long jbsc;

    /**
     * 异常次数
     */
    @Excel(name = "异常次数")
    private Integer yccs;

    /**
     * 迟到次数
     */
    @Excel(name = "迟到次数")
    private Integer cdcs;

    @Excel(name = "旷工次数")
    private Integer kgcs;

    @Excel(name = "早退次数")
    private Integer ztcs;

    @Excel(name = "班次统计")
    private Integer bctj;
    @Excel(name = "加班次数")
    private Integer jbcs;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SysDept sysDept;
    private SamsAirport samsAirport;
    private SamsAirportArea samsAirportArea;
    private SamsAirportGroup samsAirportGroup;
    private SamsAirportTeam samsAirportTeam;
    private SamsEmployee samsEmployee;
    private String name;
    private String mobilePhone;
}
