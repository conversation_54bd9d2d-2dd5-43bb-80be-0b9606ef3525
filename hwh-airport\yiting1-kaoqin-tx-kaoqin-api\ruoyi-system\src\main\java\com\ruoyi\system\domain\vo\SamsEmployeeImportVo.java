package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 员工信息导入对象
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
public class SamsEmployeeImportVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /** 部门 */
    @Excel(name = "部门")
    private String deptName;

    /** 机场机场 */
    @Excel(name = "机场")
    private String airport;

    /** 区域 */
    @Excel(name = "区域")
    private String area;

    /** 组别 */
    @Excel(name = "组别")
    private String group;

    /** 大队 */
    @Excel(name = "大队")
    private String team;

    /** 员工编号 */
    @Excel(name = "员工编号")
    private String empNo;

    /** 手机号 */
    @Excel(name = "手机号码")
    private String mobilePhone;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 汉语拼音 */
    @Excel(name = "拼音")
    private String namePinyin;

    /** 姓名简拼 */
    @Excel(name = "姓名简拼")
    private String nameJianpin;

    /** 性别 0男，1女 */
    @Excel(name = "性别")
    private String sexName;

    /** 民族 */
    @Excel(name = "民族")
    private String nation;

    /** 类别 1-本级员工 ,2-劳务派遣,3-供应商 */
    @Excel(name = "类别")
    private String type;

    /** 证件类型 0身份证，1护照，2军官证 */
    @Excel(name = "证件类型")
    private String card;

    /** 证件号码 */
    @Excel(name = "证件号码")
    private String cardId;

    /** 籍贯 */
    @Excel(name = "籍贯")
    private String jiguan;

    /** 户籍地址 */
    @Excel(name = "户籍地址")
    private String domicileAddr;

    /** 家庭住址 */
    @Excel(name = "家庭住址")
    private String homeAddr;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthDate;

    /** 户口性质 0 城市户口，1农业户口 */
    @Excel(name = "户口性质")
    private String domicileType;

    /** 政治面貌 0群众，1中共党员 */
    @Excel(name = "政治面貌")
    private String polotocalStatusDesc;

    /** 健康状况 */
    @Excel(name = "健康状况")
    private String healthStatus;

    /** 婚姻状况 */
    @Excel(name = "婚姻状况")
    private String maritalStatus;

    /** 学历 0小学，1初中，2高中，3大专，4本科，5硕士，6博士 */
    @Excel(name = "学历")
    private String educationDesc;

    /** 专业 */
    @Excel(name = "专业")
    private String major;

    /** 学校 */
    @Excel(name = "学校")
    private String school;

    /** 学历证书照片 */
    @Excel(name = "毕业证书")
    private String educationImg;

    /** 学位 */
    @Excel(name = "学位")
    private String degree;

    /** 特长 */
    @Excel(name = "特长")
    private String talent;

    /** 岗位 */
    @Excel(name = "岗位")
    private String postName;

    /** 参加工作日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "参加工作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cjgzDate;

    /** 紧急联系人 */
    @Excel(name = "紧急联系人")
    private String emContact;

    /** 紧急联系人联系方式 */
    @Excel(name = "紧急联系人联系方式")
    private String emContactPhone;

    /** 入职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ruzhiDate;

    /** 离职日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离职日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lizhiDate;

    /** 司龄 */
    @Excel(name = "司龄")
    private String gsYear;

    /** 合同到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "合同到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date htComplateDate;

    /** 退休日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退休日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tuixiuDate;

    /** 绑定小程序手机号 */
    @Excel(name = "绑定小程序手机号")
    private String bindPhone;

    /** OpenID */
    @Excel(name = "OpenID")
    private String openId;

    /** 昵称 */
    @Excel(name = "昵称")
    private String nicheng;

    /** 头像 */
    @Excel(name = "头像")
    private String avate;

    /** 状态（0启用 1禁用） */
    @Excel(name = "状态", readConverterExp = "0=启用,1=禁用")
    private String status;

    /**
     * 排班权限 0无，1有
     */
    @Excel(name = "排班权限")
    private String pbqxDesc;
    @Excel(name = "出生地")
    private String birthAddress;

    @Excel(name = "户口所在地")
    private String hkszd;

    @Excel(name = "备注")
    private String remark;

    private Integer sex;

    private Integer typeName;

    /** 证件类型 0身份证，1护照，2军官证 */
    private Integer cardType;
    /** 户口性质 0 城市户口，1农业户口 */
    private Integer domicile;

    /** 政治面貌 0群众，1中共党员 */
    private Integer polotocalStatus;

    /** 学历 0小学，1初中，2高中，3大专，4本科，5硕士，6博士 */
    private Integer education;
    private Integer pbqx;
}
