# 考勤数据修复使用说明

## 概述
由于之前的考勤分析逻辑存在跨天班次处理问题，导致历史统计数据不准确。现在提供数据修复工具来重新计算和更新历史考勤分析数据。

## 修复方案

### 1. 自动修复（推荐）
通过API接口进行数据修复，系统会自动处理所有逻辑。

#### 接口1：修复指定时间范围的数据
```
POST /system/attendanceRepair/repairHistory
参数：
- startDate: 开始日期（格式：yyyy-MM-dd，如：2024-01-01）
- endDate: 结束日期（格式：yyyy-MM-dd，如：2024-12-31）
```

#### 接口2：修复指定月份的数据
```
POST /system/attendanceRepair/repairMonth
参数：
- yearMonth: 年月（格式：yyyy-MM，如：2024-12）
```

### 2. 手动修复（高级用户）
如果需要更精细的控制，可以直接调用KqfxJob中的方法。

## 修复流程

### 步骤1：备份数据（重要！）
在执行修复前，建议备份考勤分析表：
```sql
-- 备份考勤分析数据
CREATE TABLE sams_scheduling_kqfx_backup_20241219 AS 
SELECT * FROM sams_scheduling_kqfx;
```

### 步骤2：执行修复
根据需要修复的时间范围选择合适的接口：

#### 修复整年数据
```bash
curl -X POST "http://your-domain/system/attendanceRepair/repairHistory" \
  -d "startDate=2024-01-01&endDate=2024-12-31"
```

#### 修复单个月份
```bash
curl -X POST "http://your-domain/system/attendanceRepair/repairMonth" \
  -d "yearMonth=2024-12"
```

### 步骤3：验证修复结果
1. 查看系统日志，确认修复过程无错误
2. 对比修复前后的统计数据
3. 抽查几个具体案例，验证数据准确性

## 修复逻辑说明

### 1. 数据清理
- 删除指定时间范围内的所有考勤分析数据
- 避免重复数据和累加错误

### 2. 重新计算
- 使用修复后的逻辑重新计算考勤统计
- 正确处理跨天班次
- 准确判断旷工、迟到、早退等状态

### 3. 统计项目
修复的统计项目包括：
- **班次统计(bctj)**: 正常班次次数
- **工作时长(gzsc)**: 总工作时长（分钟）
- **加班次数(jbcs)**: 加班班次次数
- **加班时长(jbsc)**: 加班工作时长（分钟）
- **迟到次数(cdcs)**: 迟到统计
- **旷工次数(kgcs)**: 旷工统计
- **早退次数(ztcs)**: 早退统计

## 注意事项

### 1. 执行时间
- 建议在业务低峰期执行修复
- 大量数据修复可能需要较长时间
- 修复过程中不影响正常考勤功能

### 2. 权限要求
- 需要 `system:attendanceRepair:repair` 权限
- 建议由系统管理员执行

### 3. 监控建议
- 执行前后对比关键统计数据
- 关注系统日志中的错误信息
- 验证修复后的数据合理性

### 4. 回滚方案
如果修复后发现问题，可以通过备份数据回滚：
```sql
-- 回滚到修复前状态
DELETE FROM sams_scheduling_kqfx;
INSERT INTO sams_scheduling_kqfx SELECT * FROM sams_scheduling_kqfx_backup_20241219;
```

## 常见问题

### Q1: 修复需要多长时间？
A: 取决于数据量，通常每月数据需要几分钟到几十分钟。

### Q2: 修复过程中系统是否可用？
A: 可用，修复只影响考勤分析数据，不影响正常打卡和排班功能。

### Q3: 如何验证修复效果？
A: 可以选择几个已知的考勤案例，手工计算后与系统统计对比。

### Q4: 修复失败怎么办？
A: 查看系统日志定位错误原因，必要时联系技术支持。

## 技术支持
如遇到问题，请提供：
1. 错误日志
2. 修复的时间范围
3. 具体的错误现象

联系方式：[技术支持邮箱或电话]
