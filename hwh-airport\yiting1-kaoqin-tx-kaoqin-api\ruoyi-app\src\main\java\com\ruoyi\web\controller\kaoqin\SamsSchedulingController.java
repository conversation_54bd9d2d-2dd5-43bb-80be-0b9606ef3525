package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SamsScheduling;
import com.ruoyi.system.service.ISamsSchedulingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.List;

/**
 * 排班信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@RestController
@RequestMapping("/app/scheduling")
public class SamsSchedulingController extends BaseController {
    @Autowired
    private ISamsSchedulingService samsSchedulingService;

    /**
     * 查询排班信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsScheduling samsScheduling) {
        startPage();
        List<SamsScheduling> list = samsSchedulingService.selectSamsSchedulingList(samsScheduling);
        return getDataTable(list);
    }

    /**
     * 导出排班信息列表
     */
    @Log(title = "排班信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsScheduling samsScheduling) {
        List<SamsScheduling> list = samsSchedulingService.selectSamsSchedulingList(samsScheduling);
        ExcelUtil<SamsScheduling> util = new ExcelUtil<SamsScheduling>(SamsScheduling.class);
        util.exportExcel(response, list, "排班信息数据");
    }

    /**
     * 获取排班信息详细信息
     */
    @GetMapping(value = "/{schedulingId}")
    public AjaxResult getInfo(@PathVariable("schedulingId") Long schedulingId) {
        return success(samsSchedulingService.selectSamsSchedulingBySchedulingId(schedulingId));
    }

    /**
     * 新增排班信息(快速排班)
     */
    @PostMapping
    public AjaxResult add(@RequestBody SamsScheduling samsScheduling) {
        return toAjax(samsSchedulingService.insertSamsScheduling(samsScheduling));
    }

    /**
     * 修改排班信息
     */
    @Log(title = "排班信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsScheduling samsScheduling) throws ParseException {
        return toAjax(samsSchedulingService.updateSamsScheduling(samsScheduling));
    }

    /**
     * 删除排班信息
     */
    @Log(title = "排班信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{schedulingIds}")
    public AjaxResult remove(@PathVariable Long[] schedulingIds) {
        return toAjax(samsSchedulingService.deleteSamsSchedulingBySchedulingIds(schedulingIds));
    }
}
