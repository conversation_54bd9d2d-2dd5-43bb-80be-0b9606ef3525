package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SamsAirport;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.mapper.SamsAirportMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISamsAirportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机场信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsAirportServiceImpl implements ISamsAirportService {
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询机场信息
     *
     * @param airportId 机场信息主键
     * @return 机场信息
     */
    @Override
    public SamsAirport selectSamsAirportByAirportId(Long airportId) {
        return samsAirportMapper.selectSamsAirportByAirportId(airportId);
    }

    /**
     * 查询机场信息列表
     *
     * @param samsAirport 机场信息
     * @return 机场信息
     */
    @Override
    public List<SamsAirport> selectSamsAirportList(SamsAirport samsAirport) {
        List<SamsAirport> list = samsAirportMapper.selectSamsAirportList(samsAirport);
        if (list != null && list.size() > 0) {
            for (SamsAirport airport : list) {
                airport.setSysDept(sysDeptMapper.selectDeptById(airport.getDeptId()));
            }
        }
        return list;
    }

    /**
     * 新增机场信息
     *
     * @param samsAirport 机场信息
     * @return 结果
     */
    @Override
    public int insertSamsAirport(SamsAirport samsAirport) {
        samsAirport.setCreateTime(DateUtils.getNowDate());
        return samsAirportMapper.insertSamsAirport(samsAirport);
    }

    /**
     * 修改机场信息
     *
     * @param samsAirport 机场信息
     * @return 结果
     */
    @Override
    public int updateSamsAirport(SamsAirport samsAirport) {
        samsAirport.setUpdateTime(DateUtils.getNowDate());
        return samsAirportMapper.updateSamsAirport(samsAirport);
    }

    /**
     * 批量删除机场信息
     *
     * @param airportIds 需要删除的机场信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportByAirportIds(Long[] airportIds) {
        return samsAirportMapper.deleteSamsAirportByAirportIds(airportIds);
    }

    /**
     * 删除机场信息信息
     *
     * @param airportId 机场信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsAirportByAirportId(Long airportId) {
        return samsAirportMapper.deleteSamsAirportByAirportId(airportId);
    }
}
