package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SamsSupplier;
import com.ruoyi.system.service.ISamsSupplierService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 供应商信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/supplier")
public class SamsSupplierController extends BaseController
{
    @Autowired
    private ISamsSupplierService samsSupplierService;

    /**
     * 查询供应商信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsSupplier samsSupplier)
    {
        startPage();
        List<SamsSupplier> list = samsSupplierService.selectSamsSupplierList(samsSupplier);
        return getDataTable(list);
    }

    /**
     * 导出供应商信息列表
     */
    @Log(title = "供应商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsSupplier samsSupplier)
    {
        List<SamsSupplier> list = samsSupplierService.selectSamsSupplierList(samsSupplier);
        ExcelUtil<SamsSupplier> util = new ExcelUtil<SamsSupplier>(SamsSupplier.class);
        util.exportExcel(response, list, "供应商信息数据");
    }

    /**
     * 获取供应商信息详细信息
     */
    @GetMapping(value = "/{supplierId}")
    public AjaxResult getInfo(@PathVariable("supplierId") Long supplierId)
    {
        return success(samsSupplierService.selectSamsSupplierBySupplierId(supplierId));
    }

    /**
     * 新增供应商信息
     */
    @Log(title = "供应商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsSupplier samsSupplier)
    {
        return toAjax(samsSupplierService.insertSamsSupplier(samsSupplier));
    }

    /**
     * 修改供应商信息
     */
    @Log(title = "供应商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsSupplier samsSupplier)
    {
        return toAjax(samsSupplierService.updateSamsSupplier(samsSupplier));
    }

    /**
     * 删除供应商信息
     */
    @Log(title = "供应商信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{supplierIds}")
    public AjaxResult remove(@PathVariable Long[] supplierIds)
    {
        return toAjax(samsSupplierService.deleteSamsSupplierBySupplierIds(supplierIds));
    }
}
