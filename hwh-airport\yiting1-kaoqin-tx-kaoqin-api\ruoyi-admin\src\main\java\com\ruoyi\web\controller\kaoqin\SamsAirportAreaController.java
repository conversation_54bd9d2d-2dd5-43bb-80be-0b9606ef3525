package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.domain.entity.SamsAirportArea;
import com.ruoyi.system.service.ISamsAirportAreaService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 机场区域信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/airportArea")
public class SamsAirportAreaController extends BaseController
{
    @Autowired
    private ISamsAirportAreaService samsAirportAreaService;

    /**
     * 查询机场区域信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirportArea samsAirportArea)
    {
        startPage();
        List<SamsAirportArea> list = samsAirportAreaService.selectSamsAirportAreaList(samsAirportArea);
        return getDataTable(list);
    }

    /**
     * 导出机场区域信息列表
     */
    @Log(title = "机场区域信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsAirportArea samsAirportArea)
    {
        List<SamsAirportArea> list = samsAirportAreaService.selectSamsAirportAreaList(samsAirportArea);
        ExcelUtil<SamsAirportArea> util = new ExcelUtil<SamsAirportArea>(SamsAirportArea.class);
        util.exportExcel(response, list, "机场区域信息数据");
    }

    /**
     * 获取机场区域信息详细信息
     */
    @GetMapping(value = "/{areaId}")
    public AjaxResult getInfo(@PathVariable("areaId") Long areaId)
    {
        return success(samsAirportAreaService.selectSamsAirportAreaByAreaId(areaId));
    }

    /**
     * 新增机场区域信息
     */
    @Log(title = "机场区域信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsAirportArea samsAirportArea)
    {
        return toAjax(samsAirportAreaService.insertSamsAirportArea(samsAirportArea));
    }

    /**
     * 修改机场区域信息
     */
    @Log(title = "机场区域信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsAirportArea samsAirportArea)
    {
        return toAjax(samsAirportAreaService.updateSamsAirportArea(samsAirportArea));
    }

    /**
     * 删除机场区域信息
     */
    @Log(title = "机场区域信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{areaIds}")
    public AjaxResult remove(@PathVariable Long[] areaIds)
    {
        return toAjax(samsAirportAreaService.deleteSamsAirportAreaByAreaIds(areaIds));
    }
}
