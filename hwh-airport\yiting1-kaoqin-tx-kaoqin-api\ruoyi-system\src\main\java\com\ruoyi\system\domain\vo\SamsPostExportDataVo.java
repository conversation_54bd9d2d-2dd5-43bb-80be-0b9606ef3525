package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 岗位导出对象
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
public class SamsPostExportDataVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    private Long postId;
    /**
     * 岗位名称
     */
    @Excel(name = "岗位名称")
    private String postName;
    /**
     * 部门
     */
    @Excel(name = "部门")
    private String deptName;

    /**
     * 机场机场
     */
    @Excel(name = "机场")
    private String airport;

    /**
     * 区域
     */
    @Excel(name = "区域")
    private String area;

    /**
     * 大队
     */
    @Excel(name = "大队")
    private String group;

    /**
     * 组别
     */
    @Excel(name = "组别")
    private String team;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectName;

    /**
     * 岗位类别
     */
    @Excel(name = "岗位类别")
    private String postNature;

    @Excel(name = "排班状态")
    private String pb;

    @Excel(name = "最后排班日期")
    private String lastPbrq;

    /**
     * 打卡开始时间
     */
    @Excel(name = "打卡开始时间")
    private String startTime;

    /**
     * 打卡开始时间前
     */
    @Excel(name = "打卡开始时间前")
    private String startTimeBefore;
    /**
     * 打卡开始时间后
     */
    @Excel(name = "打卡开始时间后")
    private String startTimeAfter;

    /**
     * 打卡结束时间
     */
    @Excel(name = "打卡结束时间")
    private String endTime;

    /**
     * 打卡结束时间前
     */
    @Excel(name = "打卡结束时间前")
    private String endTimeBefore;

    /**
     * 打卡结束时间后
     */
    @Excel(name = "打卡结束时间后")
    private String endTimeAfter;

    /**
     * 打卡地点
     */
    @Excel(name = "打卡地点")
    private String clockAddress;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String latitude;

    /**
     * 维度
     */
    @Excel(name = "维度")
    private String longitude;

    /**
     * 打卡范围
     */
    @Excel(name = "打卡范围")
    private Long clockRange;

    /**
     * 工作范围
     */
    @Excel(name = "工作范围")
    private Long workRange;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    @Excel(name = "打卡名称")
    private String clockName;
    @Excel(name = "地点命名")
    private String ddmm;
}

