<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsEmployeeApplyMapper">
    <resultMap type="SamsEmployeeApply" id="SamsEmployeeApplyResult">
        <result property="employeeApplyId" column="employee_apply_id"/>
        <result property="projectId" column="project_id"/>
        <result property="employeeId" column="employee_id"/>
        <result property="postId" column="post_id"/>
        <result property="applyStype" column="apply_stype"/>
        <result property="applyReson" column="apply_reson"/>
        <result property="applyImage" column="apply_image"/>
        <result property="applyTime" column="apply_time"/>
        <result property="authTime" column="auth_time"/>
        <result property="authStatus" column="auth_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>

        <association property="samsEmployee" javaType="SamsEmployee">
            <id property="employeeId" column="employee_id"/>
            <result property="name" column="name"/>
            <result property="mobilePhone" column="mobile_phone"/>
            <result property="deptId" column="dept_id"/>
        </association>
        <association property="sysDept" javaType="SysDept">
            <id property="deptId" column="dept_id"/>
            <result property="deptName" column="dept_name"/>
        </association>
        <association property="samsProject" javaType="SamsProject">
            <id property="projectId" column="project_id"/>
            <result property="projectName" column="project_name"/>
            <result property="projectType" column="project_type"/>
            <result property="projectStatus" column="project_status"/>
            <result property="projectStartTime" column="project_start_time"/>
            <result property="projectEndTime" column="project_end_time"/>
            <result property="projectImage" column="project_image"/>
        </association>
        <association property="samsPost" javaType="SamsPost">
            <id property="postId" column="post_id"/>
            <result property="postName" column="post_name"/>
            <result property="postStatus" column="post_status"/>
        </association>
    </resultMap>

    <sql id="selectSamsEmployeeApplyVo">
        select ea.employee_apply_id,
               ea.project_id,
               ea.employee_id,
               ea.post_id,
               ea.apply_stype,
               ea.apply_reson,
               ea.apply_image,
               ea.apply_time,
               ea.auth_time,
               ea.auth_status,
               ea.del_flag,
               ea.create_by,
               ea.create_time,
               ea.update_by,
               ea.update_time,
               ea.remark,
               e.name,
               e.dept_id,
               e.mobile_phone,
               sd.dept_name,
               sp.project_name,
               sps.post_name
        from sams_employee_apply ea
                 join sams_employee as e on ea.employee_id = e.employee_id
                 left join sys_dept sd on e.dept_id = sd.dept_id
                 left join sams_project as sp on ea.project_id = sp.project_id
                 join sams_post as sps on ea.post_id = sps.post_id
    </sql>

    <select id="selectSamsEmployeeApplyList" parameterType="SamsEmployeeApply" resultMap="SamsEmployeeApplyResult">
        select ea.*,
        e.name,
        e.dept_id,
        e.mobile_phone,
        sd.dept_name,
        sp.project_name,
        sps.post_name from sams_employee_apply as ea join sams_employee as e on ea.employee_id = e.employee_id
        left join sys_dept sd on e.dept_id = sd.dept_id
        left join sams_project as sp on ea.project_id = sp.project_id
        join sams_post as sps on ea.post_id = sps.post_id
        <where>
            <if test="projectId != null ">
                and ea.project_id = #{projectId}
            </if>
            <if test="employeeId != null ">
                and ea.employee_id = #{employeeId}
            </if>
            <if test="applyStype != null ">
                and ea.apply_stype = #{applyStype}
            </if>
            <if test="name != null  and name != ''">
                and e.name like concat('%',#{name},'%')
            </if>
            <if test="phone != null  and phone != ''">
                and e.mobile_phone like concat('%',#{phone},'%')
            </if>
        </where>
    </select>

    <select id="selectSamsEmployeeApplyByEmployeeApplyId" parameterType="Long" resultMap="SamsEmployeeApplyResult">
        <include refid="selectSamsEmployeeApplyVo"/>
        where ea.employee_apply_id = #{employeeApplyId}
    </select>

    <insert id="insertSamsEmployeeApply" parameterType="SamsEmployeeApply" useGeneratedKeys="true"
            keyProperty="employeeApplyId">
        insert into sams_employee_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">
                project_id,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="postId != null">
                post_id,
            </if>
            <if test="applyStype != null">
                apply_stype,
            </if>
            <if test="applyReson != null">
                apply_reson,
            </if>
            <if test="applyImage != null">
                apply_image,
            </if>
            <if test="applyTime != null">
                apply_time,
            </if>
            <if test="authTime != null">
                auth_time,
            </if>
            <if test="authStatus != null">
                auth_status,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">
                #{projectId},
            </if>
            <if test="employeeId != null">
                #{employeeId},
            </if>
            <if test="postId != null">
                #{postId},
            </if>
            <if test="applyStype != null">
                #{applyStype},
            </if>
            <if test="applyReson != null">
                #{applyReson},
            </if>
            <if test="applyImage != null">
                #{applyImage},
            </if>
            <if test="applyTime != null">
                #{applyTime},
            </if>
            <if test="authTime != null">
                #{authTime},
            </if>
            <if test="authStatus != null">
                #{authStatus},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
        </trim>
    </insert>

    <update id="updateSamsEmployeeApply" parameterType="SamsEmployeeApply">
        update sams_employee_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">
                project_id = #{projectId},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId},
            </if>
            <if test="postId != null">
                post_id = #{postId},
            </if>
            <if test="applyStype != null">
                apply_stype = #{applyStype},
            </if>
            <if test="applyReson != null">
                apply_reson = #{applyReson},
            </if>
            <if test="applyImage != null">
                apply_image = #{applyImage},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime},
            </if>
            <if test="authTime != null">
                auth_time = #{authTime},
            </if>
            <if test="authStatus != null">
                auth_status = #{authStatus},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </trim>
        where employee_apply_id = #{employeeApplyId}
    </update>

    <delete id="deleteSamsEmployeeApplyByEmployeeApplyId" parameterType="Long">
        delete
        from sams_employee_apply
        where employee_apply_id = #{employeeApplyId}
    </delete>

    <delete id="deleteSamsEmployeeApplyByEmployeeApplyIds" parameterType="String">
        delete from sams_employee_apply where employee_apply_id in
        <foreach item="employeeApplyId" collection="array" open="(" separator="," close=")">
            #{employeeApplyId}
        </foreach>
    </delete>
</mapper>
