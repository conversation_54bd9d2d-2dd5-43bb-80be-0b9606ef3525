package com.ruoyi.job;

import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SamsSchedulingEmployee;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxQueryVo;
import com.ruoyi.system.domain.vo.SamsSchedulingPostEmployeeVo;
import com.ruoyi.system.mapper.SamsEmployeeMapper;
import com.ruoyi.system.mapper.SamsSchedulingEmployeeMapper;
import com.ruoyi.system.mapper.SamsSchedulingKqfxMapper;
import com.ruoyi.system.mapper.SamsSchedulingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.ruoyi.common.utils.DateUtils.getTime4fenzhong;

/**
 * 考勤分析
 */
@Slf4j
@Component
public class KqfxJob {
    @Autowired
    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;
    @Autowired
    private SamsSchedulingKqfxMapper samsSchedulingKqfxMapper;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;
    @Autowired
    private SamsSchedulingMapper samsSchedulingMapper;

    /**
     * 凌晨4点检查一下考勤数据
     */
    @Scheduled(cron = "0 0 4 * * ?")
//    @Scheduled(fixedRate = 5)
    public void kqsjJobTasks() {
        log.info("凌晨4点检查一下考勤数据####开始#####");
        List<SamsSchedulingPostEmployeeVo> employeeVos = samsSchedulingEmployeeMapper.selectYestDayKqsj2();
        if (!CollectionUtils.isEmpty(employeeVos)) {
            for (SamsSchedulingPostEmployeeVo samsSchedulingPostEmployeeVo : employeeVos) {
                List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectByEmployeeId2(samsSchedulingPostEmployeeVo.getEmployeeId(),
                        samsSchedulingPostEmployeeVo.getPostId(), samsSchedulingPostEmployeeVo.getSchedulingId());
                if (schedulingEmployeeList != null && schedulingEmployeeList.size() > 0) {
                    if (schedulingEmployeeList.size() == 1) {
                        // 判断是上班，则直接跳过
                        if (schedulingEmployeeList.get(0).getDkType().equals(1) || StringUtils.isBlank(schedulingEmployeeList.get(0).getDkTag())) {
                            continue;
                        }
                        // 如果是下班，需要查询对应的上班进行统计
                        List<SamsSchedulingEmployee> samsSchedulingEmployees = samsSchedulingEmployeeMapper.selectByDkTag(schedulingEmployeeList.get(0).getDkTag());
                        if (null == samsSchedulingEmployees || samsSchedulingEmployees.size() != 2) {
                            continue;
                        }
                        schedulingEmployeeList = samsSchedulingEmployees;
                    }
                    SamsEmployee employee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(samsSchedulingPostEmployeeVo.getEmployeeId());
                    if (null == employee) {
                        continue;
                    }
                    SamsSchedulingKqfxQueryVo queryVo = new SamsSchedulingKqfxQueryVo();
                    queryVo.setDeptId(employee.getDeptId());
                    queryVo.setAirportId(employee.getAirportId());
                    queryVo.setAreaId(employee.getAreaId());
                    queryVo.setGroupId(employee.getGroupId());
                    queryVo.setTeamId(employee.getTeamId());
                    queryVo.setEmployeeId(employee.getEmployeeId());
                    queryVo.setPostId(samsSchedulingPostEmployeeVo.getPostId());
                    queryVo.setCxny(DateUtils.parseDateToStr("yyyy-MM", org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -1)));
                    SamsSchedulingKqfx samsSchedulingKqfx = samsSchedulingKqfxMapper.selectByQueryVo(queryVo);
                    if (samsSchedulingKqfx == null) {
                        samsSchedulingKqfx = new SamsSchedulingKqfx();
                        samsSchedulingKqfx.setDeptId(employee.getDeptId());
                        samsSchedulingKqfx.setAirportId(employee.getAirportId());
                        samsSchedulingKqfx.setAreaId(employee.getAreaId());
                        samsSchedulingKqfx.setGroupId(employee.getGroupId());
                        samsSchedulingKqfx.setTeamId(employee.getTeamId());
                        samsSchedulingKqfx.setEmployeeId(employee.getEmployeeId());
                        samsSchedulingKqfx.setPostId(samsSchedulingPostEmployeeVo.getPostId());
                        samsSchedulingKqfx.setCxny(DateUtils.parseDateToStr("yyyy-MM", org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -1)));
                    }

                    // 获取当前日期
                    Integer sfjb = schedulingEmployeeList.get(0).getSfjb();
                    if (schedulingEmployeeList.size() == 1) {
//                        SamsSchedulingEmployee schedulingEmployee = schedulingEmployeeList.get(0);
////                        samsSchedulingKqfx.setGzsc(samsSchedulingKqfx.getGzsc() == null ? 0 : samsSchedulingKqfx.getGzsc());
////                        samsSchedulingKqfx.setJbsc(samsSchedulingKqfx.getJbsc() == null ? 0 : samsSchedulingKqfx.getJbsc());
//                        if (schedulingEmployee.getDkStatus() == null) {
//                            samsSchedulingKqfx.setKgcs(samsSchedulingKqfx.getKgcs() == null ? 1 : (samsSchedulingKqfx.getKgcs() + 1));
//                        } else {
//                            if (schedulingEmployee.getDkStatus().equals(3)) {
//                                samsSchedulingKqfx.setCdcs(samsSchedulingKqfx.getCdcs() == null ? 1 : (samsSchedulingKqfx.getCdcs() + 1));
//                            }
//                        }
                    } else {
                        int cdNum = 0;//迟到次数
                        int kgNum = 0;//旷工次数
                        int ztNum = 0;//早退次数
                        SamsSchedulingEmployee first = schedulingEmployeeList.get(0);
                        SamsSchedulingEmployee last = schedulingEmployeeList.get(schedulingEmployeeList.size() - 1);
                        if (null != first && last != null) {
                            // 修复旷工判断逻辑：更准确地判断旷工情况

                            // 判断上班打卡是否旷工
                            boolean firstAbsent = (first.getDkStatus() == null || first.getDkStatus().equals(2) || first.getDkStatus().equals(6));
                            // 判断下班打卡是否旷工
                            boolean lastAbsent = (last.getDkStatus() == null || last.getDkStatus().equals(2) || last.getDkStatus().equals(7));

                            // 如果上班或下班任一未打卡，且已超过允许打卡时间，则算旷工
                            if (firstAbsent || lastAbsent) {
                                // 检查是否已超过打卡时间限制
                                Date currentTime = new Date();
                                boolean isOverTime = false;

                                if (firstAbsent && StringUtils.isNotBlank(first.getStartTimeAfter())) {
                                    String dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd", first.getDutyDate());
                                    String dkAfterTime = dkTimeDeal(dutyDate + " " + first.getStartTime(), first.getStartTimeAfter());
                                    if (DateUtils.getTime4fenzhong(DateUtils.parseDate(dkAfterTime), currentTime) > 0) {
                                        isOverTime = true;
                                    }
                                }

                                if (lastAbsent && StringUtils.isNotBlank(last.getEndTimeAfter())) {
                                    String dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd", last.getDutyDate());
                                    // 处理跨天情况
                                    if (last.getNextDay() == 1) {
                                        dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd",
                                            org.apache.commons.lang3.time.DateUtils.addDays(last.getDutyDate(), 1));
                                    }
                                    String dkAfterTime = dkTimeDeal(dutyDate + " " + last.getEndTime(), last.getEndTimeAfter());
                                    if (DateUtils.getTime4fenzhong(DateUtils.parseDate(dkAfterTime), currentTime) > 0) {
                                        isOverTime = true;
                                    }
                                }

                                // 如果没有设置延长时间或已超时，则算旷工
                                if (isOverTime || (StringUtils.isBlank(first.getStartTimeAfter()) && StringUtils.isBlank(last.getEndTimeAfter()))) {
                                    kgNum++;
                                }
                            }
//                           if (first.getDkStatus() == null || first.getDkStatus().equals(2) || first.getDkStatus().equals(6)) {
//                               if (last.getDkStatus() == null || last.getDkStatus().equals(2) || last.getDkStatus().equals(7)) {
//                                   kgNum++;
//                               }
//                           }
                            if (first.getDkStatus() != null && first.getDkStatus().equals(3)) {
                                cdNum++;
                            }
                            if (last.getDkStatus() != null && last.getDkStatus().equals(3)) {
                                cdNum++;
                            }

                            if (first.getDkStatus() != null && first.getDkStatus().equals(5)) {
                                ztNum++;
                            }
                            if (last.getDkStatus() != null && last.getDkStatus().equals(5)) {
                                ztNum++;
                            }

                            // 修复工作时长计算逻辑：更完整地处理各种打卡状态组合
                            if (first.getDkStatus() != null && last.getDkStatus() != null &&
                                first.getDkDate() != null && last.getDkDate() != null) {

                                // 计算工作时长的条件：上班和下班都有有效打卡记录
                                boolean canCalculateWorkTime = false;

                                // 有效的打卡状态组合（排除旷工状态）
                                if ((first.getDkStatus().equals(1) || first.getDkStatus().equals(3)) && // 上班正常或迟到
                                    (last.getDkStatus().equals(1) || last.getDkStatus().equals(5))) {   // 下班正常或早退
                                    canCalculateWorkTime = true;
                                }

                                if (canCalculateWorkTime) {
                                    long workMinutes = getTime4fenzhong(first.getDkDate(), last.getDkDate());

                                    if (sfjb != null && sfjb.equals(1)) {
                                        // 加班时长统计
                                        samsSchedulingKqfx.setJbsc(samsSchedulingKqfx.getJbsc() == null ? workMinutes :
                                            samsSchedulingKqfx.getJbsc() + workMinutes);
                                    } else {
                                        // 正常班次统计
                                        samsSchedulingKqfx.setBctj(samsSchedulingKqfx.getBctj() == null ? 1 :
                                            (samsSchedulingKqfx.getBctj() + 1));
                                        samsSchedulingKqfx.setGzsc(samsSchedulingKqfx.getGzsc() == null ? workMinutes :
                                            samsSchedulingKqfx.getGzsc() + workMinutes);
                                    }
                                }
                            }
                        }
                        samsSchedulingKqfx.setCdcs(samsSchedulingKqfx.getCdcs() == null ? cdNum: samsSchedulingKqfx.getCdcs() + cdNum);
                        samsSchedulingKqfx.setKgcs(samsSchedulingKqfx.getKgcs() == null ? kgNum : samsSchedulingKqfx.getKgcs() + kgNum);
                        samsSchedulingKqfx.setZtcs(samsSchedulingKqfx.getZtcs() == null ? ztNum : samsSchedulingKqfx.getZtcs() + ztNum);
                    }
//                    samsSchedulingKqfx.setBctj(samsSchedulingKqfx.getBctj() == null ? 1 : samsSchedulingKqfx.getBctj() + 1);
                    if (sfjb != null && sfjb.equals(1)) {
                        samsSchedulingKqfx.setJbcs(samsSchedulingKqfx.getJbcs() == null ? 1 : (samsSchedulingKqfx.getJbcs() + 1));
                    }
                    if (samsSchedulingKqfx.getKqfxId() == null) {
                        samsSchedulingKqfx.setCreateTime(DateUtils.getNowDate());
                        samsSchedulingKqfxMapper.insertSamsSchedulingKqfx(samsSchedulingKqfx);
                    } else {
                        samsSchedulingKqfx.setUpdateTime(DateUtils.getNowDate());
                        samsSchedulingKqfxMapper.updateSamsSchedulingKqfx(samsSchedulingKqfx);
                    }
                }
            }
        }
        log.info("凌晨1点检查一下考勤数据#####结束####");
    }

    public String dkTimeDeal(String dkTime, String dkTimeAfter) {
        log.info("打卡时间：{}，延长时间：{}", dkTime, dkTimeAfter);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime punchTime = LocalDateTime.parse(dkTime, formatter);
        // 解析延长时间
        String[] delayParts = dkTimeAfter.split(":");
        int hoursToAdd = Integer.parseInt(delayParts[0]); // 获取小时
        int minutesToAdd = Integer.parseInt(delayParts[1]); // 获取分钟

        // 计算结束时间
        LocalDateTime endTime = punchTime.plusHours(hoursToAdd).plusMinutes(minutesToAdd);

        // 输出结果
        System.out.println("打卡结束时间: " + endTime.format(formatter));

        return endTime.format(formatter);
    }

    /**
     * 验证考勤数据的一致性
     * @param schedulingEmployeeList 排班员工列表
     * @return 是否通过验证
     */
    private boolean validateAttendanceData(List<SamsSchedulingEmployee> schedulingEmployeeList) {
        if (schedulingEmployeeList == null || schedulingEmployeeList.isEmpty()) {
            return false;
        }

        // 检查是否有重复的打卡标识
        Set<String> dkTags = new HashSet<>();
        for (SamsSchedulingEmployee employee : schedulingEmployeeList) {
            if (StringUtils.isNotBlank(employee.getDkTag())) {
                if (dkTags.contains(employee.getDkTag())) {
                    // 同一个打卡标识应该只有上班和下班两条记录
                    continue;
                }
                dkTags.add(employee.getDkTag());
            }
        }

        return true;
    }

    /**
     * 修复历史考勤分析数据
     * @param startDate 开始日期 格式：yyyy-MM-dd
     * @param endDate 结束日期 格式：yyyy-MM-dd
     */
    public void repairHistoryAttendanceData(String startDate, String endDate) {
        log.info("开始修复历史考勤分析数据，时间范围：{} 到 {}", startDate, endDate);

        try {
            Date start = DateUtils.parseDate(startDate);
            Date end = DateUtils.parseDate(endDate);

            // 按月份循环处理
            Date currentDate = start;
            while (!currentDate.after(end)) {
                String yearMonth = DateUtils.parseDateToStr("yyyy-MM", currentDate);
                log.info("正在处理月份：{}", yearMonth);

                // 删除该月份的所有考勤分析数据
                samsSchedulingKqfxMapper.deleteByYearMonth(yearMonth);

                // 重新计算该月份的考勤分析数据
                recalculateMonthlyAttendanceData(yearMonth);

                // 移动到下一个月
                currentDate = org.apache.commons.lang3.time.DateUtils.addMonths(currentDate, 1);
            }

            log.info("历史考勤分析数据修复完成");
        } catch (Exception e) {
            log.error("修复历史考勤分析数据失败", e);
        }
    }

    /**
     * 重新计算指定月份的考勤分析数据
     * @param yearMonth 年月 格式：yyyy-MM
     */
    private void recalculateMonthlyAttendanceData(String yearMonth) {
        log.info("重新计算月份 {} 的考勤分析数据", yearMonth);

        // 获取该月份的所有考勤数据
        List<SamsSchedulingPostEmployeeVo> employeeVos = samsSchedulingEmployeeMapper.selectAttendanceDataByMonth(yearMonth);

        if (!CollectionUtils.isEmpty(employeeVos)) {
            for (SamsSchedulingPostEmployeeVo employeeVo : employeeVos) {
                try {
                    // 获取该员工该月份的所有排班记录
                    List<SamsSchedulingEmployee> schedulingEmployeeList =
                        samsSchedulingEmployeeMapper.selectEmployeeAttendanceByMonth(
                            employeeVo.getEmployeeId(), employeeVo.getPostId(), yearMonth);

                    if (schedulingEmployeeList != null && !schedulingEmployeeList.isEmpty()) {
                        // 按打卡标识分组处理
                        Map<String, List<SamsSchedulingEmployee>> groupedByTag = new HashMap<>();
                        for (SamsSchedulingEmployee employee : schedulingEmployeeList) {
                            if (StringUtils.isNotBlank(employee.getDkTag())) {
                                groupedByTag.computeIfAbsent(employee.getDkTag(), k -> new ArrayList<>()).add(employee);
                            }
                        }

                        // 处理每个打卡组
                        for (List<SamsSchedulingEmployee> tagGroup : groupedByTag.values()) {
                            if (tagGroup.size() >= 2) {
                                processAttendanceGroup(tagGroup, employeeVo, yearMonth);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("处理员工 {} 的考勤数据失败", employeeVo.getEmployeeId(), e);
                }
            }
        }
    }

    /**
     * 处理考勤组数据
     */
    private void processAttendanceGroup(List<SamsSchedulingEmployee> schedulingEmployeeList,
                                      SamsSchedulingPostEmployeeVo employeeVo, String yearMonth) {

        // 排序：上班在前，下班在后
        schedulingEmployeeList.sort((a, b) -> {
            if (a.getDkType() != null && b.getDkType() != null) {
                return a.getDkType().compareTo(b.getDkType());
            }
            return 0;
        });

        SamsEmployee employee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(employeeVo.getEmployeeId());
        if (employee == null) {
            return;
        }

        // 查询或创建考勤分析记录
        SamsSchedulingKqfxQueryVo queryVo = new SamsSchedulingKqfxQueryVo();
        queryVo.setDeptId(employee.getDeptId());
        queryVo.setAirportId(employee.getAirportId());
        queryVo.setAreaId(employee.getAreaId());
        queryVo.setGroupId(employee.getGroupId());
        queryVo.setTeamId(employee.getTeamId());
        queryVo.setEmployeeId(employee.getEmployeeId());
        queryVo.setPostId(employeeVo.getPostId());
        queryVo.setCxny(yearMonth);

        SamsSchedulingKqfx samsSchedulingKqfx = samsSchedulingKqfxMapper.selectByQueryVo(queryVo);
        if (samsSchedulingKqfx == null) {
            samsSchedulingKqfx = new SamsSchedulingKqfx();
            samsSchedulingKqfx.setDeptId(employee.getDeptId());
            samsSchedulingKqfx.setAirportId(employee.getAirportId());
            samsSchedulingKqfx.setAreaId(employee.getAreaId());
            samsSchedulingKqfx.setGroupId(employee.getGroupId());
            samsSchedulingKqfx.setTeamId(employee.getTeamId());
            samsSchedulingKqfx.setEmployeeId(employee.getEmployeeId());
            samsSchedulingKqfx.setPostId(employeeVo.getPostId());
            samsSchedulingKqfx.setCxny(yearMonth);
            // 初始化所有统计字段为0
            samsSchedulingKqfx.setBctj(0);
            samsSchedulingKqfx.setGzsc(0L);
            samsSchedulingKqfx.setJbcs(0);
            samsSchedulingKqfx.setJbsc(0L);
            samsSchedulingKqfx.setCdcs(0);
            samsSchedulingKqfx.setKgcs(0);
            samsSchedulingKqfx.setZtcs(0);
        }

        // 使用修复后的逻辑重新计算统计数据
        calculateAttendanceStatistics(schedulingEmployeeList, samsSchedulingKqfx);

        // 保存或更新记录
        if (samsSchedulingKqfx.getKqfxId() == null) {
            samsSchedulingKqfx.setCreateTime(DateUtils.getNowDate());
            samsSchedulingKqfxMapper.insertSamsSchedulingKqfx(samsSchedulingKqfx);
        } else {
            samsSchedulingKqfx.setUpdateTime(DateUtils.getNowDate());
            samsSchedulingKqfxMapper.updateSamsSchedulingKqfx(samsSchedulingKqfx);
        }
    }

    /**
     * 计算考勤统计数据（使用修复后的逻辑）
     */
    private void calculateAttendanceStatistics(List<SamsSchedulingEmployee> schedulingEmployeeList,
                                             SamsSchedulingKqfx samsSchedulingKqfx) {

        if (schedulingEmployeeList.size() < 2) {
            return;
        }

        SamsSchedulingEmployee first = schedulingEmployeeList.get(0);
        SamsSchedulingEmployee last = schedulingEmployeeList.get(schedulingEmployeeList.size() - 1);
        Integer sfjb = first.getSfjb();

        int cdNum = 0; // 迟到次数
        int kgNum = 0; // 旷工次数
        int ztNum = 0; // 早退次数

        if (first != null && last != null) {
            // 使用修复后的旷工判断逻辑
            boolean firstAbsent = (first.getDkStatus() == null || first.getDkStatus().equals(2) || first.getDkStatus().equals(6));
            boolean lastAbsent = (last.getDkStatus() == null || last.getDkStatus().equals(2) || last.getDkStatus().equals(7));

            if (firstAbsent || lastAbsent) {
                kgNum++;
            }

            // 迟到统计
            if (first.getDkStatus() != null && first.getDkStatus().equals(3)) {
                cdNum++;
            }
            if (last.getDkStatus() != null && last.getDkStatus().equals(3)) {
                cdNum++;
            }

            // 早退统计
            if (first.getDkStatus() != null && first.getDkStatus().equals(5)) {
                ztNum++;
            }
            if (last.getDkStatus() != null && last.getDkStatus().equals(5)) {
                ztNum++;
            }

            // 工作时长计算
            if (first.getDkStatus() != null && last.getDkStatus() != null &&
                first.getDkDate() != null && last.getDkDate() != null) {

                if ((first.getDkStatus().equals(1) || first.getDkStatus().equals(3)) &&
                    (last.getDkStatus().equals(1) || last.getDkStatus().equals(5))) {

                    long workMinutes = getTime4fenzhong(first.getDkDate(), last.getDkDate());

                    if (sfjb != null && sfjb.equals(1)) {
                        samsSchedulingKqfx.setJbsc((samsSchedulingKqfx.getJbsc() == null ? 0 : samsSchedulingKqfx.getJbsc()) + workMinutes);
                    } else {
                        samsSchedulingKqfx.setBctj((samsSchedulingKqfx.getBctj() == null ? 0 : samsSchedulingKqfx.getBctj()) + 1);
                        samsSchedulingKqfx.setGzsc((samsSchedulingKqfx.getGzsc() == null ? 0 : samsSchedulingKqfx.getGzsc()) + workMinutes);
                    }
                }
            }
        }

        // 累加统计数据
        samsSchedulingKqfx.setCdcs((samsSchedulingKqfx.getCdcs() == null ? 0 : samsSchedulingKqfx.getCdcs()) + cdNum);
        samsSchedulingKqfx.setKgcs((samsSchedulingKqfx.getKgcs() == null ? 0 : samsSchedulingKqfx.getKgcs()) + kgNum);
        samsSchedulingKqfx.setZtcs((samsSchedulingKqfx.getZtcs() == null ? 0 : samsSchedulingKqfx.getZtcs()) + ztNum);

        if (sfjb != null && sfjb.equals(1)) {
            samsSchedulingKqfx.setJbcs((samsSchedulingKqfx.getJbcs() == null ? 0 : samsSchedulingKqfx.getJbcs()) + 1);
        }
    }
}
