import {
	baseUrl
} from './config'

const request = (url = '', data = {}, type = '') => {
	return new Promise((resolve, reject) => {
		uni.request({
			method: type,
			url: baseUrl + url,
			data: data,
			dataType: 'json',
			header: {
				"Authorization": `Bearer ${uni.getStorageSync('token')||''}`,
			},
			success: (response) => {
				
				let {
					data
				} = response;
				resolve(data)
			},
			fail: (error) => {
				let [err, res] = error;
				console.error(err);
				reject(err)
			}

		})

	});
}
export default request