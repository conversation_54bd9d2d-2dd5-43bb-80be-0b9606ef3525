package com.ruoyi.web.controller.user;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.TxRlsbUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.mapper.SamsEmployeeMapper;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/user/profile")
public class AppProfileController extends BaseController {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;

    /**
     * 个人信息
     */
    @GetMapping
    public AjaxResult profile() {
        LoginUser loginUser = getLoginUser();
        SysUser user = loginUser.getUser();
        AjaxResult ajax = AjaxResult.success(user);
        ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
        return ajax;
    }

    /**
     * 修改小程序用户（昵称，识别图片）
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SamsEmployee employee) {
        LoginUser loginUser = getLoginUser();
        SamsEmployee currentUser = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(loginUser.getEmployee().getEmployeeId());
        if (StringUtils.isNotEmpty(employee.getNicheng())) {
            currentUser.setNicheng(employee.getNicheng());
        }
        boolean flag = false;
        if (StringUtils.isNotEmpty(employee.getCheckImg())) {
            currentUser.setCheckImg(employee.getCheckImg());
            flag = true;
        }
        if (StringUtils.isNotEmpty(employee.getAvate())) {
            currentUser.setAvate(employee.getAvate());
        }
        if (samsEmployeeMapper.updateSamsEmployee(currentUser) > 0) {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            if (flag) {
                //先删除再添加腾讯人脸
                TxRlsbUtils.deletePerson(employee.getEmployeeId() + "");
                //开始添加腾讯人脸信息
                SamsEmployee db = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(employee.getEmployeeId());
                TxRlsbUtils.addPerson(db.getName(), db.getCheckImg(), employee.getEmployeeId() + "");
            }
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword) {
        LoginUser loginUser = getLoginUser();
        String userName = loginUser.getUsername();
        String password = loginUser.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password)) {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password)) {
            return error("新密码不能与旧密码相同");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (userService.resetUserPwd(userName, newPassword) > 0) {
            // 更新缓存用户密码
            loginUser.getUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 小程序头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatar") String avatar) throws Exception {
        if (!avatar.isEmpty()) {
            LoginUser loginUser = getLoginUser();
            if (samsEmployeeMapper.updateAvate(loginUser.getUsername(), avatar) > 0) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", avatar);
                // 更新缓存用户头像
                loginUser.getUser().setAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        }
        return error("上传图片异常，请联系管理员");
    }
}
