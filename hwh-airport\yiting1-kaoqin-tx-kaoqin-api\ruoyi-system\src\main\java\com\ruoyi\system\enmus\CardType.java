package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 证件类型
 *
 * <AUTHOR>
 */
public enum CardType
{
    ID_CARD(0, "身份证"),
    PASSPORT(1, "护照"),
    MILITARY_OFFICER_CARD(2, "军官证");

    private final Integer code;
    private final String info;

    CardType(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static CardType getCertType(Integer code) {
        for (CardType cardType : CardType.values()) {
            if (Objects.equals(cardType.getCode(), code)) {
                return cardType;
            }
        }
        return null;
    }

    public static CardType getCertType(String info) {
        for (CardType cardType : CardType.values()) {
            if (Objects.equals(cardType.getInfo(), info)) {
                return cardType;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
