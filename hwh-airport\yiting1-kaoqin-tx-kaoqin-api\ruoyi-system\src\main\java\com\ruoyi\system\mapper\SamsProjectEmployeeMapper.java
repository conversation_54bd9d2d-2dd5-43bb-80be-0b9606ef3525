package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SamsProjectEmployee;
import org.apache.ibatis.annotations.Param;

/**
 * 项目员工Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsProjectEmployeeMapper
{
    /**
     * 查询项目员工
     *
     * @param projectEmployeeId 项目员工主键
     * @return 项目员工
     */
    public SamsProjectEmployee selectSamsProjectEmployeeByProjectEmployeeId(Long projectEmployeeId);

    SamsProjectEmployee selectByEmployeeIdAndProjectId(@Param("employeeId") Long employeeId,@Param("projectId") Long projectId);
    /**
     * 查询项目员工列表
     *
     * @param samsProjectEmployee 项目员工
     * @return 项目员工集合
     */
    public List<SamsProjectEmployee> selectSamsProjectEmployeeList(SamsProjectEmployee samsProjectEmployee);

    /**
     * 新增项目员工
     *
     * @param samsProjectEmployee 项目员工
     * @return 结果
     */
    public int insertSamsProjectEmployee(SamsProjectEmployee samsProjectEmployee);

    /**
     * 修改项目员工
     *
     * @param samsProjectEmployee 项目员工
     * @return 结果
     */
    public int updateSamsProjectEmployee(SamsProjectEmployee samsProjectEmployee);

    /**
     * 删除项目员工
     *
     * @param projectEmployeeId 项目员工主键
     * @return 结果
     */
    public int deleteSamsProjectEmployeeByProjectEmployeeId(Long projectEmployeeId);

    /**
     * 批量删除项目员工
     *
     * @param projectEmployeeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsProjectEmployeeByProjectEmployeeIds(Long[] projectEmployeeIds);

    List<SamsProjectEmployee> selectSamsProjectEmployeeExportList(SamsProjectEmployee samsProjectEmployee);
}
