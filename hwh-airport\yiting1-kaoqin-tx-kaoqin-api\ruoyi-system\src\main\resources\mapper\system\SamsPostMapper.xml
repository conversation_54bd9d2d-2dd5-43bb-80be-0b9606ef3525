<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsPostMapper">
    <resultMap type="SamsPost" id="SamsPostResult">
        <result property="postId" column="post_id"/>
        <result property="projectId" column="project_id"/>
        <result property="postName" column="post_name"/>
        <result property="postNature" column="post_nature"/>
        <result property="postNum" column="post_num"/>
        <result property="postDuty" column="post_duty"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="startWorkTime" column="start_work_time"/>
        <result property="endWorkTime" column="end_work_time"/>
        <result property="defaultPbrq" column="default_pbrq"/>
        <result property="lastPbrq" column="last_pbrq"/>
        <result property="pb" column="pb"/>
        <result property="deptId" column="dept_id"/>
        <result property="airportId" column="airport_id"/>
        <result property="areaId" column="area_id"/>
        <result property="groupId" column="group_id"/>
        <result property="teamId" column="team_id"/>

        <!--        部门名称-->
        <association property="sysDept" column="dept_id" javaType="com.ruoyi.common.core.domain.entity.SysDept">
            <id property="deptId" column="dept_id" />
            <result property="deptName" column="dept_name" />
        </association>
        <association property="samsAirport" column="airport_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirport">
            <id property="airportId" column="airport_id" />
            <result property="airportName" column="airport_name" />
        </association>
        <association property="samsAirportArea" column="area_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirportArea">
            <id property="areaId" column="area_id" />
            <result property="areaName" column="area_name" />
        </association>
        <association property="samsAirportGroup" column="group_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirportGroup">
            <id property="groupId" column="group_id" />
            <result property="groupName" column="group_name" />
        </association>
        <association property="samsAirportTeam" column="team_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirportTeam">
            <id property="teamId" column="team_id" />
            <result property="teamName" column="team_name" />
        </association>
    </resultMap>

    <sql id="selectSamsPostVo">
        select sp.post_id,
               sp.project_id,
               sp.pb,
               sp.post_name,
               sp.post_nature,
               sp.post_num,
               sp.post_duty,
               sp.start_date,
               sp.end_date,
               sp.del_flag,
               sp.create_by,
               sp.create_time,
               sp.update_by,
               sp.update_time,
               sp.remark,
               sp.start_work_time,
               sp.end_work_time,
               sp.default_pbrq,
               sp.dept_id,
               sp.airport_id,
               sp.area_id,
               sp.group_id,
               sp.team_id,
               sp.last_pbrq,
               sd.dept_name,
               sa.airport_name,
               saa.area_name,
               sat.team_name,
               sag.group_name
        from sams_post sp
                 LEFT JOIN sys_dept sd ON sp.dept_id = sd.dept_id
                 LEFT JOIN sams_airport sa ON sp.airport_id = sa.airport_id
                 LEFT JOIN sams_airport_area saa ON sp.area_id = saa.area_id
                 LEFT JOIN sams_airport_team sat ON sp.team_id = sat.team_id
                 LEFT JOIN sams_airport_group sag ON sp.group_id = sag.group_id
    </sql>

    <select id="selectSamsPostList" parameterType="SamsPost" resultMap="SamsPostResult">
        <include refid="selectSamsPostVo"/>
        <where>
            <if test="projectId != null ">
                and sp.project_id = #{projectId}
            </if>
            <if test="postName != null  and postName != ''">
                and sp.post_name like concat('%', #{postName}, '%')
            </if>
            <if test="postNature != null  and postNature != ''">
                and sp.post_nature = #{postNature}
            </if>
            <if test="postNum != null ">
                and sp.post_num = #{postNum}
            </if>
            <if test="postDuty != null  and postDuty != ''">
                and sp.post_duty = #{postDuty}
            </if>
            <if test="startDate != null ">
                and sp.start_date = #{startDate}
            </if>
            <if test="endDate != null ">
                and sp.end_date = #{endDate}
            </if>
            <if test="pb != null ">
                and sp.pb = #{pb}
            </if>
            <if test="deptId != null ">
                and sp.dept_id = #{deptId}
            </if>
            <if test="airportId != null ">
                and sp.airport_id = #{airportId}
            </if>
            <if test="areaId != null ">
                and sp.area_id = #{areaId}
            </if>
            <if test="groupId != null ">
                and sp.group_id = #{groupId}
            </if>
            <if test="teamId != null ">
                and sp.team_id = #{teamId}
            </if>
        </where>
    </select>

    <select id="selectAll4Cx" parameterType="SamsScheduling" resultMap="SamsPostResult">
        SELECT
        DISTINCT(p.post_id),p.*
        FROM
        sams_scheduling s
        JOIN sams_post p ON s.post_id = p.post_id
        where 1=1
        <if test="deptId != null">
            and s.dept_id=#{deptId}
        </if>
        <if test="airportId != null">
            and s.airport_id=#{airportId}
        </if>
        <if test="areaId != null">
            and s.area_id=#{areaId}
        </if>
        <if test="groupId != null">
            and s.group_id=#{groupId}
        </if>
        <if test="teamId != null">
            and s.team_id=#{teamId}
        </if>
        <if test="projectId != null">
            and s.project_id=#{projectId}
        </if>
        and s.duty_date like concat('%', #{cxStartDate}, '%')
    </select>


    <select id="selectSamsPostByPostId" parameterType="Long" resultMap="SamsPostResult">
        <include refid="selectSamsPostVo"/>
        where sp.post_id = #{postId}
    </select>

    <insert id="insertSamsPost" parameterType="SamsPost" useGeneratedKeys="true" keyProperty="postId">
        insert into sams_post
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">
                project_id,
            </if>
            <if test="postName != null">
                post_name,
            </if>
            <if test="postNature != null">
                post_nature,
            </if>
            <if test="postNum != null">
                post_num,
            </if>
            <if test="postDuty != null">
                post_duty,
            </if>
            <if test="startDate != null">
                start_date,
            </if>
            <if test="endDate != null">
                end_date,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="startWorkTime != null">
                start_work_time,
            </if>
            <if test="endWorkTime != null">
                end_work_time,
            </if>
            <if test="defaultPbrq != null">
                default_pbrq,
            </if>
            <if test="lastPbrq != null">
                last_pbrq,
            </if>
            <if test="pb != null">
                pb,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="airportId != null">
                airport_id,
            </if>
            <if test="areaId != null">
                area_id,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="teamId != null">
                team_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">
                #{projectId},
            </if>
            <if test="postName != null">
                #{postName},
            </if>
            <if test="postNature != null">
                #{postNature},
            </if>
            <if test="postNum != null">
                #{postNum},
            </if>
            <if test="postDuty != null">
                #{postDuty},
            </if>
            <if test="startDate != null">
                #{startDate},
            </if>
            <if test="endDate != null">
                #{endDate},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="startWorkTime != null">
                #{startWorkTime},
            </if>
            <if test="endWorkTime != null">
                #{endWorkTime},
            </if>
            <if test="defaultPbrq != null">
                #{defaultPbrq},
            </if>
            <if test="lastPbrq != null">
                #{lastPbrq},
            </if>
            <if test="pb != null">
                #{pb},
            </if>
            <if test="deptId != null">
                #{deptId},
            </if>
            <if test="airportId != null">
                #{airportId},
            </if>
            <if test="areaId != null">
                #{areaId},
            </if>
            <if test="groupId != null">
                #{groupId},
            </if>
            <if test="teamId != null">
                #{teamId},
            </if>
        </trim>
    </insert>

    <update id="updateSamsPost" parameterType="SamsPost">
        update sams_post
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">
                project_id = #{projectId},
            </if>
            <if test="postName != null">
                post_name = #{postName},
            </if>
            <if test="postNature != null">
                post_nature = #{postNature},
            </if>
            <if test="postNum != null">
                post_num = #{postNum},
            </if>
            <if test="postDuty != null">
                post_duty = #{postDuty},
            </if>
            <if test="startDate != null">
                start_date = #{startDate},
            </if>
            <if test="endDate != null">
                end_date = #{endDate},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="startWorkTime != null">
                start_work_time = #{startWorkTime},
            </if>
            <if test="endWorkTime != null">
                end_work_time = #{endWorkTime},
            </if>
            <if test="defaultPbrq != null">
                default_pbrq = #{defaultPbrq},
            </if>
            <if test="lastPbrq != null">
                last_pbrq = #{lastPbrq},
            </if>
            <if test="pb != null">
                pb = #{pb},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="airportId != null">
                airport_id = #{airportId},
            </if>
            <if test="areaId != null">
                area_id = #{areaId},
            </if>
            <if test="groupId != null">
                group_id = #{groupId},
            </if>
            <if test="teamId != null">
                team_id = #{teamId},
            </if>
        </trim>
        where post_id = #{postId}
    </update>

    <delete id="deleteSamsPostByPostId" parameterType="Long">
        delete
        from sams_post
        where post_id = #{postId}
    </delete>

    <delete id="deleteSamsPostByPostIds" parameterType="String">
        delete from sams_post where post_id in
        <foreach item="postId" collection="array" open="(" separator="," close=")">
            #{postId}
        </foreach>
    </delete>

    <resultMap id="SamsPostExportVoResult" type="SamsPostExportDataVo">
        <result property="postId" column="post_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="postName" column="post_name"/>
        <result property="airport" column="airport_name"/>
        <result property="area" column="area_name"/>
        <result property="group" column="group_name"/>
        <result property="team" column="team_name"/>
        <result property="projectName" column="project_name"/>
        <result property="postNature" column="post_nature"/>
        <!--        <result property="postNum" column="post_num"/>-->
        <result property="lastPbrq" column="last_pbrq"/>
        <result property="pb" column="pb"/>
        <result property="clockName" column="clock_name"/>
        <result property="startTime" column="start_time"/>
        <result property="startTimeBefore" column="start_time_before"/>
        <result property="startTimeAfter" column="start_time_after"/>
        <result property="endTime" column="end_time"/>
        <result property="endTimeBefore" column="end_time_before"/>
        <result property="endTimeAfter" column="end_time_after"/>
        <result property="clockAddress" column="clock_address"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="clockRange" column="clock_range"/>
        <result property="workRange" column="work_range"/>
        <result property="ddmm" column="ddmm"/>
    </resultMap>

    <select id="selectSamsPostExportVoList" parameterType="samsPost" resultMap="SamsPostExportVoResult">
        select
        sp.post_id, sp.post_name, sp.post_nature, sp.post_num, case when sp.pb = 1 then '待排班' when sp.pb = 2 then
        '已排班' end as pb, sp.last_pbrq, spj.project_name,
        sd.dept_name, sa.airport_name, saa.area_name, sag.group_name, sat.team_name, spc.clock_name, spc.start_time,
        spc.start_time_before, spc.start_time_after,
        spc.end_time, spc.end_time_before, spc.end_time_after, spc.clock_address, spc.latitude, spc.longitude,
        spc.clock_range, spc.work_range, spc.ddmm
        from sams_post sp
        left join sams_project spj on sp.project_id = spj.project_id
        left join sys_dept sd on sp.dept_id = sd.dept_id
        left join sams_airport sa on sp.airport_id = sa.airport_id
        left join sams_airport_area saa on sp.area_id = saa.area_id
        left join sams_airport_group sag on sp.group_id = sag.group_id
        left join sams_airport_team sat on sp.team_id = sat.team_id
        left join sams_post_clock spc on spc.post_id = sp.post_id
        <where>
            spc.del_flag = 0

            <if test="projectId != null ">
                and sp.project_id = #{projectId}
            </if>
            <if test="postName != null  and postName != ''">
                and sp.post_name like concat('%', #{postName}, '%')
            </if>
            <if test="postNature != null  and postNature != ''">
                and sp.post_nature = #{postNature}
            </if>
            <if test="postNum != null ">
                and sp.post_num = #{postNum}
            </if>
            <if test="postDuty != null  and postDuty != ''">
                and sp.post_duty = #{postDuty}
            </if>
            <if test="startDate != null ">
                and sp.start_date = #{startDate}
            </if>
            <if test="endDate != null ">
                and sp.end_date = #{endDate}
            </if>
            <if test="pb != null ">
                and sp.pb = #{pb}
            </if>
            <if test="deptId != null ">
                and sp.dept_id = #{deptId}
            </if>
            <if test="airportId != null ">
                and sp.airport_id = #{airportId}
            </if>
            <if test="areaId != null ">
                and sp.area_id = #{areaId}
            </if>
            <if test="groupId != null ">
                and sp.group_id = #{groupId}
            </if>
            <if test="teamId != null ">
                and sp.team_id = #{teamId}
            </if>
        </where>
    </select>
</mapper>
