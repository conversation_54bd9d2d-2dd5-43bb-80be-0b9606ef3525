package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SamsSupplierUser;

/**
 * 供应商用户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsSupplierUserMapper 
{
    /**
     * 查询供应商用户信息
     * 
     * @param supplierUserId 供应商用户信息主键
     * @return 供应商用户信息
     */
    public SamsSupplierUser selectSamsSupplierUserBySupplierUserId(Long supplierUserId);

    /**
     * 查询供应商用户信息列表
     * 
     * @param samsSupplierUser 供应商用户信息
     * @return 供应商用户信息集合
     */
    public List<SamsSupplierUser> selectSamsSupplierUserList(SamsSupplierUser samsSupplierUser);

    /**
     * 新增供应商用户信息
     * 
     * @param samsSupplierUser 供应商用户信息
     * @return 结果
     */
    public int insertSamsSupplierUser(SamsSupplierUser samsSupplierUser);

    /**
     * 修改供应商用户信息
     * 
     * @param samsSupplierUser 供应商用户信息
     * @return 结果
     */
    public int updateSamsSupplierUser(SamsSupplierUser samsSupplierUser);

    /**
     * 删除供应商用户信息
     * 
     * @param supplierUserId 供应商用户信息主键
     * @return 结果
     */
    public int deleteSamsSupplierUserBySupplierUserId(Long supplierUserId);

    /**
     * 批量删除供应商用户信息
     * 
     * @param supplierUserIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsSupplierUserBySupplierUserIds(Long[] supplierUserIds);
}
