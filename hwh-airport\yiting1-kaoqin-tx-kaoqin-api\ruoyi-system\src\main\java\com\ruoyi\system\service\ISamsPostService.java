package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SamsPost;
import com.ruoyi.system.domain.vo.SamsPostExportVo;

/**
 * 岗位信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface ISamsPostService
{
    /**
     * 查询岗位信息
     *
     * @param postId 岗位信息主键
     * @return 岗位信息
     */
    public SamsPost selectSamsPostByPostId(Long postId);

    /**
     * 查询岗位信息列表
     *
     * @param samsPost 岗位信息
     * @return 岗位信息集合
     */
    public List<SamsPost> selectSamsPostList(SamsPost samsPost);

    /**
     * 新增岗位信息
     *
     * @param samsPost 岗位信息
     * @return 结果
     */
    public int insertSamsPost(SamsPost samsPost);

    /**
     * 修改岗位信息
     *
     * @param samsPost 岗位信息
     * @return 结果
     */
    public int updateSamsPost(SamsPost samsPost);

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位信息主键集合
     * @return 结果
     */
    public int deleteSamsPostByPostIds(Long[] postIds);

    /**
     * 删除岗位信息信息
     *
     * @param postId 岗位信息主键
     * @return 结果
     */
    public int deleteSamsPostByPostId(Long postId);

    /**
     * 查询岗位导出列表
     * @param samsPost
     * @return
     */
    List<SamsPostExportVo> selectSamsPostExportList(SamsPost samsPost);
}
