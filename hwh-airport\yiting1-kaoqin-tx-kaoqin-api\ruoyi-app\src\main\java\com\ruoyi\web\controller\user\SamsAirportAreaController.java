package com.ruoyi.web.controller.user;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.entity.SamsAirportArea;
import com.ruoyi.system.service.ISamsAirportAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机场区域信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/airportArea")
public class SamsAirportAreaController extends BaseController
{
    @Autowired
    private ISamsAirportAreaService samsAirportAreaService;

    /**
     * 查询机场区域信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsAirportArea samsAirportArea)
    {
        startPage();
        List<SamsAirportArea> list = samsAirportAreaService.selectSamsAirportAreaList(samsAirportArea);
        return getDataTable(list);
    }

    /**
     * 获取机场区域信息详细信息
     */
    @GetMapping(value = "/{areaId}")
    public AjaxResult getInfo(@PathVariable("areaId") Long areaId)
    {
        return success(samsAirportAreaService.selectSamsAirportAreaByAreaId(areaId));
    }
    
}
