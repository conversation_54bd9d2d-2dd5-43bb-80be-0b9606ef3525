package com.ruoyi.system.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 年龄占比
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
public class YlzbVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "20-29人数")
    private Integer cat1;
    @Excel(name = "20-29人数比例")
    private String cat1St;

    @Excel(name = "30-39人数")
    private Integer cat2;
    @Excel(name = "30-39人数比例")
    private String cat2St;

    @Excel(name = "40-49人数")
    private Integer cat3;
    @Excel(name = "40-49人数比例")
    private String cat3St;

    @Excel(name = "50-59人数")
    private Integer cat4;
    @Excel(name = "50-59人数比例")
    private String cat4St;

    @Excel(name = "60-69人数")
    private Integer cat5;
    @Excel(name = "60-69人数比例")
    private String cat5St;

}
