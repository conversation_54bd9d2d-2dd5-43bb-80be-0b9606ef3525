package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 类别
 *
 * <AUTHOR>
 */
public enum TypeName
{
    EMPLOYEE(1, "本级员工"),
    LABOR_DISPATCH(2, "劳务派遣"),
    SUPPLIERS(3, "供应商");

    private final Integer code;
    private final String info;

    TypeName(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static TypeName getTypeName(Integer code) {
        for (TypeName typeName : TypeName.values()) {
            if (Objects.equals(typeName.getCode(), code)) {
                return typeName;
            }
        }
        return null;
    }

    public static TypeName getTypeName(String info) {
        for (TypeName typeName : TypeName.values()) {
            if (Objects.equals(typeName.getInfo(), info)) {
                return typeName;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
