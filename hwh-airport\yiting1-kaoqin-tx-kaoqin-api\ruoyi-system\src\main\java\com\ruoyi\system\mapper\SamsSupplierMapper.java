package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SamsSupplier;

/**
 * 供应商信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsSupplierMapper 
{
    /**
     * 查询供应商信息
     * 
     * @param supplierId 供应商信息主键
     * @return 供应商信息
     */
    public SamsSupplier selectSamsSupplierBySupplierId(Long supplierId);

    /**
     * 查询供应商信息列表
     * 
     * @param samsSupplier 供应商信息
     * @return 供应商信息集合
     */
    public List<SamsSupplier> selectSamsSupplierList(SamsSupplier samsSupplier);

    /**
     * 新增供应商信息
     * 
     * @param samsSupplier 供应商信息
     * @return 结果
     */
    public int insertSamsSupplier(SamsSupplier samsSupplier);

    /**
     * 修改供应商信息
     * 
     * @param samsSupplier 供应商信息
     * @return 结果
     */
    public int updateSamsSupplier(SamsSupplier samsSupplier);

    /**
     * 删除供应商信息
     * 
     * @param supplierId 供应商信息主键
     * @return 结果
     */
    public int deleteSamsSupplierBySupplierId(Long supplierId);

    /**
     * 批量删除供应商信息
     * 
     * @param supplierIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsSupplierBySupplierIds(Long[] supplierIds);
}
