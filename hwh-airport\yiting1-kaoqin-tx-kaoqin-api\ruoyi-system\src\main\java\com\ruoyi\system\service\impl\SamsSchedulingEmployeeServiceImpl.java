package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.vo.CxkqVo;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsSchedulingEmployeeService;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 员工排班信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Service
public class SamsSchedulingEmployeeServiceImpl implements ISamsSchedulingEmployeeService {
    @Autowired
    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;
    @Autowired
    private SamsSchedulingMapper samsSchedulingMapper;
    @Autowired
    private SamsPostClockMapper samsPostClockMapper;
    @Autowired
    private SamsProjectMapper samsProjectMapper;
    @Autowired
    private SamsPostMapper samsPostMapper;
    @Autowired
    private SamsProjectEmployeeMapper samsProjectEmployeeMapper;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportGroupMapper samsAirportGroupMapper;
    @Autowired
    private SamsAirportTeamMapper samsAirportTeamMapper;

    /**
     * 查询员工排班信息
     *
     * @param schedulingEmployeeId 员工排班信息主键
     * @return 员工排班信息
     */
    @Override
    public SamsSchedulingEmployee selectSamsSchedulingEmployeeBySchedulingEmployeeId(Long schedulingEmployeeId) {
        SamsSchedulingEmployee samsSchedulingEmployee = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeBySchedulingEmployeeId(schedulingEmployeeId);
        if(samsSchedulingEmployee!=null){
            SamsScheduling samsScheduling = samsSchedulingMapper.selectSamsSchedulingBySchedulingId(samsSchedulingEmployee.getSchedulingId());
            if(samsScheduling!=null){
                SamsProject samsProject = samsProjectMapper.selectSamsProjectByProjectId(samsScheduling.getProjectId());
                samsSchedulingEmployee.setSamsProject(samsProject);
                SamsPost samsPost = samsPostMapper.selectSamsPostByPostId(samsScheduling.getPostId());
                samsSchedulingEmployee.setSamsPost(samsPost);
                if (samsPost != null) {
                    SamsPostClock samsPostClock = new SamsPostClock();
                    samsPostClock.setPostId(samsPost.getPostId());
                    samsPost.setSamsPostClockList(samsPostClockMapper.selectSamsPostClockList(samsPostClock));
                }
                SamsEmployee samsEmployee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(samsSchedulingEmployee.getEmployeeId());
                samsSchedulingEmployee.setSamsEmployee(samsEmployee);
            }
        }
        return samsSchedulingEmployee;
    }

    /**
     * 查询员工排班信息列表
     *
     * @param samsSchedulingEmployee 员工排班信息
     * @return 员工排班信息
     */
    @Override
    public List<SamsSchedulingEmployee> selectSamsSchedulingEmployeeList(SamsSchedulingEmployee samsSchedulingEmployee) {
        List<SamsSchedulingEmployee> list = samsSchedulingEmployeeMapper.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
        if(list!=null && list.size()>0){
            for (SamsSchedulingEmployee schedulingEmployee : list) {
                if(schedulingEmployee.getSamsScheduling() != null){
                    SamsScheduling samsScheduling = schedulingEmployee.getSamsScheduling();
                    // 部门信息
                    if (null != samsScheduling.getDeptId()){
                        schedulingEmployee.setDeptId(samsScheduling.getDeptId());
                        schedulingEmployee.setSysDept(sysDeptMapper.selectDeptById(samsScheduling.getDeptId()));
                    }
                    // 机场信息
                    if (null != samsScheduling.getAirportId()) {
                        schedulingEmployee.setAirportId(samsScheduling.getAirportId());
                        schedulingEmployee.setSamsAirport(samsAirportMapper.selectSamsAirportByAirportId(samsScheduling.getAirportId()));
                    }

                    // 区域信息
                    if (null != samsScheduling.getAreaId()) {
                        schedulingEmployee.setAreaId(samsScheduling.getAreaId());
                        schedulingEmployee.setSamsAirportArea(samsAirportAreaMapper.selectSamsAirportAreaByAreaId(samsScheduling.getAreaId()));
                    }

                    // 大队
                    if (null != samsScheduling.getTeamId()) {
                        schedulingEmployee.setTeamId(samsScheduling.getTeamId());
                        schedulingEmployee.setSamsAirportTeam(samsAirportTeamMapper.selectSamsAirportTeamByTeamId(samsScheduling.getTeamId()));
                    }

                    // 组别
                    if (null != samsScheduling.getGroupId()) {
                        schedulingEmployee.setGroupId(samsScheduling.getGroupId());
                        schedulingEmployee.setSamsAirportGroup(samsAirportGroupMapper.selectSamsAirportGroupByGroupId(samsScheduling.getGroupId()));
                    }

                    // 项目
                    if (null != samsScheduling.getProjectId()) {
                        // 项目信息
                        schedulingEmployee.setProjectId(samsScheduling.getProjectId());
                        schedulingEmployee.setSamsProject(samsProjectMapper.selectSamsProjectByProjectId(samsScheduling.getProjectId()));
                    }

                    // 岗位
                    if (null != samsScheduling.getPostId()) {
                        schedulingEmployee.setPostId(samsScheduling.getPostId());
                        SamsPost samsPost = samsPostMapper.selectSamsPostByPostId(samsScheduling.getPostId());
                        schedulingEmployee.setSamsPost(samsPost);

                        if (samsPost != null) {
                            SamsPostClock samsPostClock = new SamsPostClock();
                            samsPostClock.setPostId(samsPost.getPostId());
                            // 打卡点信息
                            samsPost.setSamsPostClockList(samsPostClockMapper.selectSamsPostClockList(samsPostClock));
                        }
                    }

                    // 员工信息
                    SamsEmployee samsEmployee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(schedulingEmployee.getEmployeeId());
                    schedulingEmployee.setSamsEmployee(samsEmployee);

                }
            }
        }

        return list;
    }

    @Override
    public List<SamsSchedulingEmployee> mykqdk(CxkqVo cxkqVo) {
        List<SamsSchedulingEmployee> list = samsSchedulingEmployeeMapper.mykqdk(cxkqVo);
        List<SamsSchedulingEmployee> currentDateList = new ArrayList<>();
        List<SamsSchedulingEmployee> otherDateList = new ArrayList<>();
        if(list!=null && list.size()>0){
            for (SamsSchedulingEmployee samsSchedulingEmployee : list) {
                SamsScheduling samsScheduling = samsSchedulingMapper.selectSamsSchedulingBySchedulingId(samsSchedulingEmployee.getSchedulingId());
                if(samsScheduling!=null){
                    samsSchedulingEmployee.setSamsProject(samsProjectMapper.selectSamsProjectByProjectId(samsScheduling.getProjectId()));
                }
                if (null != samsSchedulingEmployee.getDutyDate()
                        && DateUtils.isSameDay(samsSchedulingEmployee.getDutyDate(), DateUtils.getNowDate())) {
                    currentDateList.add(samsSchedulingEmployee);
                } else {
                    otherDateList.add(samsSchedulingEmployee);
                }
            }
        }

        return ListUtils.union(currentDateList, otherDateList);
    }

    /**
     * 新增员工排班信息
     *
     * @param samsSchedulingEmployee 员工排班信息
     * @return 结果
     */
    @Override
    public int insertSamsSchedulingEmployee(SamsSchedulingEmployee samsSchedulingEmployee) {
        samsSchedulingEmployee.setCreateTime(DateUtils.getNowDate());
        return samsSchedulingEmployeeMapper.insertSamsSchedulingEmployee(samsSchedulingEmployee);
    }

    /**
     * 修改员工排班信息
     *
     * @param samsSchedulingEmployee 员工排班信息
     * @return 结果
     */
    @Override
    public int updateSamsSchedulingEmployee(SamsSchedulingEmployee samsSchedulingEmployee) {
        samsSchedulingEmployee.setUpdateTime(DateUtils.getNowDate());
        return samsSchedulingEmployeeMapper.updateSamsSchedulingEmployee(samsSchedulingEmployee);
    }

    /**
     * 批量删除员工排班信息
     *
     * @param schedulingEmployeeIds 需要删除的员工排班信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSchedulingEmployeeBySchedulingEmployeeIds(Long[] schedulingEmployeeIds) {
        return samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingEmployeeIds(schedulingEmployeeIds);
    }

    /**
     * 删除员工排班信息信息
     *
     * @param schedulingEmployeeId 员工排班信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsSchedulingEmployeeBySchedulingEmployeeId(Long schedulingEmployeeId) {
        return samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingEmployeeId(schedulingEmployeeId);
    }
}
