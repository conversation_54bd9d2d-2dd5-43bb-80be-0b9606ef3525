package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsAirportGroup;
import org.apache.ibatis.annotations.Param;

/**
 * 机场大队信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface SamsAirportGroupMapper
{
    /**
     * 查询机场大队信息
     *
     * @param groupId 机场大队信息主键
     * @return 机场大队信息
     */
    public SamsAirportGroup selectSamsAirportGroupByGroupId(Long groupId);

    /**
     * 查询机场大队信息列表
     *
     * @param samsAirportGroup 机场大队信息
     * @return 机场大队信息集合
     */
    public List<SamsAirportGroup> selectSamsAirportGroupList(SamsAirportGroup samsAirportGroup);
    int countAirportGroup();
    /**
     * 新增机场大队信息
     *
     * @param samsAirportGroup 机场大队信息
     * @return 结果
     */
    public int insertSamsAirportGroup(SamsAirportGroup samsAirportGroup);

    /**
     * 修改机场大队信息
     *
     * @param samsAirportGroup 机场大队信息
     * @return 结果
     */
    public int updateSamsAirportGroup(SamsAirportGroup samsAirportGroup);

    /**
     * 删除机场大队信息
     *
     * @param groupId 机场大队信息主键
     * @return 结果
     */
    public int deleteSamsAirportGroupByGroupId(Long groupId);

    /**
     * 批量删除机场大队信息
     *
     * @param groupIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsAirportGroupByGroupIds(Long[] groupIds);

    SamsAirportGroup selectSysGroupByDeptIdAndAirportIdAndAreaIdAndGroupName(@Param("deptId") Long deptId,
                                                                             @Param("airportId") Long airportId,
                                                                             @Param("areaId") Long areaId,
                                                                             @Param("group") String group);
}
