package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.*;
import lombok.Data;

import java.util.Date;

/**
 * 员工排班信息对象 sams_scheduling_employee
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
public class SamsSchedulingEmployee extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 员工排班ID
     */
    private Long schedulingEmployeeId;

    /**
     * 排班ID
     */
    @Excel(name = "排班ID")
    private Long schedulingId;

    /**
     * 项目员工ID
     */
    @Excel(name = "项目员工ID")
    private Long projectEmployeeId;

    /**
     * 员工信息ID
     */
    @Excel(name = "员工信息ID")
    private Long employeeId;

    /**
     * 排班日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "排班日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dutyDate;

    @Excel(name = "打卡点信息")
    private String postClock;


    /**
     * 打卡状态 null没打卡，1打卡成功，2打卡异常
     */
    @Excel(name = "打卡状态 null没打卡，1打卡成功，2打卡异常 3迟到 5 早退 6 上班未打卡 7下班未打卡 ")
    private Integer dkStatus;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String latitude;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String longitude;

    /**
     * 打卡地点
     */
    @Excel(name = "打卡地点")
    private String dkAddress;

    /**
     * 打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "打卡时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dkDate;

    @Excel(name = "打卡图片")
    private String dkImage;

    @Excel(name = "加班0否，1是")
    private Integer sfjb;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date authTime;
    @Excel(name = "审核状态 3待审核，1审核通过，2审核拒绝")
    private Integer authStatus;
    @Excel(name = "审核备注")
    private String authRemark;
    @Excel(name = "考勤调整")
    private String kqtz;
    @Excel(name = "打卡类型 1上班，2下班")
    private Integer dkType;
    @Excel(name = "打卡时间")
    private String startTime;
    @Excel(name = "结束时间")
    private String endTime;

    private int queryType;
    /**
     * 打卡标识
     */
    private String dkTag;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SamsPostClock samsPostClock;
    private SamsProject samsProject;
    private SamsPost samsPost;
    private SamsEmployee samsEmployee;
    private SysDept sysDept;
    private String mobilePhone;
    /**
     * 员工姓名
     */
    private String employeeName;

    private String dkStatusDesc;


    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 机场ID
     */
    private Long airportId;

    /**
     * 区域ID
     */
    private Long areaId;

    /**
     * 大队ID
     */
    private Long groupId;

    /**
     * 小组ID
     */
    private Long teamId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 岗位ID
     */
    private Long postId;

    private SamsAirport samsAirport;
    private SamsAirportArea samsAirportArea;
    private SamsAirportGroup samsAirportGroup;
    private SamsAirportTeam samsAirportTeam;

    private SamsScheduling samsScheduling;

    /**
     *标识员工是否需要次日打卡(0 否 1 是)
     */
    private int nextDay;

    /**
     *标识员工是否可以任意时间打卡(0 否 1 是)
     */

    private int isAnytimeCheckIn;

    /**
     * 开始时间前得范围
     */
    private String startTimeBefore;

    /**
     * 开始时间后得范围
     */
    private String startTimeAfter;

    /**
     * 结束时间前得范围
     */
    private String endTimeBefore;

    /**
     * 结束时间后得范围
     */
    private String endTimeAfter;

    /**
     * 排班时间（解决次日问题）
     */
    private String scheduleDate;

}
