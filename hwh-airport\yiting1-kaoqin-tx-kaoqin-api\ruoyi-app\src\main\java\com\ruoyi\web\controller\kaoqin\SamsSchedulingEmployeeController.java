package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.TxRlsbUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SamsSchedulingEmployee;
import com.ruoyi.system.domain.vo.CxkqVo;
import com.ruoyi.system.service.ISamsSchedulingEmployeeService;
import com.tencentcloudapi.iai.v20200303.models.VerifyPersonResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 员工排班信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@RestController
@RequestMapping("/app/schedulingEmployee")
public class SamsSchedulingEmployeeController extends BaseController {
    @Autowired
    private ISamsSchedulingEmployeeService samsSchedulingEmployeeService;

    /**
     * 查询员工排班信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsSchedulingEmployee samsSchedulingEmployee) {
        startPage();
        List<SamsSchedulingEmployee> list = samsSchedulingEmployeeService.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
        return getDataTable(list);
    }

    /**
     * 根据月份查询我的首页考勤打卡list
     */
    @GetMapping("/mykqdk")
    public TableDataInfo mykqdk(CxkqVo cxkqVo) {
//        cxkqVo.setEmployeeId(getUserId());
        startPage();
        List<SamsSchedulingEmployee> list = samsSchedulingEmployeeService.mykqdk(cxkqVo);
        return getDataTable(list);
    }


    /**
     * 导出员工排班信息列表
     */
    @Log(title = "员工排班信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsSchedulingEmployee samsSchedulingEmployee) {
        List<SamsSchedulingEmployee> list = samsSchedulingEmployeeService.selectSamsSchedulingEmployeeList(samsSchedulingEmployee);
        ExcelUtil<SamsSchedulingEmployee> util = new ExcelUtil<SamsSchedulingEmployee>(SamsSchedulingEmployee.class);
        util.exportExcel(response, list, "员工排班信息数据");
    }

    /**
     * 获取员工排班信息详细信息(个人打卡详情)
     */
    @GetMapping(value = "/{schedulingEmployeeId}")
    public AjaxResult getInfo(@PathVariable("schedulingEmployeeId") Long schedulingEmployeeId) {
        return success(samsSchedulingEmployeeService.selectSamsSchedulingEmployeeBySchedulingEmployeeId(schedulingEmployeeId));
    }

    /**
     * 新增员工排班信息
     */
    @Log(title = "员工排班信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsSchedulingEmployee samsSchedulingEmployee) {
        return toAjax(samsSchedulingEmployeeService.insertSamsSchedulingEmployee(samsSchedulingEmployee));
    }

    /**
     * 员工打卡时人脸识别
     */
    @GetMapping("/verifyPerson")
    public AjaxResult verifyPerson(@RequestParam String url) {
        VerifyPersonResponse verifyPersonResponse = TxRlsbUtils.verifyPerson(getUserId() + "", url);
        return success(verifyPersonResponse);
    }

    /**
     * 员工打卡
     */
    @PutMapping("/dodaka")
    public AjaxResult dodaka(@RequestBody SamsSchedulingEmployee samsSchedulingEmployee) {
        return toAjax(samsSchedulingEmployeeService.updateSamsSchedulingEmployee(samsSchedulingEmployee));
    }

    /**
     * 删除员工排班信息
     */
    @Log(title = "员工排班信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{schedulingEmployeeIds}")
    public AjaxResult remove(@PathVariable Long[] schedulingEmployeeIds) {
        return toAjax(samsSchedulingEmployeeService.deleteSamsSchedulingEmployeeBySchedulingEmployeeIds(schedulingEmployeeIds));
    }
}
