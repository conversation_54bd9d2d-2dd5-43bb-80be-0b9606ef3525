package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 政治面貌
 *
 * <AUTHOR>
 */
public enum PolotocalStatus
{
    MASSES(0, "群众"),
    PARTY_MEMBER(1, "中共党员"),
    LEAGUE_MEMBER(2, "共青团员");

    private final Integer code;
    private final String info;

    PolotocalStatus(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static PolotocalStatus getPolotocalStatus(Integer code) {
        for (PolotocalStatus polotocalStatus : PolotocalStatus.values()) {
            if (Objects.equals(polotocalStatus.getCode(), code)) {
                return polotocalStatus;
            }
        }
        return null;
    }

    public static PolotocalStatus getPolotocalStatus(String info) {
        for (PolotocalStatus polotocalStatus : PolotocalStatus.values()) {
            if (Objects.equals(polotocalStatus.getInfo(), info)) {
                return polotocalStatus;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
