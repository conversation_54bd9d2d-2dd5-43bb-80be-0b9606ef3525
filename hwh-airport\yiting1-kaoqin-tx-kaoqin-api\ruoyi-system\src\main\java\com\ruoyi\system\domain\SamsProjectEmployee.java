package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import lombok.Data;

import java.util.List;

/**
 * 项目员工对象 sams_project_employee
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsProjectEmployee extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 项目员工ID
     */
    private Long projectEmployeeId;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 员工信息ID
     */
    @Excel(name = "员工信息ID")
    private Long employeeId;

    /**
     * 所属项目
     */
    @Excel(name = "所属项目")
    private String projectName;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 昵称
     */
    @Excel(name = "昵称")
    private String nicheng;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobilePhone;

    /**
     * 状态（0启用 1禁用）
     */
    @Excel(name = "状态", readConverterExp = "0=启用,1=禁用")
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
    private SamsEmployee samsEmployee;
    private SamsProject samsProject;
    private String employeeIds;

    private Long deptId;
    private Long airportId;
    private Long areaId;
    private Long groupId;
}
