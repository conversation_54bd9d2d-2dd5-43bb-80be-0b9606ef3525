package com.ruoyi.web.controller.kaoqin;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SamsPost;
import com.ruoyi.system.service.ISamsPostService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 岗位信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/app/samsPost")
public class SamsPostController extends BaseController {
    @Autowired
    private ISamsPostService samsPostService;

    /**
     * 查询岗位信息列表(根据岗位传projectId)
     */
    @GetMapping("/list")
    public AjaxResult list(SamsPost samsPost) {
        List<SamsPost> list = samsPostService.selectSamsPostList(samsPost);
        return AjaxResult.success(list);
    }

    /**
     * 导出岗位信息列表
     */
    @Log(title = "岗位信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsPost samsPost) {
        List<SamsPost> list = samsPostService.selectSamsPostList(samsPost);
        ExcelUtil<SamsPost> util = new ExcelUtil<SamsPost>(SamsPost.class);
        util.exportExcel(response, list, "岗位信息数据");
    }

    /**
     * 获取岗位信息详细信息
     */
    @GetMapping(value = "/{postId}")
    public AjaxResult getInfo(@PathVariable("postId") Long postId) {
        return success(samsPostService.selectSamsPostByPostId(postId));
    }

    /**
     * 新增岗位信息
     */
    @Log(title = "岗位信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsPost samsPost) {
        return toAjax(samsPostService.insertSamsPost(samsPost));
    }

    /**
     * 修改岗位信息
     */
    @Log(title = "岗位信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsPost samsPost) {
        return toAjax(samsPostService.updateSamsPost(samsPost));
    }

    /**
     * 删除岗位信息
     */
    @Log(title = "岗位信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{postIds}")
    public AjaxResult remove(@PathVariable Long[] postIds) {
        return toAjax(samsPostService.deleteSamsPostByPostIds(postIds));
    }
}
