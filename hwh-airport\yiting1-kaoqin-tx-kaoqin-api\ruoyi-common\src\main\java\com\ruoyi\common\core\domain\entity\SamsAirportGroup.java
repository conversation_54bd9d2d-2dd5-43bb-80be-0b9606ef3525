package com.ruoyi.common.core.domain.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 机场大队信息对象 sams_airport_group
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Data
public class SamsAirportGroup extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 大队ID
     */
    private Long groupId;

    /**
     * 部门ID
     */
    @Excel(name = "部门ID")
    private Long deptId;

    /**
     * 机场ID
     */
    @Excel(name = "机场ID")
    private Long airportId;

    /**
     * 区域ID
     */
    @Excel(name = "区域ID")
    private Long areaId;

    /**
     * 大队名称
     */
    @Excel(name = "大队名称")
    private String groupName;

    /**
     * 大队编码
     */
    @Excel(name = "大队编码")
    private String groupCode;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SysDept sysDept;
    private SamsAirport samsAirport;
    private SamsAirportArea samsAirportArea;

}
