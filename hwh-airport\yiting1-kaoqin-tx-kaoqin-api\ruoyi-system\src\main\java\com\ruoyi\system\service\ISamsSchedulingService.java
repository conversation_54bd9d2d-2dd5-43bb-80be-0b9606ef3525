package com.ruoyi.system.service;

import com.ruoyi.system.domain.SamsScheduling;
import com.ruoyi.system.domain.vo.PbCxVo;

import java.text.ParseException;
import java.util.List;

/**
 * 排班信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
public interface ISamsSchedulingService
{
    /**
     * 查询排班信息
     *
     * @param schedulingId 排班信息主键
     * @return 排班信息
     */
    public SamsScheduling selectSamsSchedulingBySchedulingId(Long schedulingId);

    /**
     * 查询排班信息列表
     *
     * @param samsScheduling 排班信息
     * @return 排班信息集合
     */
    public List<SamsScheduling> selectSamsSchedulingList(SamsScheduling samsScheduling);

    List<PbCxVo> selectSamsSchedulingList4Cx(SamsScheduling samsScheduling);

    List<PbCxVo> selectSamsSchedulingList4Cx2(SamsScheduling samsScheduling);


    /**
     * 根据部门机场，区域大队，组别，项目，岗位，排班日期（单日）查询排班
     * @param samsScheduling
     * @return
     */
    public List<SamsScheduling> list4cxTB(SamsScheduling samsScheduling);

    /**
     * 新增排班信息
     *
     * @param samsScheduling 排班信息
     * @return 结果
     */
    public int insertSamsScheduling(SamsScheduling samsScheduling);

    /**
     * 修改排班信息
     *
     * @param samsScheduling 排班信息
     * @return 结果
     */
    public int updateSamsScheduling(SamsScheduling samsScheduling) throws ParseException;

    /**
     * 批量删除排班信息
     *
     * @param schedulingIds 需要删除的排班信息主键集合
     * @return 结果
     */
    public int deleteSamsSchedulingBySchedulingIds(Long[] schedulingIds);

    /**
     * 删除排班信息信息
     *
     * @param schedulingId 排班信息主键
     * @return 结果
     */
    public int deleteSamsSchedulingBySchedulingId(Long schedulingId);

    int deleteSamsSchedulingBySchedulingIdAndDutyDate(Long schedulingId, String dutyDate);
}
