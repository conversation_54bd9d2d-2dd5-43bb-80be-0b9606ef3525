package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.vo.SamsPostExportDataVo;
import com.ruoyi.system.domain.vo.SamsPostExportVo;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsPostService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 岗位信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Slf4j
@Service
public class SamsPostServiceImpl implements ISamsPostService {
    @Autowired
    private SamsPostMapper samsPostMapper;
    @Autowired
    private SamsProjectMapper samsProjectMapper;
    @Autowired
    private SamsPostClockMapper samsPostClockMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportGroupMapper samsAirportGroupMapper;
    @Autowired
    private SamsAirportTeamMapper samsAirportTeamMapper;
    @Autowired
    private SamsSchedulingMapper samsSchedulingMapper;
    @Autowired
    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;
    @Autowired
    private SamsProjectEmployeeMapper samsProjectEmployeeMapper;

    /**
     * 查询岗位信息
     *
     * @param postId 岗位信息主键
     * @return 岗位信息
     */
    @Override
    public SamsPost selectSamsPostByPostId(Long postId) {
        SamsPost samsPost = samsPostMapper.selectSamsPostByPostId(postId);
        if (samsPost != null) {
            SamsPostClock samsPostClock = new SamsPostClock();
            samsPostClock.setPostId(samsPost.getPostId());
            samsPost.setSamsPostClockList(samsPostClockMapper.selectSamsPostClockList(samsPostClock));
        }
        return samsPost;
    }

    /**
     * 查询岗位信息列表
     *
     * @param samsPost 岗位信息
     * @return 岗位信息
     */
    @Override
    public List<SamsPost> selectSamsPostList(SamsPost samsPost) {
        List<SamsPost> list = samsPostMapper.selectSamsPostList(samsPost);
        if (list != null && list.size() > 0) {
            for (SamsPost post : list) {
                post.setSamsProject(samsProjectMapper.selectSamsProjectByProjectId(post.getProjectId()));
                SamsPostClock samsPostClock = new SamsPostClock();
                samsPostClock.setPostId(post.getPostId());
                post.setSamsPostClockList(samsPostClockMapper.selectSamsPostClockList(samsPostClock));
//                post.setSysDept(sysDeptMapper.selectDeptById(post.getDeptId()));
//                post.setSamsAirport(samsAirportMapper.selectSamsAirportByAirportId(post.getAirportId()));
//                post.setSamsAirportArea(samsAirportAreaMapper.selectSamsAirportAreaByAreaId(post.getAreaId()));
//                post.setSamsAirportGroup(samsAirportGroupMapper.selectSamsAirportGroupByGroupId(post.getGroupId()));
//                post.setSamsAirportTeam(samsAirportTeamMapper.selectSamsAirportTeamByTeamId(post.getTeamId()));
            }
        }
        return list;
    }


    /**
     * 新增岗位信息
     *
     * @param samsPost 岗位信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertSamsPost(SamsPost samsPost) {
        samsPost.setCreateTime(DateUtils.getNowDate());
        int rows = samsPostMapper.insertSamsPost(samsPost);
        List<SamsPostClock> samsPostClockList = samsPost.getSamsPostClockList();
        for (SamsPostClock postClock : samsPostClockList) {
            postClock.setPostId(samsPost.getPostId());
            postClock.setCreateTime(DateUtils.getNowDate());
            samsPostClockMapper.insertSamsPostClock(postClock);
        }
        SamsProject project = samsProjectMapper.selectSamsProjectByProjectId(samsPost.getProjectId());
        project.setGwgs(project.getGwgs() + 1);
        samsProjectMapper.updateSamsProject(project);
        return rows;
    }

    /**
     * 修改岗位信息
     *
     * @param samsPost 岗位信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateSamsPost(SamsPost samsPost) {
        SamsPost db = samsPostMapper.selectSamsPostByPostId(samsPost.getPostId());
        if (!db.getProjectId().equals(samsPost.getProjectId())) {
            SamsProject dbProject = samsProjectMapper.selectSamsProjectByProjectId(samsPost.getProjectId());
            if (dbProject != null) {
                dbProject.setGwgs(dbProject.getGwgs() - 1);
                samsProjectMapper.updateSamsProject(dbProject);
            }

            SamsProject samsProject = samsProjectMapper.selectSamsProjectByProjectId(samsPost.getProjectId());
            samsProject.setGwgs(samsProject.getGwgs() + 1);
            samsProjectMapper.updateSamsProject(samsProject);
        }
        samsPost.setUpdateTime(DateUtils.getNowDate());
        int rows = samsPostMapper.updateSamsPost(samsPost);
//        samsPostClockMapper.deleteByPostId(samsPost.getPostId());
        // 原来打卡点
        SamsPostClock oldSamsPostClock = new SamsPostClock();
        oldSamsPostClock.setPostId(samsPost.getPostId());
        List<SamsPostClock> oldSamsPostClockList = samsPostClockMapper.selectSamsPostClockList(oldSamsPostClock);

        List<SamsPostClock> samsPostClockList = samsPost.getSamsPostClockList();

        if (oldSamsPostClockList.size() == samsPostClockList.size()) {//修改了两条
            for (SamsPostClock postClock : samsPostClockList) {
                postClock.setPostId(samsPost.getPostId());
                postClock.setUpdateTime(DateUtils.getNowDate());
                samsPostClockMapper.updateSamsPostClock(postClock);
            }
        } else if (oldSamsPostClockList.size() > samsPostClockList.size()) {// 删除了其中一条
            for (SamsPostClock postClock : samsPostClockList) {
                // 比较两个打卡点id
                for (SamsPostClock oldPostClock : oldSamsPostClockList) {
                    if (postClock.getPostClockId().equals(oldPostClock.getPostClockId())) {
                        postClock.setUpdateTime(DateUtils.getNowDate());
                        samsPostClockMapper.updateSamsPostClock(postClock);
                    } else {
                        oldPostClock.setDelFlag("2");
                        oldPostClock.setUpdateTime(DateUtils.getNowDate());
                        samsPostClockMapper.updateSamsPostClock(oldPostClock);
                    }
                }
            }
        } else {
            for (SamsPostClock postClock : samsPostClockList) {
                postClock.setPostId(samsPost.getPostId());
                postClock.setCreateTime(DateUtils.getNowDate());
                if (null == postClock.getPostClockId()) {
                    samsPostClockMapper.insertSamsPostClock(postClock);
                } else {
                    samsPostClockMapper.updateSamsPostClock(postClock);
                }
            }
        }

        Date updateTime = db.getUpdateTime();
        if (db.getUpdateTime() == null) {
            return rows;
        }
        String lastUpdateDate = DateUtils.dateTime(updateTime);
        // ----------------------2025-01-17修改------------------------------------------
        // 获取岗位最后更新日期
//            if (null != updateTime) {
//                String lastUpdateDate = DateUtils.dateTime(updateTime);
//                List<SamsScheduling> secondSamsSchedulingList = samsSchedulingMapper.selectSamsSchedulingByPostIdAndCreateDate(samsPost.getPostId(), lastUpdateDate);
//                if (!CollectionUtils.isEmpty(secondSamsSchedulingList)) {
//                    // 获取当前日期时间
//                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//                    LocalDate currentDate = LocalDate.now();
//                    // 获取当前日期往后的第二天
//                    String futureFirstDate = currentDate.plusDays(1).format(formatter);
//                    for (SamsScheduling samsScheduling : secondSamsSchedulingList) {
//                        deleteAndUpdateSamsScheduling(samsScheduling, futureFirstDate, currentDate.format(formatter));
//                    }
//                }
//            }
        // -----------------------------------------------------------------------------
        // ----------------------2025-01-22修改------------------------------------------
        // 获取当前日期时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate currentDate = LocalDate.now();
        // 获取当前日期往后的第二天
        String futureFirstDate = currentDate.plusDays(1).format(formatter);
        // 1.根据岗位id、当前日期查询后面的排班
        List<SamsScheduling> schedulingList = samsSchedulingMapper.selectSamsSchedulingByPostIdAndCreateDate(samsPost.getPostId(), lastUpdateDate);
        if (!CollectionUtils.isEmpty(schedulingList)) {
            for (SamsScheduling samsScheduling : schedulingList) {
                if (StringUtils.isNotBlank(samsScheduling.getPostClock())) {
                    updateSamsSchedulingAndEmployee(samsScheduling, futureFirstDate, samsPost);
                }
            }
        }

        return rows;
    }

    /**
     * 删除、修改排班信息
     *
     * @param futureFirstDate 当前日期往后的一天
     * @param currentDate     当前日期
     * @param samsScheduling  排班
     */
    private void deleteAndUpdateSamsScheduling(SamsScheduling samsScheduling, String futureFirstDate, String currentDate) {
        List<String> dutyDateList = new ArrayList<>(Arrays.asList(samsScheduling.getDutyDate().split(",")));
        List<String> newDutyDateList = new ArrayList<>();
        Date date = DateUtils.parseDate(futureFirstDate);
        for (int i = 0; i < dutyDateList.size(); i++) {
            String dbDutyDateStr = dutyDateList.get(i);
            Date dbDutyDate = DateUtils.parseDate(dbDutyDateStr);
            // 如果dutyDate大于futureFirstDate, 删除排班员工需要排除次日
            if (dbDutyDate.getTime() == date.getTime()) {
                // 根据schedulingId，dutyDate删除排班员工
                samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeOffWorkBySchedulingIdAndDutyDate(samsScheduling.getSchedulingId(), currentDate);
            } else if (dbDutyDate.getTime() > date.getTime()) {
                // 根据schedulingId，dutyDate删除排班员工
                samsSchedulingEmployeeMapper.deleteSamsSchedulingEmployeeBySchedulingIdAndAfterDutyDate(samsScheduling.getSchedulingId(), dbDutyDateStr);
            } else {
                newDutyDateList.add(dutyDateList.get(i));
            }
        }
        if (!CollectionUtils.isEmpty(newDutyDateList)) {
            samsScheduling.setDutyDate(String.join(",", newDutyDateList));
            // 修改排班
            samsSchedulingMapper.updateSamsScheduling(samsScheduling);
        } else {
            samsSchedulingMapper.deleteSamsSchedulingBySchedulingId(samsScheduling.getSchedulingId());
        }
    }

    /**
     * 修改排班信息、员工信息
     *
     * @param futureFirstDate 当前日期往后的一天
     * @param samsScheduling  排班
     * @param samsPost        岗位
     */
    private void updateSamsSchedulingAndEmployee(SamsScheduling samsScheduling, String futureFirstDate, SamsPost samsPost) {
        // 排除当天的考勤人员
        List<String> dkTags = samsSchedulingEmployeeMapper.selectBySchedulingIdAndAfterDutyDate(samsScheduling.getSchedulingId(), futureFirstDate);
        // 删除已排班的排班员工
        samsSchedulingEmployeeMapper.deleteBySchedulingIdAndAfterDutyDate(samsScheduling.getSchedulingId(), futureFirstDate, dkTags);

//        // 按照日期重新排班
        List<String> dutyDateList = new ArrayList<>(Arrays.asList(samsScheduling.getDutyDate().split(",")));
        // 过滤掉小于futureFirstDate的日期不包括等于
        dutyDateList = dutyDateList.stream().filter(d -> !DateUtils.parseDate(d).before(DateUtils.parseDate(futureFirstDate))).collect(Collectors.toList());
        log.info("=============dutyDateList:{}", dutyDateList);

        // 解析
        List<SamsPostClock> postClockList = samsPost.getSamsPostClockList();

        // 打卡点信息
        JSONArray clockPointInfo = new JSONArray();

        postClockList.forEach(samsPostClock -> {
            // 根据postClockId将clockEmp赋值给postClock
            JSONArray postClockArray = JSONArray.parseArray(samsScheduling.getPostClock());
            for (int i = 0; i < postClockArray.size(); i++) {
                JSONObject postClockObject = postClockArray.getJSONObject(i);
                System.out.println(postClockObject.getString("postClockId"));
                System.out.println(samsPostClock.getPostClockId());
                if(String.valueOf(samsPostClock.getPostClockId()).equals(postClockObject.getString("postClockId"))){
                    samsPostClock.setClockEmp(postClockObject.getJSONArray("clockEmp").toString());
                }
            }
            clockPointInfo.add(samsPostClock);
        });

//        String[] dutyDateArr = samsScheduling.getDutyDate().split(",");
        log.info("=============clockPointInfo:{}=============", clockPointInfo);
        // 考勤人员
        Map<String, String> kqEmpMap = new HashMap<>();
        dutyDateList.forEach(s1 -> {
            log.info("=============s1:{}===========" , s1);
            // 对象：key: 员工id#是否加班#postClockId；obj: postClock,其中clockEmp应该只有该用户
            JSONObject jsonObject1 = new JSONObject();
            for (int i = 0; i < clockPointInfo.size(); i++) {
                // postClock
                JSONObject jsonObject = clockPointInfo.getJSONObject(i);
                log.info("=============jsonObject:" + jsonObject);
                // clockEmp
                JSONArray clockArray = jsonObject.getJSONArray("clockEmp");
                if (clockArray == null) {
                    continue;
                }
                for (int i1 = 0; i1 < clockArray.size(); i1++) {
                    // 排班员工
                    JSONObject people = clockArray.getJSONObject(i1);
                    // s格式：排班员工id#是否加班
                    String s = people.getString("value");
                    String objKey = s + "#" + jsonObject.getLong("postClockId");
                    JSONArray jsonArray1 = jsonObject1.getJSONArray(objKey);
                    if (jsonArray1 == null) {
                        jsonArray1 = new JSONArray();
                    }
                    JSONObject newJsonObject = new JSONObject(jsonObject);
                    newJsonObject.put("clockEmp", JSONArray.of(people));
                    jsonArray1.add(newJsonObject);
                    jsonObject1.put(objKey, jsonArray1);
                }
            }
            log.info("=============jsonObject1:" + jsonObject1);
            for (String s : jsonObject1.keySet()) {
                // 定时任务运行时间
//                Date scheduleRunTime = new Date();
                String[] strArray = s.split("#");
                JSONArray clockArray = jsonObject1.getJSONArray(s);
                String dkTag = UUID.randomUUID().toString();
                for (int k = 1; k <= 2; k++) {
                    if (strArray.length > 1) {
                        if (strArray[1].equalsIgnoreCase("0")) {
                            JSONArray otherArray = jsonObject1.getJSONArray(strArray[0] + "#1" + "#" + strArray[2]);
                            if (otherArray != null) {
                                clockArray.addAll(otherArray);
                            }
                        } else {
                            JSONArray otherArray = jsonObject1.getJSONArray(strArray[0] + "#0" + "#" + strArray[2]);
                            if (otherArray != null) {
                                clockArray.addAll(otherArray);
                            }
                        }
                    } else {
                        JSONArray otherArray = jsonObject1.getJSONArray(strArray[0]);
                        if (otherArray != null) {
                            clockArray.addAll(otherArray);
                        }
                    }

                    SamsSchedulingEmployee samsSchedulingEmployee = new SamsSchedulingEmployee();
                    samsSchedulingEmployee.setDkTag(dkTag);
                    samsSchedulingEmployee.setSchedulingId(samsScheduling.getSchedulingId());
                    SamsProjectEmployee samsProjectEmployee = samsProjectEmployeeMapper.selectByEmployeeIdAndProjectId(Long.parseLong(strArray[0]), samsScheduling.getProjectId());
                    samsSchedulingEmployee.setProjectEmployeeId(samsProjectEmployee.getProjectEmployeeId());
                    samsSchedulingEmployee.setEmployeeId(Long.parseLong(strArray[0]));
                    samsSchedulingEmployee.setScheduleDate(s1);
                    String dutyDate = s1;
                    if (null != clockArray && clockArray.size() > 0) {
                        JSONObject clockObject = clockArray.getJSONObject(0);
                        if (com.ruoyi.common.utils.StringUtils.isNotBlank(clockObject.getString("startTime")) && com.ruoyi.common.utils.StringUtils.isNotBlank(clockObject.getString("endTime"))) {

                            samsSchedulingEmployee.setStartTime(clockObject.getString("startTime"));
                            samsSchedulingEmployee.setStartTimeBefore(clockObject.getString("startTimeBefore"));
                            samsSchedulingEmployee.setStartTimeAfter(clockObject.getString("startTimeAfter"));
                            samsSchedulingEmployee.setEndTime(clockObject.getString("endTime"));
                            samsSchedulingEmployee.setEndTimeBefore(clockObject.getString("endTimeBefore"));
                            samsSchedulingEmployee.setEndTimeAfter(clockObject.getString("endTimeAfter"));
                            Date startTime = DateUtils.parseDate(s1 + " " + clockObject.getString("startTime"));
                            Date endTime = DateUtils.parseDate(s1 + " " + clockObject.getString("endTime"));
//                            scheduleRunTime = endTime;
                            if (k == 2 && clockObject.getInteger("nextDay") == 1) {
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(endTime);
                                calendar.add(Calendar.DAY_OF_MONTH, 1);
                                dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd", calendar.getTime());
                            }
                        }

                        samsSchedulingEmployee.setNextDay(clockObject.getInteger("nextDay"));
                        samsSchedulingEmployee.setIsAnytimeCheckIn(clockObject.getInteger("isAnytimeCheckIn"));
                    }
                    samsSchedulingEmployee.setDutyDate(DateUtils.parseDate(dutyDate));
                    samsSchedulingEmployee.setPostClock(clockArray.toString());
                    samsSchedulingEmployee.setCreateTime(DateUtils.getNowDate());
                    samsSchedulingEmployee.setDkType(k);
                    if (strArray.length > 1) {
                        samsSchedulingEmployee.setSfjb(Integer.parseInt(strArray[1]));
                    }
                    samsSchedulingEmployeeMapper.insertSamsSchedulingEmployee(samsSchedulingEmployee);

                }

            }
            for (String s : jsonObject1.keySet()) {
                String[] strArray = s.split("#");
                kqEmpMap.put(strArray[0], strArray[0]);
            }
        });
        String str = kqEmpMap.entrySet().stream().map(e -> e.getKey())
                .collect(Collectors.joining(","));
        samsScheduling.setKqEmp(str);
        samsSchedulingMapper.updateSamsScheduling(samsScheduling);

    }

    /**
     * 批量删除岗位信息
     *
     * @param postIds 需要删除的岗位信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsPostByPostIds(Long[] postIds) {
        return samsPostMapper.deleteSamsPostByPostIds(postIds);
    }

    /**
     * 删除岗位信息信息
     *
     * @param postId 岗位信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsPostByPostId(Long postId) {
        return samsPostMapper.deleteSamsPostByPostId(postId);
    }

    @Override
    public List<SamsPostExportVo> selectSamsPostExportList(SamsPost samsPost) {
        List<SamsPostExportVo> samsPostExportVoList = new ArrayList<>();
        List<SamsPostExportDataVo> samsPostExportVo = samsPostMapper.selectSamsPostExportVoList(samsPost);
        if (!CollectionUtils.isEmpty(samsPostExportVo)) {
            Map<Long, List<SamsPostExportDataVo>> exportDataVoMap = samsPostExportVo.stream().collect(Collectors.groupingBy(SamsPostExportDataVo::getPostId));
            exportDataVoMap.forEach((k, v) -> {
                if (!CollectionUtils.isEmpty(v)) {
                    SamsPostExportVo exportVo = new SamsPostExportVo();
                    List<SamsPostClock> postClockList = new ArrayList<>();
                    BeanUtils.copyProperties(v.get(0), exportVo);
                    v.forEach(vo -> {
                        SamsPostClock samsPostClock = new SamsPostClock();
                        BeanUtils.copyProperties(vo, samsPostClock);
                        postClockList.add(samsPostClock);
                    });
                    exportVo.setPostClockList(postClockList);
                    samsPostExportVoList.add(exportVo);
                }
            });
        }
        return samsPostExportVoList;
    }
}
