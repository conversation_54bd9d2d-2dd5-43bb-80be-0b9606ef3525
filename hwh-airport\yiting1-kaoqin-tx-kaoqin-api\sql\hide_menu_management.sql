-- =============================================
-- 隐藏考勤统计模块中的菜单管理页面
-- =============================================

-- 将菜单管理页面设置为隐藏状态
-- menu_id = 102 对应菜单管理页面
-- visible = '1' 表示隐藏，'0' 表示显示
UPDATE sys_menu 
SET visible = '1', 
    update_time = NOW(),
    update_by = 'admin'
WHERE menu_id = 102 
  AND menu_name = '菜单管理';

-- 验证更新结果
SELECT menu_id, menu_name, visible, status, update_time, update_by 
FROM sys_menu 
WHERE menu_id = 102;

-- 可选：如果需要同时隐藏菜单管理的相关按钮权限，可以执行以下语句
-- UPDATE sys_menu 
-- SET visible = '1', 
--     update_time = NOW(),
--     update_by = 'admin'
-- WHERE parent_id = 102 
--   AND menu_type = 'F';
