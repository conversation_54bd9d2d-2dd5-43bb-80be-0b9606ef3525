import request from "./request.js"
export class Unis {
	// loginByPhone(code){
	// 	return request('/login/loginByAuth?code='+code,{},'GET')
	// }


	// 获取用户信息
	getUser(id) {
		return request('/app/appuser/' + id, {}, "GET")
	}
	// 首页信息
	getIndexData(data) {
		console.log('请求')
		return request('/app/schedulingEmployee/mykqdk', data, 'get')
	}
	// 获取小程序用户信息
	getUserInfo(){
		return request('/getInfo',{},'get')
	}
	login(code,openId){
		console.log(code)
		return request('/login?code='+code+'&openId='+openId,{},'post')
	}
	changeUserInfo(data){
		return request('/app/user/profile',data,'put')
	}
	getUserInfo(){
		return request('/getInfo',{},'get')
	}

	getshenqingList(data){
		return request('/app/employeeApply/list',data,'get')
	}

	addShengqing(data){
		return request('/app/employeeApply/',data,'post')
	}
	getgwList(data){
		return request('/app/samsPost/list',data,'get')
	}
	getProjectList(data){
		return request('/app/project/kqxmlist',data,'get')
	}
	// 我的考勤
	getMykaoqin(data){
		return request('/app/schedulingEmployee/mykqdk',data,'get')
	}
	// 员工打卡
	daka(data){
		return request('/app/schedulingEmployee/dodaka',data,'put')
	}

	// 获取打卡详情
	getDakaDetail(id){
		return request('/app/schedulingEmployee/'+id,{},'get')
	}

	getdeptList(data){
		return request('/app/dept/list',data,'get')
	}
	getaiportList(data){
		return request('/app/airport/list',data,'get')
	}
	getareaList(data){
		return request('/app/airportArea/list',data,'get')
	}
	getgroupList(data){
		return request('/app/airportGroup/list',data,'get')
	}
	getteamList(data){
		return request('/app/airportTeam/list',data,'get')
	}
	getprojectList(data){
		return request('/app/project/list',data,'get')
	}

	//
	getPaiBanList(data){
		return request('/app/scheduling/list',data,'get')
	}

	getGwList(data){
		return request('/app/samsPost/list',data,'get')
	}
	getzqry(data){
		return request('/app/projectEmployee/list',data,'get')
	}
	postClockList(data){
		return request('/app/postClock/list',data,'get')
	}
	addPaiBan(data){
		return request('/app/scheduling',data,'post')
	}
	putPaiBan(data){
		return request('/app/scheduling',data,'put')
	}
	getOpenId(code){
		return request('/authToken4Miniapp?code='+code,{},'get')
	}
	vervifyPerson(url){
		return request('/app/schedulingEmployee/verifyPerson?url='+url,{},'get')
	}
}

export default Unis
