package com.ruoyi.system.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.SamsProject;
import com.ruoyi.system.domain.vo.SamsProjectExportVo;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ISamsProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@Service
public class SamsProjectServiceImpl implements ISamsProjectService {
    @Autowired
    private SamsProjectMapper samsProjectMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SamsAirportMapper samsAirportMapper;
    @Autowired
    private SamsAirportAreaMapper samsAirportAreaMapper;
    @Autowired
    private SamsAirportGroupMapper samsAirportGroupMapper;

    /**
     * 查询项目信息
     *
     * @param projectId 项目信息主键
     * @return 项目信息
     */
    @Override
    public SamsProject selectSamsProjectByProjectId(Long projectId) {
        return samsProjectMapper.selectSamsProjectByProjectId(projectId);
    }

    /**
     * 查询项目信息列表
     *
     * @param samsProject 项目信息
     * @return 项目信息
     */
    @Override
    public List<SamsProject> selectSamsProjectList(SamsProject samsProject) {
        List<SamsProject> list = samsProjectMapper.selectSamsProjectList(samsProject);
        if (list != null && list.size() > 0) {
            for (SamsProject p : list) {
                p.setDept(sysDeptMapper.selectDeptById(p.getDeptId()));
                p.setAirport(samsAirportMapper.selectSamsAirportByAirportId(p.getAirportId()));
                p.setArea(samsAirportAreaMapper.selectSamsAirportAreaByAreaId(p.getAreaId()));
                p.setGroup(samsAirportGroupMapper.selectSamsAirportGroupByGroupId(p.getGroupId()));
            }
        }

        return list;
    }

    @Override
    public List<SamsProject> kqxmlist(Long employeeId) {
        return samsProjectMapper.kqxmlist(employeeId);
    }

    /**
     * 新增项目信息
     *
     * @param samsProject 项目信息
     * @return 结果
     */
    @Override
    public int insertSamsProject(SamsProject samsProject) {
        samsProject.setCreateTime(DateUtils.getNowDate());
        return samsProjectMapper.insertSamsProject(samsProject);
    }

    /**
     * 修改项目信息
     *
     * @param samsProject 项目信息
     * @return 结果
     */
    @Override
    public int updateSamsProject(SamsProject samsProject) {
        samsProject.setUpdateTime(DateUtils.getNowDate());
        return samsProjectMapper.updateSamsProject(samsProject);
    }

    /**
     * 批量删除项目信息
     *
     * @param projectIds 需要删除的项目信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsProjectByProjectIds(Long[] projectIds) {
        return samsProjectMapper.deleteSamsProjectByProjectIds(projectIds);
    }

    /**
     * 删除项目信息信息
     *
     * @param projectId 项目信息主键
     * @return 结果
     */
    @Override
    public int deleteSamsProjectByProjectId(Long projectId) {
        return samsProjectMapper.deleteSamsProjectByProjectId(projectId);
    }

    /**
     * 查询员工信息列表
     *
     * @param samsProject 项目信息
     * @return 员工信息
     */
    @Override
    public List<SamsProjectExportVo> selectSamsProjectExportList(SamsProject samsProject) {
        return samsProjectMapper.selectSamsProjectExportVoList(samsProject);
    }
}
