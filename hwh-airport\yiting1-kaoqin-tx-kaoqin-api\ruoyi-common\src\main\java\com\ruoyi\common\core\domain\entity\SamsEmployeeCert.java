package com.ruoyi.common.core.domain.entity;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 员工证书信息对象 sams_employee_cert
 * 
 * <AUTHOR>
 * @date 2024-11-15
 */
@Data
public class SamsEmployeeCert extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 证书信息ID */
    private Long employeeCertId;

    /** 员工信息ID */
    @Excel(name = "员工信息ID")
    private Long employeeId;

    /** 字典值 */
    @Excel(name = "字典值")
    private String certDict;

    /** 证书名称 */
    @Excel(name = "证书名称")
    private String certName;

    /** 证书编号 */
    @Excel(name = "证书编号")
    private String certNo;

    /** 证书有效期 */
    @Excel(name = "证书有效期")
    private String certVali;

    /** 证书照片 */
    @Excel(name = "证书照片")
    private String certImg;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;
    @Excel(name = "证书等级")
    private String certLevel;
}
