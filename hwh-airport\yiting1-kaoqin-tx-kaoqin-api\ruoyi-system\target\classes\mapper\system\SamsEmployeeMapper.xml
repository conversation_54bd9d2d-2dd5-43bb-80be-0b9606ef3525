<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsEmployeeMapper">

    <resultMap type="SamsEmployee" id="SamsEmployeeResult">
        <result property="employeeId"    column="employee_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="airportId"    column="airport_id"    />
        <result property="areaId"    column="area_id"    />
        <result property="groupId"    column="group_id"    />
        <result property="teamId"    column="team_id"    />
        <result property="empNo"    column="emp_no"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="name"    column="name"    />
        <result property="namePinyin"    column="name_pinyin"    />
        <result property="nameJianpin"    column="name_jianpin"    />
        <result property="sex"    column="sex"    />
        <result property="nation"    column="nation"    />
        <result property="typeName"    column="type_name"    />
        <result property="cardType"    column="card_type"    />
        <result property="cardId"    column="card_id"    />
        <result property="checkImg"    column="check_img"    />
        <result property="jiguan"    column="jiguan"    />
        <result property="domicileAddr"    column="domicile_addr"    />
        <result property="homeAddr"    column="home_addr"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="domicileType"    column="domicile_type"    />
        <result property="polotocalStatus"    column="polotocal_status"    />
        <result property="healthStatus"    column="health_status"    />
        <result property="maritalStatus"    column="marital_status"    />
        <result property="education"    column="education"    />
        <result property="major"    column="major"    />
        <result property="school"    column="school"    />
        <result property="educationImg"    column="education_img"    />
        <result property="degree"    column="degree"    />
        <result property="talent"    column="talent"    />
        <result property="postName"    column="post_name"    />
        <result property="cjgzDate"    column="cjgz_date"    />
        <result property="emContact"    column="em_contact"    />
        <result property="emContactPhone"    column="em_contact_phone"    />
        <result property="ruzhiDate"    column="ruzhi_date"    />
        <result property="lizhiDate"    column="lizhi_date"    />
        <result property="gsYear"    column="gs_year"    />
        <result property="htComplateDate"    column="ht_complate_date"    />
        <result property="tuixiuDate"    column="tuixiu_date"    />
        <result property="bindPhone"    column="bind_phone"    />
        <result property="openId"    column="open_id"    />
        <result property="nicheng"    column="nicheng"    />
        <result property="avate"    column="avate"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="pbqx"    column="pbqx"    />
        <result property="birthAddress"    column="birth_address"    />
        <result property="hkszd"    column="hkszd"    />

<!--        部门名称-->
        <association property="sysDept" column="dept_id" javaType="com.ruoyi.common.core.domain.entity.SysDept">
            <id property="deptId" column="dept_id" />
            <result property="deptName" column="dept_name" />
        </association>
        <association property="samsAirport" column="airport_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirport">
            <id property="airportId" column="airport_id" />
            <result property="airportName" column="airport_name" />
        </association>
        <association property="samsAirportArea" column="area_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirportArea">
            <id property="areaId" column="area_id" />
            <result property="areaName" column="area_name" />
        </association>
        <association property="samsAirportGroup" column="group_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirportGroup">
            <id property="groupId" column="group_id" />
            <result property="groupName" column="group_name" />
        </association>
        <association property="samsAirportTeam" column="team_id" javaType="com.ruoyi.common.core.domain.entity.SamsAirportTeam">
            <id property="teamId" column="team_id" />
            <result property="teamName" column="team_name" />
        </association>
    <!--        <result property="deptName"    column="dept_name"    />-->
    <!--        <result property="airportName"    column="airport_name"    />-->
    <!--        <result property="areaName"    column="area_name"    />-->
    <!--        <result property="groupName"    column="group_name"    />-->
    <!--        <result property="teamName"    column="team_name"    />-->

    </resultMap>

    <resultMap type="samsEmployeeExportVo" id="SamsEmployeeExportVoResult" extends="SamsEmployeeResult">
        <result property="empNo"    column="emp_no"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="name"    column="name"    />
        <result property="namePinyin"    column="name_pinyin"    />
        <result property="nameJianpin"    column="name_jianpin"    />
        <result property="sex"    column="sex"    />
        <result property="nation"    column="nation"    />
        <result property="typeName"    column="type_name"    />
        <result property="cardType"    column="card_type"    />
        <result property="cardId"    column="card_id"    />
        <result property="jiguan"    column="jiguan"    />
        <result property="domicileAddr"    column="domicile_addr"    />
        <result property="homeAddr"    column="home_addr"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="domicileType"    column="domicile_type"    />
        <result property="polotocalStatus"    column="polotocal_status"    />
        <result property="healthStatus"    column="health_status"    />
        <result property="maritalStatus"    column="marital_status"    />
        <result property="education"    column="education"    />
        <result property="major"    column="major"    />
        <result property="school"    column="school"    />
        <result property="educationImg"    column="education_img"    />
        <result property="degree"    column="degree"    />
        <result property="talent"    column="talent"    />
        <result property="postName"    column="post_name"    />
        <result property="cjgzDate"    column="cjgz_date"    />
        <result property="emContact"    column="em_contact"    />
        <result property="emContactPhone"    column="em_contact_phone"    />
        <result property="ruzhiDate"    column="ruzhi_date"    />
        <result property="lizhiDate"    column="lizhi_date"    />
        <result property="gsYear"    column="gs_year"    />
        <result property="htComplateDate"    column="ht_complate_date"    />
        <result property="tuixiuDate"    column="tuixiu_date"    />
        <result property="bindPhone"    column="bind_phone"    />
        <result property="openId"    column="open_id"    />
        <result property="nicheng"    column="nicheng"    />
        <result property="avate"    column="avate"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="pbqx"    column="pbqx"    />
        <result property="birthAddress"    column="birth_address"    />
        <result property="hkszd"    column="hkszd"    />
        <result property="deptName"    column="dept_name"    />
        <result property="airport"    column="airport_name"    />
        <result property="area"    column="area_name"    />
        <result property="group"    column="group_name"    />
        <result property="team"    column="team_name"    />
    </resultMap>

    <sql id="selectSamsEmployeeVo">
        SELECT
            se.employee_id,
            se.dept_id,
            se.airport_id,
            se.pbqx,
            se.area_id,
            se.group_id,
            se.team_id,
            se.emp_no,
            se.mobile_phone,
            se.NAME,
            se.name_pinyin,
            se.name_jianpin,
            se.sex,
            se.nation,
            se.type_name,
            se.card_type,
            se.card_id,
            se.check_img,
            se.jiguan,
            se.domicile_addr,
            se.home_addr,
            se.birth_date,
            se.domicile_type,
            se.polotocal_status,
            se.health_status,
            se.marital_status,
            se.education,
            se.major,
            se.school,
            se.education_img,
            se.degree,
            se.talent,
            se.post_name,
            se.cjgz_date,
            se.em_contact,
            se.em_contact_phone,
            se.ruzhi_date,
            se.lizhi_date,
            se.gs_year,
            se.ht_complate_date,
            se.tuixiu_date,
            se.bind_phone,
            se.open_id,
            se.nicheng,
            se.avate,
            se.STATUS,
            se.del_flag,
            se.create_by,
            se.create_time,
            se.update_by,
            se.update_time,
            se.remark,
            se.birth_address,
            se.hkszd ,
            sd.dept_name,
            sa.airport_name,
            saa.area_name,
            sat.team_name,
            sag.group_name
        FROM
            sams_employee se
                LEFT JOIN sys_dept sd ON se.dept_id = sd.dept_id
                LEFT JOIN sams_airport sa ON se.airport_id = sa.airport_id
                LEFT JOIN sams_airport_area saa ON se.area_id = saa.area_id
                LEFT JOIN sams_airport_team sat ON se.team_id = sat.team_id
                LEFT JOIN sams_airport_group sag ON se.group_id = sag.group_id
    </sql>

    <select id="selectSamsEmployeeList" parameterType="SamsEmployee" resultMap="SamsEmployeeResult">
        <include refid="selectSamsEmployeeVo"/>
        <where>
            <if test="deptId != null "> and se.dept_id = #{deptId}</if>
            <if test="airportId != null "> and se.airport_id = #{airportId}</if>
            <if test="areaId != null "> and se.area_id = #{areaId}</if>
            <if test="groupId != null "> and se.group_id = #{groupId}</if>
            <if test="teamId != null "> and se.team_id = #{teamId}</if>
            <if test="empNo != null  and empNo != ''"> and se.emp_no = #{empNo}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and se.mobile_phone = #{mobilePhone}</if>
            <if test="name != null  and name != ''"> and se.name like concat('%', #{name}, '%')</if>
            <if test="namePinyin != null  and namePinyin != ''"> and se.name_pinyin = #{namePinyin}</if>
            <if test="nameJianpin != null  and nameJianpin != ''"> and se.name_jianpin = #{nameJianpin}</if>
            <if test="sex != null "> and se.sex = #{sex}</if>
            <if test="nation != null  and nation != ''"> and se.nation = #{nation}</if>
            <if test="typeName != null "> and se.type_name =#{typeName}</if>
            <if test="cardType != null "> and se.card_type = #{cardType}</if>
            <if test="cardId != null  and cardId != ''"> and se.card_id = #{cardId}</if>
            <if test="checkImg != null  and checkImg != ''"> and se.check_img = #{checkImg}</if>
            <if test="jiguan != null  and jiguan != ''"> and se.jiguan = #{jiguan}</if>
            <if test="domicileAddr != null  and domicileAddr != ''"> and se.domicile_addr = #{domicileAddr}</if>
            <if test="homeAddr != null  and homeAddr != ''"> and se.home_addr = #{homeAddr}</if>
            <if test="birthDate != null "> and se.birth_date = #{birthDate}</if>
            <if test="domicileType != null "> and se.domicile_type = #{domicileType}</if>
            <if test="polotocalStatus != null "> and se.polotocal_status = #{polotocalStatus}</if>
            <if test="healthStatus != null  and healthStatus != ''"> and se.health_status = #{healthStatus}</if>
            <if test="maritalStatus != null  and maritalStatus != ''"> and se.marital_status = #{maritalStatus}</if>
            <if test="education != null "> and se.education = #{education}</if>
            <if test="major != null  and major != ''"> and se.major = #{major}</if>
            <if test="school != null  and school != ''"> and se.school = #{school}</if>
            <if test="educationImg != null  and educationImg != ''"> and se.education_img = #{educationImg}</if>
            <if test="degree != null  and degree != ''"> and se.degree = #{degree}</if>
            <if test="talent != null  and talent != ''"> and se.talent = #{talent}</if>
            <if test="postName != null  and postName != ''"> and se.post_name like concat('%', #{postName}, '%')</if>
            <if test="cjgzDate != null "> and se.cjgz_date = #{cjgzDate}</if>
            <if test="emContact != null  and emContact != ''"> and se.em_contact = #{emContact}</if>
            <if test="emContactPhone != null  and emContactPhone != ''"> and se.em_contact_phone = #{emContactPhone}</if>
            <if test="ruzhiDate != null "> and se.ruzhi_date = #{ruzhiDate}</if>
            <if test="lizhiDate != null "> and se.lizhi_date = #{lizhiDate}</if>
            <if test="gsYear != null  and gsYear != ''"> and se.gs_year = #{gsYear}</if>
            <if test="htComplateDate != null "> and se.ht_complate_date = #{htComplateDate}</if>
            <if test="tuixiuDate != null "> and se.tuixiu_date = #{tuixiuDate}</if>
            <if test="bindPhone != null  and bindPhone != ''"> and se.bind_phone = #{bindPhone}</if>
            <if test="openId != null  and openId != ''"> and se.open_id = #{openId}</if>
            <if test="nicheng != null  and nicheng != ''"> and se.nicheng = #{nicheng}</if>
            <if test="avate != null  and avate != ''"> and se.avate = #{avate}</if>
            <if test="status != null  and status != ''"> and se.status = #{status}</if>
        </where>
    </select>


    <select id="selectSamsEmployeeList2" parameterType="SamsEmployee" resultMap="SamsEmployeeResult">
        select e.*,sd.dept_name,
        sa.airport_name,
        saa.area_name,
        sat.team_name,
        sag.group_name from sams_employee e
        LEFT JOIN sys_dept sd ON e.dept_id = sd.dept_id
        LEFT JOIN sams_airport sa ON e.airport_id = sa.airport_id
        LEFT JOIN sams_airport_area saa ON e.area_id = saa.area_id
        LEFT JOIN sams_airport_team sat ON e.team_id = sat.team_id
        LEFT JOIN sams_airport_group sag ON e.group_id = sag.group_id
        where e.employee_id not in(select pe.employee_id from sams_project_employee pe where pe.project_id = #{projectId})
        <if test="name != null  and name != ''"> and e.name like concat('%', #{name}, '%')</if>
        <if test="deptId != null">
            and e.dept_id = #{deptId}
        </if>
        <if test="airportId != null">
            and e.airport_id = #{airportId}
        </if>
        <if test="areaId != null">
            and e.area_id = #{areaId}
        </if>
        <if test="groupId != null">
            and e.group_id = #{groupId}
        </if>
        <if test="teamId != null">
            and e.team_id = #{teamId}
        </if>
    </select>


    <select id="selectSamsEmployeeByEmployeeId" parameterType="Long" resultMap="SamsEmployeeResult">
        <include refid="selectSamsEmployeeVo"/>
        where se.employee_id = #{employeeId}
    </select>

    <select id="totalRs" resultType="int">
        select ifnull(count(1),0) from sams_employee
    </select>

    <select id="cxnlrys" parameterType="integer" resultType="int">
        SELECT
            ifnull(count(1),0)
        FROM
            sams_employee where TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) between #{age1} and #{age2}
    </select>

    <select id="cxxlrys" parameterType="integer" resultType="int">
        select ifnull(count(1),0) from sams_employee where education = #{education}
    </select>




    <select id="selectByBindPhone" parameterType="string" resultMap="SamsEmployeeResult">
        <include refid="selectSamsEmployeeVo"/>
        where se.bind_phone = #{bindPhone} limit 1
    </select>

    <select id="nxRs" parameterType="integer" resultType="int">
        select ifnull(count(1),0) from sams_employee where sex=#{sex}
    </select>

    <select id="typeRs" parameterType="integer" resultType="int">
        select ifnull(count(1),0) from sams_employee where  type_name =#{typeName}
    </select>



    <insert id="insertSamsEmployee" parameterType="SamsEmployee" useGeneratedKeys="true" keyProperty="employeeId">
        insert into sams_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="airportId != null">airport_id,</if>
            <if test="areaId != null">area_id,</if>
            <if test="groupId != null">group_id,</if>
            <if test="teamId != null">team_id,</if>
            <if test="empNo != null">emp_no,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="name != null">name,</if>
            <if test="namePinyin != null">name_pinyin,</if>
            <if test="nameJianpin != null">name_jianpin,</if>
            <if test="sex != null">sex,</if>
            <if test="nation != null">nation,</if>
            <if test="typeName != null">type_name,</if>
            <if test="cardType != null">card_type,</if>
            <if test="cardId != null">card_id,</if>
            <if test="checkImg != null">check_img,</if>
            <if test="jiguan != null">jiguan,</if>
            <if test="domicileAddr != null">domicile_addr,</if>
            <if test="homeAddr != null">home_addr,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="domicileType != null">domicile_type,</if>
            <if test="polotocalStatus != null">polotocal_status,</if>
            <if test="healthStatus != null">health_status,</if>
            <if test="maritalStatus != null">marital_status,</if>
            <if test="education != null">education,</if>
            <if test="major != null">major,</if>
            <if test="school != null">school,</if>
            <if test="educationImg != null">education_img,</if>
            <if test="degree != null">degree,</if>
            <if test="talent != null">talent,</if>
            <if test="postName != null">post_name,</if>
            <if test="cjgzDate != null">cjgz_date,</if>
            <if test="emContact != null">em_contact,</if>
            <if test="emContactPhone != null">em_contact_phone,</if>
            <if test="ruzhiDate != null">ruzhi_date,</if>
            <if test="lizhiDate != null">lizhi_date,</if>
            <if test="gsYear != null">gs_year,</if>
            <if test="htComplateDate != null">ht_complate_date,</if>
            <if test="tuixiuDate != null">tuixiu_date,</if>
            <if test="bindPhone != null">bind_phone,</if>
            <if test="openId != null">open_id,</if>
            <if test="nicheng != null">nicheng,</if>
            <if test="avate != null">avate,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="pbqx != null">pbqx,</if>
            <if test="birthAddress != null">birth_address,</if>
            <if test="hkszd != null">hkszd,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="airportId != null">#{airportId},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="empNo != null">#{empNo},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="name != null">#{name},</if>
            <if test="namePinyin != null">#{namePinyin},</if>
            <if test="nameJianpin != null">#{nameJianpin},</if>
            <if test="sex != null">#{sex},</if>
            <if test="nation != null">#{nation},</if>
            <if test="typeName != null">#{typeName},</if>
            <if test="cardType != null">#{cardType},</if>
            <if test="cardId != null">#{cardId},</if>
            <if test="checkImg != null">#{checkImg},</if>
            <if test="jiguan != null">#{jiguan},</if>
            <if test="domicileAddr != null">#{domicileAddr},</if>
            <if test="homeAddr != null">#{homeAddr},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="domicileType != null">#{domicileType},</if>
            <if test="polotocalStatus != null">#{polotocalStatus},</if>
            <if test="healthStatus != null">#{healthStatus},</if>
            <if test="maritalStatus != null">#{maritalStatus},</if>
            <if test="education != null">#{education},</if>
            <if test="major != null">#{major},</if>
            <if test="school != null">#{school},</if>
            <if test="educationImg != null">#{educationImg},</if>
            <if test="degree != null">#{degree},</if>
            <if test="talent != null">#{talent},</if>
            <if test="postName != null">#{postName},</if>
            <if test="cjgzDate != null">#{cjgzDate},</if>
            <if test="emContact != null">#{emContact},</if>
            <if test="emContactPhone != null">#{emContactPhone},</if>
            <if test="ruzhiDate != null">#{ruzhiDate},</if>
            <if test="lizhiDate != null">#{lizhiDate},</if>
            <if test="gsYear != null">#{gsYear},</if>
            <if test="htComplateDate != null">#{htComplateDate},</if>
            <if test="tuixiuDate != null">#{tuixiuDate},</if>
            <if test="bindPhone != null">#{bindPhone},</if>
            <if test="openId != null">#{openId},</if>
            <if test="nicheng != null">#{nicheng},</if>
            <if test="avate != null">#{avate},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="pbqx != null">#{pbqx},</if>
            <if test="birthAddress != null">#{birthAddress},</if>
            <if test="hkszd != null">#{hkszd},</if>
         </trim>
    </insert>

    <update id="updateSamsEmployee" parameterType="SamsEmployee">
        update sams_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="airportId != null">airport_id = #{airportId},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="empNo != null">emp_no = #{empNo},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="name != null">name = #{name},</if>
            <if test="namePinyin != null">name_pinyin = #{namePinyin},</if>
            <if test="nameJianpin != null">name_jianpin = #{nameJianpin},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="nation != null">nation = #{nation},</if>
            <if test="typeName != null">type_name = #{typeName},</if>
            <if test="cardType != null">card_type = #{cardType},</if>
            <if test="cardId != null">card_id = #{cardId},</if>
            <if test="checkImg != null">check_img = #{checkImg},</if>
            <if test="jiguan != null">jiguan = #{jiguan},</if>
            <if test="domicileAddr != null">domicile_addr = #{domicileAddr},</if>
            <if test="homeAddr != null">home_addr = #{homeAddr},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="domicileType != null">domicile_type = #{domicileType},</if>
            <if test="polotocalStatus != null">polotocal_status = #{polotocalStatus},</if>
            <if test="healthStatus != null">health_status = #{healthStatus},</if>
            <if test="maritalStatus != null">marital_status = #{maritalStatus},</if>
            <if test="education != null">education = #{education},</if>
            <if test="major != null">major = #{major},</if>
            <if test="school != null">school = #{school},</if>
            <if test="educationImg != null">education_img = #{educationImg},</if>
            <if test="degree != null">degree = #{degree},</if>
            <if test="talent != null">talent = #{talent},</if>
            <if test="postName != null">post_name = #{postName},</if>
            <if test="cjgzDate != null">cjgz_date = #{cjgzDate},</if>
            <if test="emContact != null">em_contact = #{emContact},</if>
            <if test="emContactPhone != null">em_contact_phone = #{emContactPhone},</if>
            <if test="ruzhiDate != null">ruzhi_date = #{ruzhiDate},</if>
            <if test="lizhiDate != null">lizhi_date = #{lizhiDate},</if>
            <if test="gsYear != null">gs_year = #{gsYear},</if>
            <if test="htComplateDate != null">ht_complate_date = #{htComplateDate},</if>
            <if test="tuixiuDate != null">tuixiu_date = #{tuixiuDate},</if>
            <if test="bindPhone != null">bind_phone = #{bindPhone},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="nicheng != null">nicheng = #{nicheng},</if>
            <if test="avate != null">avate = #{avate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="pbqx != null">pbqx = #{pbqx},</if>
            <if test="birthAddress != null">birth_address = #{birthAddress},</if>
            <if test="hkszd != null">hkszd = #{hkszd},</if>
        </trim>
        where employee_id = #{employeeId}
    </update>

    <update id="updateAvate" parameterType="string">
        update sams_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="avate != null">avate = #{avate},</if>
        </trim>
        where open_id = #{openId}
    </update>

    <update id="jcwxbd" parameterType="long">
        update sams_employee set bind_phone =null,open_id=null,nicheng=null,avate=null where employee_id = #{employeeId}
    </update>

    <delete id="deleteSamsEmployeeByEmployeeId" parameterType="Long">
        delete from sams_employee where employee_id = #{employeeId}
    </delete>

    <delete id="deleteSamsEmployeeByEmployeeIds" parameterType="String">
        delete from sams_employee where employee_id in
        <foreach item="employeeId" collection="array" open="(" separator="," close=")">
            #{employeeId}
        </foreach>
    </delete>

    <select id="selectSamsEmployeeExportVoList" parameterType="SamsEmployee" resultMap="SamsEmployeeExportVoResult">
        select se.emp_no, se.mobile_phone, se.name, se.name_pinyin, se.name_jianpin, se.sex, se.nation, se.type_name,
            se.card_type, se.card_id, se.jiguan, se.domicile_addr, se.home_addr, se.birth_date, se.domicile_type, se.polotocal_status, se.health_status, se.marital_status,
            se.education, se.major, se.school, se.education_img, se.degree, se.talent, se.post_name, se.cjgz_date, se.em_contact, se.em_contact_phone, se.ruzhi_date, se.lizhi_date, se.gs_year,
            se.ht_complate_date, se.tuixiu_date, se.bind_phone, se.open_id, se.nicheng, se.avate, se.status, se.create_by, se.create_time, se.update_by, se.update_time, se.remark,birth_address, se.hkszd,
            sd.dept_name, sa.airport_name, saa.area_name, sag.group_name, sat.team_name
        from sams_employee se
        left join sys_dept sd on se.dept_id = sd.dept_id
        left join sams_airport sa on se.airport_id = sa.airport_id
        left join sams_airport_area saa on se.area_id = saa.area_id
        left join sams_airport_group sag on se.group_id = sag.group_id
        left join sams_airport_team sat on se.team_id = sat.team_id
        <where>
            <if test="deptId != null "> and se.dept_id = #{deptId}</if>
            <if test="airportId != null "> and se.airport_id = #{airportId}</if>
            <if test="areaId != null "> and se.area_id = #{areaId}</if>
            <if test="groupId != null "> and se.group_id = #{groupId}</if>
            <if test="teamId != null "> and se.team_id = #{teamId}</if>
            <if test="empNo != null  and empNo != ''"> and se.emp_no = #{empNo}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and se.mobile_phone = #{mobilePhone}</if>
            <if test="name != null  and name != ''"> and se.name like concat('%', #{name}, '%')</if>
            <if test="namePinyin != null  and namePinyin != ''"> and se.name_pinyin = #{namePinyin}</if>
            <if test="nameJianpin != null  and nameJianpin != ''"> and se.name_jianpin = #{nameJianpin}</if>
            <if test="sex != null "> and se.sex = #{sex}</if>
            <if test="nation != null  and nation != ''"> and se.nation = #{nation}</if>
            <if test="typeName != null "> and se.type_name =#{typeName}</if>
            <if test="cardType != null "> and se.card_type = #{cardType}</if>
            <if test="cardId != null  and cardId != ''"> and se.card_id = #{cardId}</if>
            <if test="domicileType != null "> and se.domicile_type = #{domicileType}</if>
            <if test="polotocalStatus != null "> and se.polotocal_status = #{polotocalStatus}</if>
            <if test="healthStatus != null  and healthStatus != ''"> and se.health_status = #{healthStatus}</if>
            <if test="maritalStatus != null  and maritalStatus != ''"> and se.marital_status = #{maritalStatus}</if>
            <if test="education != null "> and se.education = #{education}</if>
            <if test="major != null  and major != ''"> and se.major = #{major}</if>
            <if test="school != null  and school != ''"> and se.school = #{school}</if>
            <if test="educationImg != null  and educationImg != ''"> and se.education_img = #{educationImg}</if>
            <if test="degree != null  and degree != ''"> and se.degree = #{degree}</if>
            <if test="talent != null  and talent != ''"> and se.talent = #{talent}</if>
            <if test="postName != null  and postName != ''"> and se.post_name like concat('%', #{postName}, '%')</if>
            <if test="cjgzDate != null "> and se.cjgz_date = #{cjgzDate}</if>
            <if test="emContact != null  and emContact != ''"> and se.em_contact = #{emContact}</if>
            <if test="emContactPhone != null  and emContactPhone != ''"> and se.em_contact_phone = #{emContactPhone}</if>
            <if test="ruzhiDate != null "> and se.ruzhi_date = #{ruzhiDate}</if>
            <if test="lizhiDate != null "> and se.lizhi_date = #{lizhiDate}</if>
            <if test="gsYear != null  and gsYear != ''"> and se.gs_year = #{gsYear}</if>
            <if test="htComplateDate != null "> and se.ht_complate_date = #{htComplateDate}</if>
            <if test="tuixiuDate != null "> and se.tuixiu_date = #{tuixiuDate}</if>
            <if test="bindPhone != null  and bindPhone != ''"> and se.bind_phone = #{bindPhone}</if>
            <if test="openId != null  and openId != ''"> and se.open_id = #{openId}</if>
            <if test="nicheng != null  and nicheng != ''"> and se.nicheng = #{nicheng}</if>
            <if test="avate != null  and avate != ''"> and se.avate = #{avate}</if>
            <if test="status != null  and status != ''"> and se.status = #{status}</if>
        </where>
    </select>
</mapper>
