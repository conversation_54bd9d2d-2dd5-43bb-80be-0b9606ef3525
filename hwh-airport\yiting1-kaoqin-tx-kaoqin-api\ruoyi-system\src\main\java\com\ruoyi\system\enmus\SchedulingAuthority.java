package com.ruoyi.system.enmus;

import java.util.Objects;

/**
 * 排班权限
 *
 * <AUTHOR>
 */
public enum SchedulingAuthority
{
    NOT_HAVE(0, "无"),
    HAVE(1, "有");

    private final Integer code;
    private final String info;

    SchedulingAuthority(Integer code, String info)
    {
        this.code = code;
        this.info = info;
    }

    public static SchedulingAuthority getSchedulingAuthority(Integer code) {
        for (SchedulingAuthority authority : SchedulingAuthority.values()) {
            if (Objects.equals(authority.getCode(), code)) {
                return authority;
            }
        }
        return null;
    }
    public static SchedulingAuthority getSchedulingAuthority(String info) {
        for (SchedulingAuthority authority : SchedulingAuthority.values()) {
            if (Objects.equals(authority.getInfo(), info)) {
                return authority;
            }
        }
        return null;
    }

    public Integer getCode()
    {
        return code;
    }

    public String getInfo()
    {
        return info;
    }
}
