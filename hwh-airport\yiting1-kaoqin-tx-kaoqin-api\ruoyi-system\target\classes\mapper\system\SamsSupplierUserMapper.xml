<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsSupplierUserMapper">
    
    <resultMap type="SamsSupplierUser" id="SamsSupplierUserResult">
        <result property="supplierUserId"    column="supplier_user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="password"    column="password"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="wechat"    column="wechat"    />
        <result property="supplierId"    column="supplier_id"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSamsSupplierUserVo">
        select supplier_user_id, user_name, nick_name, password, phonenumber, wechat, supplier_id, status, del_flag, create_by, create_time, update_by, update_time, remark from sams_supplier_user
    </sql>

    <select id="selectSamsSupplierUserList" parameterType="SamsSupplierUser" resultMap="SamsSupplierUserResult">
        <include refid="selectSamsSupplierUserVo"/>
        <where>  
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="wechat != null  and wechat != ''"> and wechat = #{wechat}</if>
            <if test="supplierId != null "> and supplier_id = #{supplierId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSamsSupplierUserBySupplierUserId" parameterType="Long" resultMap="SamsSupplierUserResult">
        <include refid="selectSamsSupplierUserVo"/>
        where supplier_user_id = #{supplierUserId}
    </select>
        
    <insert id="insertSamsSupplierUser" parameterType="SamsSupplierUser" useGeneratedKeys="true" keyProperty="supplierUserId">
        insert into sams_supplier_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="phonenumber != null and phonenumber != ''">phonenumber,</if>
            <if test="wechat != null and wechat != ''">wechat,</if>
            <if test="supplierId != null">supplier_id,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="phonenumber != null and phonenumber != ''">#{phonenumber},</if>
            <if test="wechat != null and wechat != ''">#{wechat},</if>
            <if test="supplierId != null">#{supplierId},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsSupplierUser" parameterType="SamsSupplierUser">
        update sams_supplier_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="password != null and password != ''">password = #{password},</if>
            <if test="phonenumber != null and phonenumber != ''">phonenumber = #{phonenumber},</if>
            <if test="wechat != null and wechat != ''">wechat = #{wechat},</if>
            <if test="supplierId != null">supplier_id = #{supplierId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where supplier_user_id = #{supplierUserId}
    </update>

    <delete id="deleteSamsSupplierUserBySupplierUserId" parameterType="Long">
        delete from sams_supplier_user where supplier_user_id = #{supplierUserId}
    </delete>

    <delete id="deleteSamsSupplierUserBySupplierUserIds" parameterType="String">
        delete from sams_supplier_user where supplier_user_id in 
        <foreach item="supplierUserId" collection="array" open="(" separator="," close=")">
            #{supplierUserId}
        </foreach>
    </delete>
</mapper>