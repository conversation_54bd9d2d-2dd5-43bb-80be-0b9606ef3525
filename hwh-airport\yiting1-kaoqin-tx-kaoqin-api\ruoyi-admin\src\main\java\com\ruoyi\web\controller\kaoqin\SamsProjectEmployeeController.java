package com.ruoyi.web.controller.kaoqin;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SamsProjectEmployee;
import com.ruoyi.system.domain.vo.WxSmsVo;
import com.ruoyi.system.mapper.SamsEmployeeMapper;
import com.ruoyi.system.service.ISamsProjectEmployeeService;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 项目员工Controller
 *
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/projectEmployee")
public class SamsProjectEmployeeController extends BaseController {
    @Autowired
    private ISamsProjectEmployeeService samsProjectEmployeeService;
    @Autowired
    private WxMaService wxService;
    @Autowired
    private SamsEmployeeMapper samsEmployeeMapper;

    /**
     * 查询项目员工列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsProjectEmployee samsProjectEmployee) {
        startPage();
        List<SamsProjectEmployee> list = samsProjectEmployeeService.selectSamsProjectEmployeeList(samsProjectEmployee);
        return getDataTable(list);
    }

    /**
     * 排除重复员工
     *
     * @param samsEmployee
     * @return
     */
    @GetMapping("/list2")
    public AjaxResult list2(SamsEmployee samsEmployee) {
        List<SamsEmployee> list = samsEmployeeMapper.selectSamsEmployeeList2(samsEmployee);
        return AjaxResult.success(list);
    }

    /**
     * 排除重复员工
     *
     * @param samsEmployee
     * @return
     */
    @GetMapping("/list3")
    public TableDataInfo list3(SamsEmployee samsEmployee) {
        startPage();
        List<SamsEmployee> list = samsEmployeeMapper.selectSamsEmployeeList2(samsEmployee);
        return getDataTable(list);
    }

    @PostMapping("/sendSms")
    public AjaxResult sendSms(@RequestBody WxSmsVo wxSmsVo) {
        // 验证必填参数
        if (wxSmsVo.getProjectId() == null) {
            return AjaxResult.error("项目ID不能为空");
        }
        if (StringUtils.isEmpty(wxSmsVo.getContent())) {
            return AjaxResult.error("消息内容不能为空");
        }

        SamsProjectEmployee samsProjectEmployee = new SamsProjectEmployee();
        samsProjectEmployee.setProjectId(wxSmsVo.getProjectId()); // 设置项目ID进行筛选
        List<SamsProjectEmployee> list = samsProjectEmployeeService.selectSamsProjectEmployeeList(samsProjectEmployee);

        if (list == null || list.size() == 0) {
            return AjaxResult.error("该项目下没有找到员工，请检查项目是否正确");
        }

        // 使用多线程发送消息
        return sendMessagesInParallel(list, wxSmsVo);
    }

    /**
     * 多线程发送微信订阅消息
     */
    private AjaxResult sendMessagesInParallel(List<SamsProjectEmployee> projectEmployees, WxSmsVo wxSmsVo) {
        // 创建线程池，根据员工数量动态调整线程数，最大不超过20个线程
        int threadCount = Math.min(projectEmployees.size(), Math.min(20, Runtime.getRuntime().availableProcessors() * 2));
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // 使用原子类来统计发送结果，保证线程安全
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 创建CountDownLatch来等待所有任务完成
        CountDownLatch latch = new CountDownLatch(projectEmployees.size());

        // 获取当前用户名，避免在多线程中重复调用
        String currentUsername = getUsername();
        String currentTime = DateUtils.getTime();

        logger.info("开始多线程发送消息，总员工数：{}，线程数：{}", projectEmployees.size(), threadCount);

        // 为每个员工创建发送任务
        for (SamsProjectEmployee projectEmployee : projectEmployees) {
            executor.submit(() -> {
                try {
                    sendSingleMessage(projectEmployee, wxSmsVo, currentUsername, currentTime, successCount, failCount);
                } catch (Exception e) {
                    failCount.incrementAndGet();
                    logger.error("发送消息任务执行异常，员工ID：{}", projectEmployee.getEmployeeId(), e);
                } finally {
                    latch.countDown(); // 任务完成，计数器减1
                }
            });
        }

        try {
            // 等待所有任务完成，最多等待5分钟
            boolean completed = latch.await(5, TimeUnit.MINUTES);
            if (!completed) {
                logger.warn("消息发送超时，部分消息可能未发送完成");
            }
        } catch (InterruptedException e) {
            logger.error("等待消息发送完成时被中断", e);
            Thread.currentThread().interrupt();
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        int finalSuccessCount = successCount.get();
        int finalFailCount = failCount.get();
        String resultMsg = String.format("消息发送完成！成功：%d人，失败：%d人", finalSuccessCount, finalFailCount);
        logger.info("群发消息结果：{}", resultMsg);

        return AjaxResult.success(resultMsg);
    }

    /**
     * 发送单条消息
     */
    private void sendSingleMessage(SamsProjectEmployee projectEmployee, WxSmsVo wxSmsVo,
                                 String username, String time, AtomicInteger successCount, AtomicInteger failCount) {
        SamsEmployee employee = null;
        try {
            employee = samsEmployeeMapper.selectSamsEmployeeByEmployeeId(projectEmployee.getEmployeeId());

            if (employee != null && StringUtils.isNotEmpty(employee.getOpenId())) {
                WxMaSubscribeMessage message = new WxMaSubscribeMessage();
                message.setTemplateId("4ArO_nvbK7h7rmgXFcdN7ml-h_pOmhmNzHSPpH8XGPw");

                List<WxMaSubscribeMessage.MsgData> data = new ArrayList<>();
                data.add(new WxMaSubscribeMessage.MsgData("time2", time));
                data.add(new WxMaSubscribeMessage.MsgData("thing3", wxSmsVo.getContent()));
                data.add(new WxMaSubscribeMessage.MsgData("thing7", username));

                message.setData(data);
                message.setToUser(employee.getOpenId());

                // 发送消息
                wxService.getSubscribeService().sendSubscribeMsg(message);
                successCount.incrementAndGet();
                logger.info("消息发送成功，员工：{}, OpenId：{}", employee.getName(), employee.getOpenId());

                // 添加短暂延迟，避免频率过高
                Thread.sleep(50);

            } else {
                failCount.incrementAndGet();
                logger.warn("员工{}没有绑定微信或OpenId为空，跳过发送", employee != null ? employee.getName() : "未知");
            }
        } catch (Exception e) {
            failCount.incrementAndGet();
            logger.error("给员工{}发送消息失败", employee != null ? employee.getName() : "未知", e);
        }
    }

    /**
     * 导出项目员工列表
     */
    @Log(title = "项目员工", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsProjectEmployee samsProjectEmployee) {
        List<SamsProjectEmployee> list = samsProjectEmployeeService.selectSamsProjectEmployeeExportList(samsProjectEmployee);
        ExcelUtil<SamsProjectEmployee> util = new ExcelUtil<SamsProjectEmployee>(SamsProjectEmployee.class);
        util.exportExcel(response, list, "项目员工数据");
    }

    /**
     * 获取项目员工详细信息
     */
    @GetMapping(value = "/{projectEmployeeId}")
    public AjaxResult getInfo(@PathVariable("projectEmployeeId") Long projectEmployeeId) {
        return success(samsProjectEmployeeService.selectSamsProjectEmployeeByProjectEmployeeId(projectEmployeeId));
    }

    /**
     * 新增项目员工
     */
    @Log(title = "项目员工", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsProjectEmployee samsProjectEmployee) {
        return toAjax(samsProjectEmployeeService.insertSamsProjectEmployee(samsProjectEmployee));
    }

    /**
     * 修改项目员工
     */
    @Log(title = "项目员工", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsProjectEmployee samsProjectEmployee) {
        return toAjax(samsProjectEmployeeService.updateSamsProjectEmployee(samsProjectEmployee));
    }

    /**
     * 删除项目员工
     */
    @Log(title = "项目员工", businessType = BusinessType.DELETE)
    @DeleteMapping("/{projectEmployeeIds}")
    public AjaxResult remove(@PathVariable Long[] projectEmployeeIds) {
        return toAjax(samsProjectEmployeeService.deleteSamsProjectEmployeeByProjectEmployeeIds(projectEmployeeIds));
    }
}
