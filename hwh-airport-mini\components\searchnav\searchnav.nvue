<template>
	<view class="example-body">
		<uni-nav-bar :fixed="false" color="#333333" background-color="#FFFFFF" right-icon="scan" @clickLeft="showCity" @clickRight="scan">
			<view class="input-view">
				<image src="../../static/icon/sousuo_icon.png" style="width: 30rpx;height: 28rpx;position: absolute;left: 252rpx;top: 70rpx;" mode=""></image>
				<text style="position: absolute;left: 300rpx;top: 54rpx;color: #ccc;">|</text>
				<input confirm-type="search" class="nav-bar-input" type="text" placeholder="搜您想搜" @confirm="confirm">
			</view>
		</uni-nav-bar>
	</view>
</template>

<script>
	export default {
		name: 'navbar',
		data() {
			return {
				city: '北京'
			}
		},
		methods: {
			back() {
				uni.navigateBack({
					delta: 1
				})
			},
			showMenu() {
				uni.showToast({
					title: '菜单'
				})
			},
			clickLeft() {
		
				uni.showToast({
					title: '左侧按钮'
				})
			},
			search() {
				uni.showToast({
					title: '搜索'
				})
			},
			showCity() {
		
				uni.showToast({
					title: '选择城市'
				})
			},
			scan() {
				uni.showToast({
					title: '扫码'
				})
			},
			confirm() {
				uni.showToast({
					title: '搜索'
				})
			}
		},
		onPullDownRefresh() {
			console.log('onPullDownRefresh')
			setTimeout(function() {
				uni.stopPullDownRefresh()
				console.log('stopPullDownRefresh')
			}, 1000)
		}
	}
</script>

<style>
/* 头条小程序组件内不能引入字体 */
	/* #ifdef MP-TOUTIAO */
/* 	@font-face {
		font-family: uniicons;
		font-weight: normal;
		font-style: normal;
		src: url('~@/static/uni.ttf') format('truetype');
	} */

	/* #endif */

	/* #ifndef APP-NVUE */
	page {
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		background-color: #efeff4;
		min-height: 100%;
		height: auto;
	}

	view {
		font-size: 14px;
		line-height: inherit;
	}

	.example {
		padding: 0 15px 15px;
	}

	.example-info {
		padding: 15px;
		color: #3b4144;
		background: #ffffff;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		/* display: flex; */
		/* #endif */
		padding: 0;
		font-size: 28rpx;
		background-color: #ffffff;
	}
     
	 .bg {
		 width: 100%;
         position: absolute;
		 height: 112rpx;
		 z-index:1;
	 }
	 
	/* #endif */
	.example {
		padding: 0 30rpx;
	}

	.example-info {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 30rpx;
		color: #3b4144;
		background-color: #ffffff;
		font-size: 28rpx;
		line-height: 40rpx;
	}

	.example-info-text {
		font-size: 28rpx;
		line-height: 40rpx;
		color: #3b4144;
	}


	.example-body {
		flex-direction: column;
		padding: 30rpx;
		background-color: #ffffff;
		height: 112rpx;
		width: 100%;
		overflow: hidden;
		position: fixed;
		top: 0;
		z-index:9999;
	}

	.word-btn-white {
		font-size: 18px;
		color: #FFFFFF;
	}

	.word-btn {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		border-radius: 6px;
		height: 48px;
		margin: 15px;
		background-color: #007AFF;
	}

	.word-btn--hover {
		background-color: #4ca2ff;
	}

	.uni-nav-bar-text {
		font-size: 14px;
	}

	.city {
		/* #ifndef APP-PLUS-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		/* width: 160rpx;
 */
		margin-left: 4px;
	}

	.input-view {
		/* #ifndef APP-PLUS-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		flex: 1;
		height: 56px;
		border-radius: 15px;
		padding: 30rpx 0;
		flex-wrap: nowrap;
		line-height: 30px;
		position: absolute;
         z-index:999;		
	}

	.input-uni-icon {
		line-height: 60rpx;
	}

	.nav-bar-input {
		height: 56rpx;
		line-height: 60rpx;
		padding: 0 10rpx;
		font-size: 28rpx;
		background-color: #fff;
		border-radius: 40rpx;
		padding-left: 90rpx;
		margin-left: 20rpx;
		width: 388rpx;
		box-sizing: border-box;
		border: 1rpx solid #ccc;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 0;
	}
</style>
