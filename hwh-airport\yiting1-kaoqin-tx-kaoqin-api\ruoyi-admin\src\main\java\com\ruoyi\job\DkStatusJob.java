package com.ruoyi.job;

import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SamsPostClock;
import com.ruoyi.system.domain.SamsSchedulingEmployee;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxQueryVo;
import com.ruoyi.system.domain.vo.SamsSchedulingPostEmployeeVo;
import com.ruoyi.system.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import static com.ruoyi.common.utils.DateUtils.getTime4fenzhong;

/**
 * 考勤分析
 */
@Slf4j
@Component
public class DkStatusJob {
    @Autowired
    private SamsSchedulingEmployeeMapper samsSchedulingEmployeeMapper;

    /**
     * 凌晨12.01点检查一下考勤数据
     */
    @Scheduled(cron = "0 0 1 * * ?")
//    @Scheduled(fixedRate = 5)
    public void kqsjJobTasks() {
        log.info("凌晨12点01检查一下考勤数据####开始#####");
        // 查询前一天所有人员、岗位、排班任务
        List<SamsSchedulingPostEmployeeVo> employeeVos = samsSchedulingEmployeeMapper.selectYestDayKqsj2Day();

        if (!CollectionUtils.isEmpty(employeeVos)) {
            for (SamsSchedulingPostEmployeeVo samsSchedulingPostEmployeeVo : employeeVos) {
                List<SamsSchedulingEmployee> schedulingEmployeeList = samsSchedulingEmployeeMapper.selectByEmployeeId2Day(samsSchedulingPostEmployeeVo.getEmployeeId(),
                        samsSchedulingPostEmployeeVo.getPostId(), samsSchedulingPostEmployeeVo.getSchedulingId());
                if (schedulingEmployeeList != null && !schedulingEmployeeList.isEmpty()) {
                    if (schedulingEmployeeList.size() == 1) {
                        // 判断是上班，则直接跳过
                        if (schedulingEmployeeList.get(0).getDkType().equals(1) || StringUtils.isBlank(schedulingEmployeeList.get(0).getDkTag())) {
                            continue;
                        }
                        // 如果是下班，需要查询对应的上班进行统计
                        List<SamsSchedulingEmployee> samsSchedulingEmployees = samsSchedulingEmployeeMapper.selectByDkTag(schedulingEmployeeList.get(0).getDkTag());
                        if (null == samsSchedulingEmployees || samsSchedulingEmployees.size() != 2) {
                            continue;
                        }
                        schedulingEmployeeList = samsSchedulingEmployees;
                    }

                    schedulingEmployeeList.forEach(schedulingEmployee -> {
                        if (schedulingEmployee.getDkStatus() == null) {
                            // 判断打卡结束时间是否在当前时间后
                            String dutyDate = DateUtils.parseDateToStr("yyyy-MM-dd", schedulingEmployee.getDutyDate());
                            if (schedulingEmployee.getDkType() == 1) {

                                if (StringUtils.isBlank(schedulingEmployee.getStartTimeAfter())){
                                    schedulingEmployee.setStartTimeAfter("00:00");
                                }
                                // 计算结束时间
                                String dkAfterTime = dkTimeDeal(dutyDate + " " + schedulingEmployee.getStartTime(), schedulingEmployee.getStartTimeAfter());
                                // 判断当前时间是否在打卡结束时间后
                                if (DateUtils.getTime4fenzhong(DateUtils.parseDate(dkAfterTime), new Date()) > 0) {
                                    schedulingEmployee.setDkStatus(6);
                                }
                            } else {
                                if (StringUtils.isBlank(schedulingEmployee.getEndTimeAfter())){
                                    schedulingEmployee.setEndTimeAfter("00:00");
                                }
                                // 修复跨天问题：处理下班时间跨天的情况
                                String endDutyDate = dutyDate;
                                if (schedulingEmployee.getNextDay() == 1) {
                                    endDutyDate = DateUtils.parseDateToStr("yyyy-MM-dd",
                                        org.apache.commons.lang3.time.DateUtils.addDays(schedulingEmployee.getDutyDate(), 1));
                                }
                                // 计算结束时间
                                String dkAfterTime = dkTimeDeal(endDutyDate + " " + schedulingEmployee.getEndTime(), schedulingEmployee.getEndTimeAfter());
                                // 判断当前时间是否在打卡结束时间后
                                if (DateUtils.getTime4fenzhong(DateUtils.parseDate(dkAfterTime), new Date()) > 0) {
                                    schedulingEmployee.setDkStatus(7);
                                }
                            }
                        }

                        schedulingEmployee.setUpdateTime(new Date());
                        samsSchedulingEmployeeMapper.updateSamsSchedulingEmployee(schedulingEmployee);
                    });
                }
            }

        }
        log.info("凌晨12点01检查一下考勤数据#####结束####");
    }


    public String dkTimeDeal(String dkTime, String dkTimeAfter) {
        log.info("打卡时间：{}，延长时间：{}", dkTime, dkTimeAfter);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        LocalDateTime punchTime = LocalDateTime.parse(dkTime, formatter);
        // 解析延长时间
        String[] delayParts = dkTimeAfter.split(":");
        int hoursToAdd = Integer.parseInt(delayParts[0]); // 获取小时
        int minutesToAdd = Integer.parseInt(delayParts[1]); // 获取分钟

        // 计算结束时间
        LocalDateTime endTime = punchTime.plusHours(hoursToAdd).plusMinutes(minutesToAdd);

        // 输出结果
        System.out.println("打卡结束时间: " + endTime.format(formatter));

        return endTime.format(formatter);
    }
}
