<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsPostClockMapper">

    <resultMap type="SamsPostClock" id="SamsPostClockResult">
        <result property="postClockId"    column="post_clock_id"    />
        <result property="postId"    column="post_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="startTimeBefore"    column="start_time_before"    />
        <result property="startTimeAfter"    column="start_time_after"    />
        <result property="endTime"    column="end_time"    />
        <result property="endTimeBefore"    column="end_time_before"    />
        <result property="endTimeAfter"    column="end_time_after"    />
        <result property="nextDay"    column="next_day"    />
        <result property="clockAddress"    column="clock_address"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="clockRange"    column="clock_range"    />
        <result property="workRange"    column="work_range"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="clockName"    column="clock_name"    />
        <result property="ddmm"    column="ddmm"    />
        <result property="isAnytimeCheckIn"    column="is_anytime_check_in"    />

    </resultMap>

    <sql id="selectSamsPostClockVo">
        select post_clock_id, post_id,clock_name,ddmm, start_time, start_time_before, start_time_after, end_time,
               end_time_before, end_time_after, next_day, clock_address, latitude, longitude, clock_range, work_range, del_flag,
               create_by, create_time, update_by, update_time, remark, is_anytime_check_in from sams_post_clock
    </sql>

    <select id="selectSamsPostClockList" parameterType="SamsPostClock" resultMap="SamsPostClockResult">
        <include refid="selectSamsPostClockVo"/>
        <where>
            del_flag = 0
            <if test="postId != null "> and post_id = #{postId}</if>
            <if test="startTime != null  and startTime != ''"> and start_time = #{startTime}</if>
            <if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
            <if test="clockAddress != null  and clockAddress != ''"> and clock_address = #{clockAddress}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="clockRange != null "> and clock_range = #{clockRange}</if>
            <if test="workRange != null "> and work_range = #{workRange}</if>
            <if test="clockName != null  and clockName != ''"> and clock_name = #{clockName}</if>
        </where>
    </select>

    <select id="selectSamsPostClockByPostClockId" parameterType="Long" resultMap="SamsPostClockResult">
        <include refid="selectSamsPostClockVo"/>
        where post_clock_id = #{postClockId}
    </select>

    <insert id="insertSamsPostClock" parameterType="SamsPostClock" useGeneratedKeys="true" keyProperty="postClockId">
        insert into sams_post_clock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="postId != null">post_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="startTimeBefore != null">start_time_before,</if>
            <if test="startTimeAfter != null">start_time_after,</if>
            <if test="endTime != null">end_time,</if>
            <if test="endTimeBefore != null">end_time_before,</if>
            <if test="endTimeAfter != null">end_time_after,</if>
            <if test="nextDay != null">next_day,</if>
            <if test="clockAddress != null">clock_address,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="clockRange != null">clock_range,</if>
            <if test="workRange != null">work_range,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="clockName != null">clock_name,</if>
            <if test="ddmm != null">ddmm,</if>
            <if test="isAnytimeCheckIn != null">is_anytime_check_in,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="postId != null">#{postId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="startTimeBefore != null">#{startTimeBefore},</if>
            <if test="startTimeAfter != null">#{startTimeAfter},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="endTimeBefore != null">#{endTimeBefore},</if>
            <if test="endTimeAfter != null">#{endTimeAfter},</if>
            <if test="nextDay != null">#{nextDay},</if>
            <if test="clockAddress != null">#{clockAddress},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="clockRange != null">#{clockRange},</if>
            <if test="workRange != null">#{workRange},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="clockName != null">#{clockName},</if>
            <if test="ddmm != null">#{ddmm},</if>
            <if test="isAnytimeCheckIn != null">#{isAnytimeCheckIn},</if>
         </trim>
    </insert>

    <update id="updateSamsPostClock" parameterType="SamsPostClock">
        update sams_post_clock
        <trim prefix="SET" suffixOverrides=",">
            <if test="postId != null">post_id = #{postId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="startTimeBefore != null">start_time_before = #{startTimeBefore},</if>
            <if test="startTimeAfter != null">start_time_after = #{startTimeAfter},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="endTimeBefore != null">end_time_before = #{endTimeBefore},</if>
            <if test="endTimeAfter != null">end_time_after = #{endTimeAfter},</if>
            <if test="nextDay != null">next_day = #{nextDay},</if>
            <if test="clockAddress != null">clock_address = #{clockAddress},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="clockRange != null">clock_range = #{clockRange},</if>
            <if test="workRange != null">work_range = #{workRange},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="clockName != null">clock_name = #{clockName},</if>
            <if test="ddmm != null">ddmm = #{ddmm},</if>
            <if test="isAnytimeCheckIn != null">is_anytime_check_in = #{isAnytimeCheckIn},</if>
        </trim>
        where post_clock_id = #{postClockId}
    </update>

    <delete id="deleteSamsPostClockByPostClockId" parameterType="Long">
        delete from sams_post_clock where post_clock_id = #{postClockId}
    </delete>

    <delete id="deleteByPostId" parameterType="Long">
        delete from sams_post_clock where post_id = #{postId}
    </delete>


    <delete id="deleteSamsPostClockByPostClockIds" parameterType="String">
        delete from sams_post_clock where post_clock_id in
        <foreach item="postClockId" collection="array" open="(" separator="," close=")">
            #{postClockId}
        </foreach>
    </delete>

    <update id="updateByPostId">
        update sams_post_clock set del_flag = 2 where post_id = #{postId}
    </update>
</mapper>
