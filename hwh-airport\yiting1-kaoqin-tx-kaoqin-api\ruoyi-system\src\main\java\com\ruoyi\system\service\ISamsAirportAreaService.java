package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsAirportArea;

/**
 * 机场区域信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface ISamsAirportAreaService 
{
    /**
     * 查询机场区域信息
     * 
     * @param areaId 机场区域信息主键
     * @return 机场区域信息
     */
    public SamsAirportArea selectSamsAirportAreaByAreaId(Long areaId);

    /**
     * 查询机场区域信息列表
     * 
     * @param samsAirportArea 机场区域信息
     * @return 机场区域信息集合
     */
    public List<SamsAirportArea> selectSamsAirportAreaList(SamsAirportArea samsAirportArea);

    /**
     * 新增机场区域信息
     * 
     * @param samsAirportArea 机场区域信息
     * @return 结果
     */
    public int insertSamsAirportArea(SamsAirportArea samsAirportArea);

    /**
     * 修改机场区域信息
     * 
     * @param samsAirportArea 机场区域信息
     * @return 结果
     */
    public int updateSamsAirportArea(SamsAirportArea samsAirportArea);

    /**
     * 批量删除机场区域信息
     * 
     * @param areaIds 需要删除的机场区域信息主键集合
     * @return 结果
     */
    public int deleteSamsAirportAreaByAreaIds(Long[] areaIds);

    /**
     * 删除机场区域信息信息
     * 
     * @param areaId 机场区域信息主键
     * @return 结果
     */
    public int deleteSamsAirportAreaByAreaId(Long areaId);
}
