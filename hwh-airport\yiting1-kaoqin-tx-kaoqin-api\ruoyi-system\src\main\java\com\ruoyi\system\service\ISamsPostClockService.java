package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SamsPostClock;

/**
 * 岗位打卡信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
public interface ISamsPostClockService 
{
    /**
     * 查询岗位打卡信息
     * 
     * @param postClockId 岗位打卡信息主键
     * @return 岗位打卡信息
     */
    public SamsPostClock selectSamsPostClockByPostClockId(Long postClockId);

    /**
     * 查询岗位打卡信息列表
     * 
     * @param samsPostClock 岗位打卡信息
     * @return 岗位打卡信息集合
     */
    public List<SamsPostClock> selectSamsPostClockList(SamsPostClock samsPostClock);

    /**
     * 新增岗位打卡信息
     * 
     * @param samsPostClock 岗位打卡信息
     * @return 结果
     */
    public int insertSamsPostClock(SamsPostClock samsPostClock);

    /**
     * 修改岗位打卡信息
     * 
     * @param samsPostClock 岗位打卡信息
     * @return 结果
     */
    public int updateSamsPostClock(SamsPostClock samsPostClock);

    /**
     * 批量删除岗位打卡信息
     * 
     * @param postClockIds 需要删除的岗位打卡信息主键集合
     * @return 结果
     */
    public int deleteSamsPostClockByPostClockIds(Long[] postClockIds);

    /**
     * 删除岗位打卡信息信息
     * 
     * @param postClockId 岗位打卡信息主键
     * @return 结果
     */
    public int deleteSamsPostClockByPostClockId(Long postClockId);
}
