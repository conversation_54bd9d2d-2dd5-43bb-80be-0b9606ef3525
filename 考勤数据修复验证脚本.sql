-- 考勤数据修复验证脚本
-- 使用说明：修复前后分别执行此脚本，对比结果验证修复效果

-- 1. 备份当前考勤分析数据（修复前执行）
-- CREATE TABLE sams_scheduling_kqfx_backup_20241219 AS 
-- SELECT * FROM sams_scheduling_kqfx WHERE cxny = '2024-12';

-- 2. 统计概览 - 按月份汇总
SELECT 
    cxny AS '年月',
    COUNT(*) AS '记录总数',
    SUM(bctj) AS '总班次',
    SUM(gzsc) AS '总工作时长(分钟)',
    SUM(jbcs) AS '总加班次数',
    SUM(jbsc) AS '总加班时长(分钟)',
    SUM(cdcs) AS '总迟到次数',
    SUM(kgcs) AS '总旷工次数',
    SUM(ztcs) AS '总早退次数'
FROM sams_scheduling_kqfx 
WHERE cxny >= '2024-01'  -- 修改为需要验证的时间范围
GROUP BY cxny 
ORDER BY cxny;

-- 3. 异常数据检查
-- 3.1 检查是否有负数或异常大的数值
SELECT 
    '负数检查' AS '检查项',
    COUNT(*) AS '异常记录数'
FROM sams_scheduling_kqfx 
WHERE cxny >= '2024-01'
  AND (bctj < 0 OR gzsc < 0 OR jbcs < 0 OR jbsc < 0 OR cdcs < 0 OR kgcs < 0 OR ztcs < 0)

UNION ALL

SELECT 
    '异常大数值检查' AS '检查项',
    COUNT(*) AS '异常记录数'
FROM sams_scheduling_kqfx 
WHERE cxny >= '2024-01'
  AND (bctj > 100 OR gzsc > 50000 OR jbcs > 100 OR jbsc > 50000 OR cdcs > 100 OR kgcs > 100 OR ztcs > 100);

-- 4. 数据一致性检查
-- 4.1 检查工作时长与班次的合理性（平均每班次工作时长应在合理范围内）
SELECT 
    cxny AS '年月',
    AVG(CASE WHEN bctj > 0 THEN gzsc / bctj ELSE 0 END) AS '平均每班次工作时长(分钟)',
    AVG(CASE WHEN jbcs > 0 THEN jbsc / jbcs ELSE 0 END) AS '平均每次加班时长(分钟)'
FROM sams_scheduling_kqfx 
WHERE cxny >= '2024-01'
  AND (bctj > 0 OR jbcs > 0)
GROUP BY cxny 
ORDER BY cxny;

-- 5. 员工维度统计（抽样检查）
-- 随机选择几个员工查看详细统计
SELECT 
    se.name AS '员工姓名',
    se.card_id AS '身份证号',
    sk.cxny AS '年月',
    sp.post_name AS '岗位',
    sk.bctj AS '班次',
    sk.gzsc AS '工作时长',
    sk.jbcs AS '加班次数',
    sk.jbsc AS '加班时长',
    sk.cdcs AS '迟到次数',
    sk.kgcs AS '旷工次数',
    sk.ztcs AS '早退次数'
FROM sams_scheduling_kqfx sk
JOIN sams_employee se ON sk.employee_id = se.employee_id
LEFT JOIN sams_post sp ON sk.post_id = sp.post_id
WHERE sk.cxny = '2024-12'  -- 修改为需要检查的月份
ORDER BY RAND() 
LIMIT 10;

-- 6. 跨天班次验证
-- 检查是否正确处理了跨天班次
SELECT 
    '跨天班次统计' AS '检查项',
    COUNT(DISTINCT sse.dk_tag) AS '跨天班次数量'
FROM sams_scheduling_employee sse
WHERE sse.next_day = 1 
  AND DATE_FORMAT(sse.duty_date, '%Y-%m') = '2024-12'  -- 修改为需要检查的月份
  AND sse.dk_tag IS NOT NULL;

-- 7. 打卡状态分布检查
SELECT 
    '打卡状态分布' AS '检查类型',
    CASE 
        WHEN dk_status = 1 THEN '正常'
        WHEN dk_status = 2 THEN '异常'
        WHEN dk_status = 3 THEN '迟到'
        WHEN dk_status = 5 THEN '早退'
        WHEN dk_status = 6 THEN '上班未打卡'
        WHEN dk_status = 7 THEN '下班未打卡'
        ELSE '其他'
    END AS '状态',
    COUNT(*) AS '数量'
FROM sams_scheduling_employee 
WHERE DATE_FORMAT(duty_date, '%Y-%m') = '2024-12'  -- 修改为需要检查的月份
GROUP BY dk_status
ORDER BY dk_status;

-- 8. 修复前后对比（修复后执行）
-- 注意：需要先执行备份，修复后才能运行此查询
/*
SELECT 
    '修复对比' AS '对比项',
    '修复前' AS '时期',
    COUNT(*) AS '记录数',
    SUM(bctj) AS '总班次',
    SUM(gzsc) AS '总工作时长',
    SUM(kgcs) AS '总旷工次数'
FROM sams_scheduling_kqfx_backup_20241219
WHERE cxny = '2024-12'

UNION ALL

SELECT 
    '修复对比' AS '对比项',
    '修复后' AS '时期',
    COUNT(*) AS '记录数',
    SUM(bctj) AS '总班次',
    SUM(gzsc) AS '总工作时长',
    SUM(kgcs) AS '总旷工次数'
FROM sams_scheduling_kqfx
WHERE cxny = '2024-12';
*/

-- 9. 具体案例验证
-- 选择一个具体的员工和日期，查看原始打卡数据和统计结果
SELECT 
    '原始打卡数据' AS '数据类型',
    sse.employee_id AS '员工ID',
    sse.duty_date AS '值班日期',
    sse.dk_type AS '打卡类型',
    sse.dk_status AS '打卡状态',
    sse.dk_date AS '打卡时间',
    sse.start_time AS '计划上班时间',
    sse.end_time AS '计划下班时间',
    sse.next_day AS '是否跨天',
    sse.dk_tag AS '打卡标识'
FROM sams_scheduling_employee sse
WHERE sse.employee_id = 1  -- 修改为具体员工ID
  AND DATE_FORMAT(sse.duty_date, '%Y-%m') = '2024-12'  -- 修改为需要检查的月份
ORDER BY sse.duty_date, sse.dk_type;

-- 10. 数据完整性检查
-- 检查是否有遗漏的统计数据
SELECT 
    '数据完整性检查' AS '检查项',
    COUNT(DISTINCT CONCAT(sse.employee_id, '-', ss.post_id, '-', DATE_FORMAT(sse.duty_date, '%Y-%m'))) AS '应有统计记录数',
    (SELECT COUNT(*) FROM sams_scheduling_kqfx WHERE cxny = '2024-12') AS '实际统计记录数'
FROM sams_scheduling_employee sse
JOIN sams_scheduling ss ON sse.scheduling_id = ss.scheduling_id
WHERE DATE_FORMAT(sse.duty_date, '%Y-%m') = '2024-12';  -- 修改为需要检查的月份
