<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SamsProjectEmployeeMapper">

    <resultMap type="SamsProjectEmployee" id="SamsProjectEmployeeResult">
        <result property="projectEmployeeId"    column="project_employee_id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="name"    column="name"    />
        <result property="nicheng"    column="nicheng"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap type="SamsProjectEmployee" id="SamsProjectEmployeeExportResult" extends="SamsProjectEmployeeResult">
        <result property="projectName"    column="project_name"    />
    </resultMap>

    <sql id="selectSamsProjectEmployeeVo">
        select project_employee_id,project_id, employee_id, name, nicheng, mobile_phone, status, del_flag, create_by, create_time, update_by, update_time, remark from sams_project_employee
    </sql>

    <select id="selectSamsProjectEmployeeList" parameterType="SamsProjectEmployee" resultMap="SamsProjectEmployeeResult">
        <include refid="selectSamsProjectEmployeeVo"/>
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="nicheng != null  and nicheng != ''"> and nicheng = #{nicheng}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>

            <if test="deptId != null">
                and project_id in (select project_id from sams_project where dept_id = #{deptId}
                <if test="airportId != null">
                    and airport_id = #{airportId}
                </if>
                <if test="areaId != null">
                    and area_id = #{areaId}
                </if>
                )
            </if>
        </where>
    </select>

    <select id="selectSamsProjectEmployeeByProjectEmployeeId" parameterType="Long" resultMap="SamsProjectEmployeeResult">
        <include refid="selectSamsProjectEmployeeVo"/>
        where project_employee_id = #{projectEmployeeId}
    </select>

    <select id="selectByEmployeeIdAndProjectId" parameterType="Long" resultMap="SamsProjectEmployeeResult">
        select * from sams_project_employee where employee_id = #{employeeId} and project_id = #{projectId} limit 1
    </select>


    <insert id="insertSamsProjectEmployee" parameterType="SamsProjectEmployee" useGeneratedKeys="true" keyProperty="projectEmployeeId">
        insert into sams_project_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectId != null">project_id,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="name != null">name,</if>
            <if test="nicheng != null">nicheng,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectId != null">#{projectId},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="name != null">#{name},</if>
            <if test="nicheng != null">#{nicheng},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSamsProjectEmployee" parameterType="SamsProjectEmployee">
        update sams_project_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="nicheng != null">nicheng = #{nicheng},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where project_employee_id = #{projectEmployeeId}
    </update>

    <delete id="deleteSamsProjectEmployeeByProjectEmployeeId" parameterType="Long">
        delete from sams_project_employee where project_employee_id = #{projectEmployeeId}
    </delete>

    <delete id="deleteSamsProjectEmployeeByProjectEmployeeIds" parameterType="String">
        delete from sams_project_employee where project_employee_id in
        <foreach item="projectEmployeeId" collection="array" open="(" separator="," close=")">
            #{projectEmployeeId}
        </foreach>
    </delete>

    <select id="selectSamsProjectEmployeeExportVoList" parameterType="SamsProjectEmployee" resultMap="SamsProjectEmployeeResult">
        <include refid="selectSamsProjectEmployeeVo"/>
        <where>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="nicheng != null  and nicheng != ''"> and nicheng = #{nicheng}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>

            <if test="deptId != null">
                and project_id in (select project_id from sams_project where dept_id = #{deptId}
                <if test="airportId != null">
                    and airport_id = #{airportId}
                </if>
                <if test="areaId != null">
                    and area_id = #{areaId}
                </if>
                )
            </if>
        </where>
    </select>
    <select id="selectSamsProjectEmployeeExportList" parameterType="SamsProjectEmployee" resultMap="SamsProjectEmployeeExportResult">
        SELECT
            spe.project_employee_id,
            spe.project_id,
            spe.employee_id,
            spe.NAME,
            spe.nicheng,
            spe.mobile_phone,
            spe.STATUS,
            spe.del_flag,
            spe.create_by,
            spe.create_time,
            spe.update_by,
            spe.update_time,
            spe.remark,
            sp.project_name
        FROM
        sams_project_employee spe
        left join sams_project sp on spe.project_id = sp.project_id
        <where>
            <if test="projectId != null "> and spe.project_id = #{projectId}</if>
            <if test="employeeId != null "> and spe.employee_id = #{employeeId}</if>
            <if test="name != null  and name != ''"> and spe.name like concat('%', #{name}, '%')</if>
            <if test="nicheng != null  and nicheng != ''"> and spe.nicheng = #{nicheng}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and spe.mobile_phone = #{mobilePhone}</if>
            <if test="status != null  and status != ''"> and spe.status = #{status}</if>

            <if test="deptId != null">
                and spe.project_id in (select project_id from sams_project where dept_id = #{deptId}
                <if test="airportId != null">
                    and airport_id = #{airportId}
                </if>
                <if test="areaId != null">
                    and area_id = #{areaId}
                </if>
                )
            </if>
        </where>
    </select>
</mapper>
