package com.ruoyi.web.controller.kaoqin;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SamsSupplierUser;
import com.ruoyi.system.service.ISamsSupplierUserService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 供应商用户信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-02
 */
@RestController
@RequestMapping("/system/supplierUser")
public class SamsSupplierUserController extends BaseController
{
    @Autowired
    private ISamsSupplierUserService samsSupplierUserService;

    /**
     * 查询供应商用户信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SamsSupplierUser samsSupplierUser)
    {
        startPage();
        List<SamsSupplierUser> list = samsSupplierUserService.selectSamsSupplierUserList(samsSupplierUser);
        return getDataTable(list);
    }

    /**
     * 导出供应商用户信息列表
     */
    @Log(title = "供应商用户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SamsSupplierUser samsSupplierUser)
    {
        List<SamsSupplierUser> list = samsSupplierUserService.selectSamsSupplierUserList(samsSupplierUser);
        ExcelUtil<SamsSupplierUser> util = new ExcelUtil<SamsSupplierUser>(SamsSupplierUser.class);
        util.exportExcel(response, list, "供应商用户信息数据");
    }

    /**
     * 获取供应商用户信息详细信息
     */
    @GetMapping(value = "/{supplierUserId}")
    public AjaxResult getInfo(@PathVariable("supplierUserId") Long supplierUserId)
    {
        return success(samsSupplierUserService.selectSamsSupplierUserBySupplierUserId(supplierUserId));
    }

    /**
     * 新增供应商用户信息
     */
    @Log(title = "供应商用户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SamsSupplierUser samsSupplierUser)
    {
        return toAjax(samsSupplierUserService.insertSamsSupplierUser(samsSupplierUser));
    }

    /**
     * 修改供应商用户信息
     */
    @Log(title = "供应商用户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SamsSupplierUser samsSupplierUser)
    {
        return toAjax(samsSupplierUserService.updateSamsSupplierUser(samsSupplierUser));
    }

    /**
     * 删除供应商用户信息
     */
    @Log(title = "供应商用户信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{supplierUserIds}")
    public AjaxResult remove(@PathVariable Long[] supplierUserIds)
    {
        return toAjax(samsSupplierUserService.deleteSamsSupplierUserBySupplierUserIds(supplierUserIds));
    }
}
