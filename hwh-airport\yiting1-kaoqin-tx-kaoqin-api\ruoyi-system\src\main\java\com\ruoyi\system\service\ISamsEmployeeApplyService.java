package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SamsEmployeeApply;

/**
 * 员工申请信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-03
 */
public interface ISamsEmployeeApplyService 
{
    /**
     * 查询员工申请信息
     * 
     * @param employeeApplyId 员工申请信息主键
     * @return 员工申请信息
     */
    public SamsEmployeeApply selectSamsEmployeeApplyByEmployeeApplyId(Long employeeApplyId);

    /**
     * 查询员工申请信息列表
     * 
     * @param samsEmployeeApply 员工申请信息
     * @return 员工申请信息集合
     */
    public List<SamsEmployeeApply> selectSamsEmployeeApplyList(SamsEmployeeApply samsEmployeeApply);

    /**
     * 新增员工申请信息
     * 
     * @param samsEmployeeApply 员工申请信息
     * @return 结果
     */
    public int insertSamsEmployeeApply(SamsEmployeeApply samsEmployeeApply);

    /**
     * 修改员工申请信息
     * 
     * @param samsEmployeeApply 员工申请信息
     * @return 结果
     */
    public int updateSamsEmployeeApply(SamsEmployeeApply samsEmployeeApply);

    /**
     * 批量删除员工申请信息
     * 
     * @param employeeApplyIds 需要删除的员工申请信息主键集合
     * @return 结果
     */
    public int deleteSamsEmployeeApplyByEmployeeApplyIds(Long[] employeeApplyIds);

    /**
     * 删除员工申请信息信息
     * 
     * @param employeeApplyId 员工申请信息主键
     * @return 结果
     */
    public int deleteSamsEmployeeApplyByEmployeeApplyId(Long employeeApplyId);
}
