package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.common.core.domain.entity.SamsSchedulingKqfx;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxExportVo;
import com.ruoyi.system.domain.vo.SamsSchedulingKqfxQueryVo;

/**
 * 分析考勤报Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public interface SamsSchedulingKqfxMapper
{
    /**
     * 查询分析考勤报
     *
     * @param kqfxId 分析考勤报主键
     * @return 分析考勤报
     */
    public SamsSchedulingKqfx selectSamsSchedulingKqfxByKqfxId(Long kqfxId);

    /**
     * 查询指定大队下面的指定人指定月份是否有分析数据
     * @param queryVo
     * @return
     */
    SamsSchedulingKqfx selectByQueryVo(SamsSchedulingKqfxQueryVo queryVo);
    /**
     * 查询分析考勤报列表
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 分析考勤报集合
     */
    public List<SamsSchedulingKqfx> selectSamsSchedulingKqfxList(SamsSchedulingKqfx samsSchedulingKqfx);

    /**
     * 新增分析考勤报
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 结果
     */
    public int insertSamsSchedulingKqfx(SamsSchedulingKqfx samsSchedulingKqfx);

    /**
     * 修改分析考勤报
     *
     * @param samsSchedulingKqfx 分析考勤报
     * @return 结果
     */
    public int updateSamsSchedulingKqfx(SamsSchedulingKqfx samsSchedulingKqfx);

    /**
     * 删除分析考勤报
     *
     * @param kqfxId 分析考勤报主键
     * @return 结果
     */
    public int deleteSamsSchedulingKqfxByKqfxId(Long kqfxId);

    /**
     * 批量删除分析考勤报
     *
     * @param kqfxIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSamsSchedulingKqfxByKqfxIds(Long[] kqfxIds);

    /**
     * 查询考勤分析明细导出数据
     * @param samsSchedulingKqfx
     * @return
     */
    List<SamsSchedulingKqfxExportVo> selectSamsSchedulingKqfxExportList(SamsSchedulingKqfx samsSchedulingKqfx);

    /**
     * 根据年月删除考勤分析数据
     *
     * @param yearMonth 年月 格式：yyyy-MM
     * @return 结果
     */
    public int deleteByYearMonth(String yearMonth);
}
