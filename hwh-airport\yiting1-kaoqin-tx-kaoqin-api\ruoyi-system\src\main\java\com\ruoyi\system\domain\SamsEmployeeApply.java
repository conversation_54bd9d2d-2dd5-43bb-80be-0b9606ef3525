package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SamsEmployee;
import com.ruoyi.common.core.domain.entity.SysDept;
import lombok.Data;

import java.util.Date;

/**
 * 员工申请信息对象 sams_employee_apply
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Data
public class SamsEmployeeApply extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 员工申请ID
     */
    private Long employeeApplyId;

    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 员工信息ID
     */
    @Excel(name = "员工信息ID")
    private Long employeeId;
    @Excel(name = "岗位ID")
    private Long postId;

    /**
     * 申请类型0 请假，1外出，2出差
     */
    @Excel(name = "申请类型0 请假，1外出，2出差，3迟到说明，4早退说明，5系统异常")
    private Integer applyStype;

    /**
     * 申请理由
     */
    @Excel(name = "申请理由")
    private String applyReson;

    /**
     * 申请图片
     */
    @Excel(name = "申请图片")
    private String applyImage;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date authTime;

    /**
     * 审核状态 null待审核，1审核通过，2审核拒绝
     */
    @Excel(name = "审核状态 null待审核，1审核通过，2审核拒绝")
    private Integer authStatus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    private SamsProject samsProject;
    private SamsPost samsPost;
    private SamsEmployee samsEmployee;
    private SysDept sysDept;
    private String name;
    private String phone;
}
